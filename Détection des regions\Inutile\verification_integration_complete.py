#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vérification complète de l'intégration du conseiller poker
Vérifie que tous les éléments sont bien pris en compte
"""

import sys
import os

def verifier_integration_bouton_dealer():
    """Vérifie que le système de bouton dealer est bien intégré"""
    print("🔘 VÉRIFICATION DU SYSTÈME DE BOUTON DEALER")
    print("-" * 50)
    
    try:
        # Vérifier que detector_gui.py contient les nouvelles méthodes
        with open("detector_gui.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("move_dealer_next", "✅ Méthode move_dealer_next présente"),
            ("move_dealer_previous", "✅ Méthode move_dealer_previous présente"),
            ("update_dealer_display", "✅ Méthode update_dealer_display présente"),
            ("update_positions_display", "✅ Méthode update_positions_display présente"),
            ("_calculate_my_position_relative_to_button", "✅ Calcul de position relative présent"),
            ("dealer_position", "✅ Variable dealer_position présente"),
            ("manual_button_position", "✅ Intégration position manuelle présente"),
            ("game_analysis['button_position']", "✅ Position intégrée dans game_analysis"),
            ("game_analysis['my_position']", "✅ Ma position calculée et intégrée")
        ]
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def verifier_integration_montants():
    """Vérifie que les montants sont bien intégrés"""
    print("\n💰 VÉRIFICATION DES MONTANTS")
    print("-" * 50)
    
    try:
        # Vérifier poker_advisor_light.py
        with open("poker_advisor_light.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("call_amount", "✅ Montant call présent"),
            ("raise_amount", "✅ Montant raise présent"),
            ("bet_to_call", "✅ Montant à suivre présent"),
            ("extract_recommended_amount", "✅ Extraction montant recommandé présente"),
            ("format_recommended_amount", "✅ Formatage montant présent"),
            ("montant_call", "✅ Région montant_call gérée"),
            ("montant_relance", "✅ Région montant_relance gérée"),
            ("BB", "✅ Affichage en Big Blinds présent")
        ]
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        # Vérifier detector_gui.py pour l'affichage simplifié
        with open("detector_gui.py", "r", encoding="utf-8") as f:
            gui_content = f.read()
        
        gui_checks = [
            ("simplify_action", "✅ Simplification d'action présente"),
            ("amount_match", "✅ Extraction montants dans simplify_action"),
            ("amount_str", "✅ Formatage montants dans affichage"),
            ("BB", "✅ Affichage BB dans interface simplifiée")
        ]
        
        for check, message in gui_checks:
            if check in gui_content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def verifier_logique_avancee():
    """Vérifie que la logique avancée est préservée"""
    print("\n🧠 VÉRIFICATION DE LA LOGIQUE AVANCÉE")
    print("-" * 50)
    
    try:
        # Vérifier poker_advisor_integration.py
        if os.path.exists("poker_advisor_integration.py"):
            with open("poker_advisor_integration.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            checks = [
                ("button_position", "✅ Position bouton dans logique avancée"),
                ("my_position", "✅ Ma position dans logique avancée"),
                ("player_stacks", "✅ Jetons joueurs pris en compte"),
                ("player_bets", "✅ Mises joueurs prises en compte"),
                ("pot_total", "✅ Pot total pris en compte"),
                ("effective_stack", "✅ Tapis effectif calculé"),
                ("average_stack", "✅ Moyenne des jetons calculée")
            ]
            
            for check, message in checks:
                if check in content:
                    print(message)
                else:
                    print(f"❌ MANQUANT: {check}")
        else:
            print("⚠️ Fichier poker_advisor_integration.py non trouvé")
        
        # Vérifier poker_advisor_light.py pour la logique
        with open("poker_advisor_light.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        logic_checks = [
            ("recommend_action", "✅ Fonction de recommandation présente"),
            ("advanced_analysis", "✅ Analyse avancée présente"),
            ("monte_carlo", "✅ Simulation Monte Carlo présente"),
            ("gto_analysis", "✅ Analyse GTO présente"),
            ("range_analysis", "✅ Analyse de range présente"),
            ("unified_recommendation", "✅ Recommandation unifiée présente"),
            ("proportional_bet", "✅ Mises proportionnelles présentes")
        ]
        
        for check, message in logic_checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def verifier_interface_simplifiee():
    """Vérifie que l'interface simplifiée est bien intégrée"""
    print("\n🎯 VÉRIFICATION DE L'INTERFACE SIMPLIFIÉE")
    print("-" * 50)
    
    try:
        with open("detector_gui.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("action_display", "✅ Zone d'affichage action présente"),
            ("fold_button", "✅ Bouton FOLD présent"),
            ("call_button", "✅ Bouton CALL présent"),
            ("raise_button", "✅ Bouton RAISE présent"),
            ("dealer_display", "✅ Affichage position dealer présent"),
            ("positions_display", "✅ Affichage positions joueurs présent"),
            ("prev_dealer_button", "✅ Bouton précédent dealer présent"),
            ("next_dealer_button", "✅ Bouton suivant dealer présent"),
            ("update_action_display", "✅ Mise à jour affichage action présente"),
            ("highlight_action_button", "✅ Mise en évidence boutons présente"),
            ("update_simplified_display", "✅ Mise à jour affichage simplifié présente")
        ]
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def verifier_conservation_fonctionnalites():
    """Vérifie que toutes les fonctionnalités existantes sont conservées"""
    print("\n🔒 VÉRIFICATION DE LA CONSERVATION DES FONCTIONNALITÉS")
    print("-" * 50)
    
    try:
        with open("detector_gui.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ("advisor_text", "✅ Zone d'analyse détaillée conservée"),
            ("refresh_advisor_button", "✅ Bouton rafraîchir conservé"),
            ("correct_cards_button", "✅ Bouton correction cartes conservé"),
            ("reset_advisor_button", "✅ Bouton reset conservé"),
            ("learning_dashboard_button", "✅ Bouton tableau de bord conservé"),
            ("update_advisor_analysis", "✅ Méthode analyse conseiller conservée"),
            ("poker_advisor.analyze_detection_results", "✅ Analyse résultats conservée"),
            ("cache", "✅ Système de cache conservé"),
            ("persistent_detector", "✅ Détecteur persistant conservé")
        ]
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def main():
    """Fonction principale de vérification"""
    print("🔍 VÉRIFICATION COMPLÈTE DE L'INTÉGRATION")
    print("=" * 60)
    print("Vérification que toutes les fonctionnalités sont bien intégrées...")
    print()
    
    # Changer vers le bon répertoire
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Effectuer toutes les vérifications
    verifications = [
        verifier_integration_bouton_dealer(),
        verifier_integration_montants(),
        verifier_logique_avancee(),
        verifier_interface_simplifiee(),
        verifier_conservation_fonctionnalites()
    ]
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DE LA VÉRIFICATION")
    print("-" * 60)
    
    success_count = sum(verifications)
    total_count = len(verifications)
    
    if success_count == total_count:
        print("✅ TOUTES LES VÉRIFICATIONS RÉUSSIES !")
        print("🎯 Le conseiller poker est complètement intégré avec :")
        print("   - Interface simplifiée avec montants")
        print("   - Gestion manuelle du bouton dealer")
        print("   - Logique avancée complète préservée")
        print("   - Toutes les fonctionnalités existantes conservées")
        print()
        print("🚀 PRÊT À UTILISER :")
        print("   1. Lancez 'lancer_detector_cuda_advisor.bat'")
        print("   2. Activez le conseiller poker")
        print("   3. Utilisez les boutons de gestion du dealer")
        print("   4. Observez les recommandations avec montants")
    else:
        print(f"⚠️ {success_count}/{total_count} vérifications réussies")
        print("Certains éléments peuvent nécessiter une attention supplémentaire.")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
