#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de diagnostic pour vérifier l'état complet du système de surveillance.
"""

import os
import sys
import json
import time
from datetime import datetime

def check_files():
    """Vérifie la présence des fichiers nécessaires"""
    print("🔍 VÉRIFICATION DES FICHIERS")
    print("=" * 40)
    
    required_files = [
        "monitor_app.py",
        "detector_gui.py", 
        "show_logs.ps1",
        "lancer_detector_cuda_advisor.bat"
    ]
    
    optional_files = [
        "monitor.log",
        "test_surveillance.py",
        "test_crash_simulation.py"
    ]
    
    all_good = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MANQUANT")
            all_good = False
    
    print("\nFichiers optionnels:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"⚠️ {file} - Non trouvé")
    
    return all_good

def check_imports():
    """Vérifie que tous les modules peuvent être importés"""
    print("\n🔍 VÉRIFICATION DES IMPORTS")
    print("=" * 40)
    
    modules = [
        ("psutil", "Surveillance système"),
        ("torch", "CUDA/GPU"),
        ("cv2", "OpenCV"),
        ("numpy", "Calculs numériques")
    ]
    
    all_good = True
    
    for module, description in modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError as e:
            print(f"❌ {module} - {description} - ERREUR: {e}")
            all_good = False
    
    # Test spécifique du module de surveillance
    try:
        from monitor_app import AppMonitor
        print("✅ monitor_app.AppMonitor - Module de surveillance")
    except ImportError as e:
        print(f"❌ monitor_app.AppMonitor - ERREUR: {e}")
        all_good = False
    
    return all_good

def check_log_file():
    """Vérifie l'état du fichier de log"""
    print("\n🔍 VÉRIFICATION DU FICHIER DE LOG")
    print("=" * 40)
    
    log_file = "monitor.log"
    
    if not os.path.exists(log_file):
        print(f"⚠️ {log_file} n'existe pas encore")
        return False
    
    try:
        # Informations sur le fichier
        stat = os.stat(log_file)
        size = stat.st_size
        mtime = datetime.fromtimestamp(stat.st_mtime)
        
        print(f"✅ Fichier: {log_file}")
        print(f"📏 Taille: {size} octets")
        print(f"🕒 Dernière modification: {mtime}")
        
        # Lire les dernières lignes
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 Nombre de lignes: {len(lines)}")
        
        if lines:
            print("\n📋 Dernières entrées:")
            for line in lines[-3:]:
                print(f"   {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la lecture du log: {e}")
        return False

def check_system_resources():
    """Vérifie les ressources système"""
    print("\n🔍 VÉRIFICATION DES RESSOURCES SYSTÈME")
    print("=" * 40)
    
    try:
        import psutil
        
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"🖥️ CPU: {cpu_percent:.1f}%")
        
        # Mémoire
        memory = psutil.virtual_memory()
        print(f"💾 RAM: {memory.used / 1024 / 1024:.0f}MB / {memory.total / 1024 / 1024:.0f}MB ({memory.percent:.1f}%)")
        
        # GPU (si disponible)
        try:
            import torch
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_allocated(0) / 1024 / 1024
                print(f"🎮 GPU: {gpu_memory:.0f}MB alloués")
            else:
                print("🎮 GPU: CUDA non disponible")
        except ImportError:
            print("🎮 GPU: PyTorch non disponible")
        
        # Processus Python
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
            try:
                if 'python' in proc.info['name'].lower():
                    python_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"🐍 Processus Python: {len(python_processes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification des ressources: {e}")
        return False

def test_monitoring():
    """Test rapide du système de surveillance"""
    print("\n🔍 TEST DU SYSTÈME DE SURVEILLANCE")
    print("=" * 40)
    
    try:
        from monitor_app import AppMonitor
        
        monitor = AppMonitor()
        print("✅ Instance AppMonitor créée")
        
        # Test de démarrage
        monitor.start_monitoring()
        print("✅ Surveillance démarrée")
        
        # Attendre quelques secondes
        print("⏳ Collecte de données (5 secondes)...")
        time.sleep(5)
        
        # Vérifier les données
        if monitor.stats_history:
            latest = monitor.stats_history[-1]
            print(f"✅ Données collectées: CPU {latest['system_cpu']:.1f}%, RAM {latest['system_memory_mb']:.0f}MB")
        else:
            print("⚠️ Aucune donnée collectée")
        
        # Arrêter la surveillance
        monitor.stop_monitoring()
        print("✅ Surveillance arrêtée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de surveillance: {e}")
        return False

def main():
    """Fonction principale de diagnostic"""
    print("🔍 DIAGNOSTIC COMPLET DU SYSTÈME DE SURVEILLANCE")
    print("=" * 60)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = []
    
    # Tests
    results.append(("Fichiers", check_files()))
    results.append(("Imports", check_imports()))
    results.append(("Fichier de log", check_log_file()))
    results.append(("Ressources système", check_system_resources()))
    results.append(("Test de surveillance", test_monitoring()))
    
    # Résumé
    print("\n📊 RÉSUMÉ DU DIAGNOSTIC")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ RÉUSSI" if passed else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS!")
        print("✅ Le système de surveillance est opérationnel.")
    else:
        print("⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Veuillez corriger les problèmes identifiés.")
    
    print("\n👋 Diagnostic terminé")

if __name__ == "__main__":
    main()
