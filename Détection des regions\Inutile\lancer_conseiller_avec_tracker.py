#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 LANCEUR CONSEILLER POKER AVEC TRACKER INTELLIGENT
===================================================

Script pour lancer votre conseiller poker avec le tracker intelligent intégré.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from poker_tracker_intelligent import IntelligentTracker
from poker_advisor_tracker_integration import PokerAdvisorWithTracker

class TrackerGUI:
    """Interface graphique pour le tracker intelligent"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Poker Advisor avec Tracker Intelligent")
        self.root.geometry("800x600")
        self.root.configure(bg='#2b2b2b')
        
        # Style sombre
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TLabel', background='#2b2b2b', foreground='white')
        style.configure('TButton', background='#404040', foreground='white')
        style.configure('TFrame', background='#2b2b2b')
        
        self.tracker = None
        self.advisor = None
        self.current_players = []
        
        self.setup_ui()
        self.init_tracker()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        
        # Titre
        title_label = tk.Label(
            self.root, 
            text="🎯 Poker Advisor avec Tracker Intelligent",
            font=("Arial", 16, "bold"),
            bg='#2b2b2b',
            fg='white'
        )
        title_label.pack(pady=10)
        
        # Frame principal
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Section Status
        status_frame = ttk.LabelFrame(main_frame, text="📊 Status", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = tk.Label(
            status_frame,
            text="🔄 Initialisation...",
            bg='#2b2b2b',
            fg='yellow',
            font=("Arial", 10)
        )
        self.status_label.pack()
        
        # Section Base de données
        db_frame = ttk.LabelFrame(main_frame, text="💾 Base de données", padding=10)
        db_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.db_info_label = tk.Label(
            db_frame,
            text="Joueurs: 0 | Dernière mise à jour: Jamais",
            bg='#2b2b2b',
            fg='white',
            font=("Arial", 9)
        )
        self.db_info_label.pack()
        
        update_btn = tk.Button(
            db_frame,
            text="🔄 Mettre à jour la base",
            command=self.update_database,
            bg='#404040',
            fg='white',
            font=("Arial", 9)
        )
        update_btn.pack(pady=5)
        
        # Section Table actuelle
        table_frame = ttk.LabelFrame(main_frame, text="🎲 Table actuelle", padding=10)
        table_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Entry pour les joueurs
        players_label = tk.Label(
            table_frame,
            text="Joueurs (séparés par des virgules):",
            bg='#2b2b2b',
            fg='white',
            font=("Arial", 9)
        )
        players_label.pack(anchor=tk.W)
        
        self.players_entry = tk.Entry(
            table_frame,
            width=60,
            font=("Arial", 9),
            bg='#404040',
            fg='white'
        )
        self.players_entry.pack(fill=tk.X, pady=5)
        
        set_table_btn = tk.Button(
            table_frame,
            text="🎯 Analyser la table",
            command=self.analyze_table,
            bg='#404040',
            fg='white',
            font=("Arial", 9)
        )
        set_table_btn.pack(pady=5)
        
        # Section Analyse de table
        analysis_frame = ttk.LabelFrame(main_frame, text="🧠 Analyse de table", padding=10)
        analysis_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Zone de texte pour l'analyse
        self.analysis_text = tk.Text(
            analysis_frame,
            height=15,
            bg='#1e1e1e',
            fg='white',
            font=("Consolas", 9),
            wrap=tk.WORD
        )
        
        # Scrollbar
        scrollbar = tk.Scrollbar(analysis_frame, orient=tk.VERTICAL, command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=scrollbar.set)
        
        self.analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Boutons d'action
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X)
        
        export_btn = tk.Button(
            action_frame,
            text="📄 Exporter rapport",
            command=self.export_report,
            bg='#404040',
            fg='white',
            font=("Arial", 9)
        )
        export_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        clear_btn = tk.Button(
            action_frame,
            text="🗑️ Effacer",
            command=self.clear_analysis,
            bg='#404040',
            fg='white',
            font=("Arial", 9)
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Bouton pour lancer le conseiller principal
        launch_btn = tk.Button(
            action_frame,
            text="🚀 Lancer Conseiller Principal",
            command=self.launch_main_advisor,
            bg='#0066cc',
            fg='white',
            font=("Arial", 10, "bold")
        )
        launch_btn.pack(side=tk.RIGHT)
    
    def init_tracker(self):
        """Initialise le tracker en arrière-plan"""
        def init_thread():
            try:
                self.update_status("🔄 Initialisation du tracker...")
                self.advisor = PokerAdvisorWithTracker()
                self.tracker = self.advisor.tracker
                
                # Mettre à jour l'affichage
                self.update_db_info()
                self.update_status("✅ Tracker prêt")
                
                self.log_message("🎯 Tracker Poker Intelligent initialisé avec succès!")
                self.log_message(f"📁 Dossier historique: {self.tracker.history_path}")
                self.log_message(f"💾 Base de données: {self.tracker.database.db_path}")
                
            except Exception as e:
                self.update_status(f"❌ Erreur: {e}")
                self.log_message(f"❌ Erreur d'initialisation: {e}")
        
        threading.Thread(target=init_thread, daemon=True).start()
    
    def update_database(self):
        """Met à jour la base de données"""
        def update_thread():
            try:
                self.update_status("🔄 Mise à jour de la base...")
                players = self.advisor.update_player_database()
                self.update_db_info()
                self.update_status("✅ Base mise à jour")
                self.log_message(f"✅ Base de données mise à jour: {len(players)} joueurs")
            except Exception as e:
                self.update_status(f"❌ Erreur: {e}")
                self.log_message(f"❌ Erreur mise à jour: {e}")
        
        threading.Thread(target=update_thread, daemon=True).start()
    
    def analyze_table(self):
        """Analyse la table actuelle"""
        players_text = self.players_entry.get().strip()
        if not players_text:
            messagebox.showwarning("Attention", "Veuillez entrer les noms des joueurs")
            return
        
        # Parser les noms des joueurs
        self.current_players = [name.strip() for name in players_text.split(',') if name.strip()]
        
        if not self.current_players:
            messagebox.showwarning("Attention", "Aucun joueur valide détecté")
            return
        
        try:
            self.update_status("🔄 Analyse de la table...")
            
            # Définir la table
            self.advisor.set_current_table(self.current_players)
            
            # Obtenir l'analyse
            table_analysis = self.tracker.get_table_intelligence(self.current_players)
            
            # Afficher l'analyse
            self.clear_analysis()
            self.log_message(f"🎲 ANALYSE DE TABLE")
            self.log_message("=" * 50)
            self.log_message(f"👥 Joueurs: {', '.join(self.current_players)}")
            self.log_message(f"🎯 Style de table: {table_analysis['table_style']}")
            
            if table_analysis['recommendations']:
                self.log_message(f"\n💡 RECOMMANDATIONS GÉNÉRALES:")
                for rec in table_analysis['recommendations']:
                    self.log_message(f"  • {rec}")
            
            if table_analysis['alerts']:
                self.log_message(f"\n⚠️ ALERTES:")
                for alert in table_analysis['alerts']:
                    self.log_message(f"  • {alert}")
            
            self.log_message(f"\n👥 PROFILS DES JOUEURS:")
            self.log_message("-" * 50)
            
            for player_name in self.current_players:
                profile = self.tracker.get_player_profile(player_name)
                if profile:
                    stats = profile['stats']
                    style = profile['style']
                    
                    self.log_message(f"\n🎭 {player_name}:")
                    self.log_message(f"  📊 Style: {style['type']} (confiance: {style['confidence']*100:.0f}%)")
                    self.log_message(f"  📈 Stats: VPIP {stats.vpip:.1f}% | PFR {stats.pfr:.1f}% | AF {stats.aggression_factor:.1f}")
                    self.log_message(f"  🎯 Mains observées: {stats.hands_played}")
                    
                    if profile['recommendations']:
                        self.log_message(f"  💡 Contre lui:")
                        for rec in profile['recommendations'][:3]:
                            self.log_message(f"    • {rec}")
                else:
                    self.log_message(f"\n🔍 {player_name}: Joueur inconnu - observez attentivement")
            
            self.update_status("✅ Analyse terminée")
            
        except Exception as e:
            self.update_status(f"❌ Erreur: {e}")
            self.log_message(f"❌ Erreur d'analyse: {e}")
    
    def export_report(self):
        """Exporte le rapport de session"""
        if not self.current_players:
            messagebox.showwarning("Attention", "Aucune table analysée")
            return
        
        try:
            filename = f"rapport_session_{int(time.time())}.txt"
            report = self.advisor.export_session_report(filename)
            messagebox.showinfo("Succès", f"Rapport exporté: {filename}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur d'export: {e}")
    
    def launch_main_advisor(self):
        """Lance le conseiller principal"""
        try:
            # Essayer de lancer votre conseiller principal
            import subprocess
            subprocess.Popen([sys.executable, "poker_advisor_light.py"])
            self.log_message("🚀 Conseiller principal lancé")
        except Exception as e:
            self.log_message(f"⚠️ Impossible de lancer le conseiller principal: {e}")
            messagebox.showinfo("Info", "Lancez manuellement votre conseiller poker principal")
    
    def update_status(self, message):
        """Met à jour le status"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def update_db_info(self):
        """Met à jour les infos de la base"""
        if self.tracker:
            try:
                players = self.tracker.database.get_all_players()
                count = len(players)
                last_update = self.tracker.last_scan.strftime("%H:%M:%S") if self.tracker.last_scan else "Jamais"
                self.db_info_label.config(text=f"Joueurs: {count} | Dernière mise à jour: {last_update}")
            except:
                self.db_info_label.config(text="Joueurs: ? | Dernière mise à jour: Erreur")
    
    def log_message(self, message):
        """Ajoute un message au log"""
        self.analysis_text.insert(tk.END, message + "\n")
        self.analysis_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_analysis(self):
        """Efface l'analyse"""
        self.analysis_text.delete(1.0, tk.END)
    
    def run(self):
        """Lance l'interface"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    print("🎯 Lancement du Conseiller Poker avec Tracker Intelligent")
    print("=" * 60)
    
    try:
        app = TrackerGUI()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
