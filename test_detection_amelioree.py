#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour les améliorations de détection des petites cartes et du J
"""

import sys
import os
import cv2
import numpy as np
import time

# Ajouter le répertoire de détection au path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Détection des regions'))

try:
    from detector import Detector
    print("✅ Module detector importé avec succès")
except ImportError as e:
    print(f"❌ Erreur d'importation du module detector: {e}")
    sys.exit(1)

def create_test_j_image(size=(50, 30), background_color=(0, 0, 0), text_color=(255, 255, 255)):
    """Crée une image de test avec un J pour tester la détection"""
    img = np.full((size[1], size[0], 3), background_color, dtype=np.uint8)

    # Dessiner un J simple
    h, w = size[1], size[0]

    # Barre horizontale en haut
    cv2.line(img, (w//4, h//4), (3*w//4, h//4), text_color, 2)

    # Ligne verticale à droite
    cv2.line(img, (3*w//4, h//4), (3*w//4, 3*h//4), text_color, 2)

    # Courbure en bas à gauche
    cv2.ellipse(img, (w//2, 3*h//4), (w//4, h//8), 0, 0, 180, text_color, 2)

    return img

def create_test_small_card_image(card_value="A", size=(40, 25)):
    """Crée une petite image de carte pour tester la détection"""
    img = np.full((size[1], size[0], 3), (255, 255, 255), dtype=np.uint8)

    # Ajouter du texte
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    color = (0, 0, 0)
    thickness = 1

    # Calculer la position du texte
    text_size = cv2.getTextSize(card_value, font, font_scale, thickness)[0]
    x = (size[0] - text_size[0]) // 2
    y = (size[1] + text_size[1]) // 2

    cv2.putText(img, card_value, (x, y), font, font_scale, color, thickness)

    return img

def test_j_detection():
    """Test spécifique pour la détection du J"""
    print("\n🔍 === TEST DE DÉTECTION DU J ===")

    # Initialiser le détecteur
    detector = Detector()

    # Test avec différentes tailles et contrastes
    test_cases = [
        {"size": (30, 20), "bg": (0, 0, 0), "fg": (255, 255, 255), "desc": "Petit J blanc sur noir"},
        {"size": (50, 30), "bg": (0, 0, 0), "fg": (255, 255, 255), "desc": "J moyen blanc sur noir"},
        {"size": (30, 20), "bg": (255, 255, 255), "fg": (0, 0, 0), "desc": "Petit J noir sur blanc"},
        {"size": (25, 15), "bg": (128, 128, 128), "fg": (255, 255, 255), "desc": "Très petit J blanc sur gris"},
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['desc']}")

        # Créer l'image de test
        test_img = create_test_j_image(
            size=test_case["size"],
            background_color=test_case["bg"],
            text_color=test_case["fg"]
        )

        # Sauvegarder l'image de test pour inspection visuelle
        cv2.imwrite(f"test_j_{i}.png", test_img)
        print(f"   💾 Image sauvée: test_j_{i}.png")

        # Tester la détection
        start_time = time.time()

        # Test avec la méthode simple (cartes en main)
        result_simple = detector.detect_text_simple(test_img, is_hand_card=True)

        # Test avec la détection J améliorée
        j_detected = detector.enhanced_j_detection_improved(test_img, is_hand_card=True)

        detection_time = time.time() - start_time

        print(f"   🔍 Résultat OCR simple: '{result_simple}'")
        print(f"   🎯 Détection J améliorée: {j_detected}")
        print(f"   ⏱️ Temps de détection: {detection_time:.3f}s")

        # Évaluation
        if result_simple == 'J' or j_detected:
            print(f"   ✅ SUCCÈS: J détecté correctement")
        else:
            print(f"   ❌ ÉCHEC: J non détecté")

def test_small_cards_detection():
    """Test spécifique pour la détection des petites cartes"""
    print("\n🔍 === TEST DE DÉTECTION DES PETITES CARTES ===")

    # Initialiser le détecteur
    detector = Detector()

    # Test avec différentes valeurs de cartes
    card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
    sizes = [(30, 20), (40, 25), (25, 15)]  # Différentes tailles de petites cartes

    total_tests = 0
    successful_tests = 0

    for size in sizes:
        print(f"\n📏 Test avec taille {size[0]}x{size[1]} pixels:")

        for card_value in card_values:
            total_tests += 1

            # Créer l'image de test
            test_img = create_test_small_card_image(card_value, size)

            # Sauvegarder pour inspection
            filename = f"test_card_{card_value}_{size[0]}x{size[1]}.png"
            cv2.imwrite(filename, test_img)

            # Tester la détection
            start_time = time.time()
            result = detector.detect_text_simple(test_img, is_hand_card=True)
            detection_time = time.time() - start_time

            # Vérifier si la détection est correcte
            is_correct = (result == card_value or
                         detector.correct_card_value(result) == card_value)

            if is_correct:
                successful_tests += 1
                status = "✅"
            else:
                status = "❌"

            print(f"   {status} {card_value}: '{result}' ({detection_time:.3f}s)")

    # Statistiques finales
    success_rate = (successful_tests / total_tests) * 100
    print(f"\n📊 RÉSULTATS FINAUX:")
    print(f"   Tests réussis: {successful_tests}/{total_tests}")
    print(f"   Taux de réussite: {success_rate:.1f}%")

    return success_rate

def test_performance():
    """Test de performance des améliorations"""
    print("\n⚡ === TEST DE PERFORMANCE ===")

    detector = Detector()

    # Créer une image de test
    test_img = create_test_small_card_image("J", (30, 20))

    # Test de performance
    num_iterations = 10

    print(f"🔄 Test de performance sur {num_iterations} itérations...")

    start_time = time.time()
    for i in range(num_iterations):
        result = detector.detect_text_simple(test_img, is_hand_card=True)
    total_time = time.time() - start_time

    avg_time = total_time / num_iterations

    print(f"   ⏱️ Temps total: {total_time:.3f}s")
    print(f"   ⏱️ Temps moyen par détection: {avg_time:.3f}s")
    print(f"   🚀 Détections par seconde: {1/avg_time:.1f}")

def main():
    """Fonction principale de test"""
    print("🚀 === TESTS D'AMÉLIORATION DE DÉTECTION ===")
    print("Ce script teste les améliorations pour la détection des petites cartes et du J")

    try:
        # Test de détection du J
        test_j_detection()

        # Test de détection des petites cartes
        success_rate = test_small_cards_detection()

        # Test de performance
        test_performance()

        print(f"\n🎯 === RÉSUMÉ FINAL ===")
        print(f"✅ Tests terminés avec succès")
        print(f"📈 Taux de réussite global: {success_rate:.1f}%")

        if success_rate >= 80:
            print("🏆 EXCELLENT: Les améliorations fonctionnent très bien!")
        elif success_rate >= 60:
            print("👍 BON: Les améliorations sont efficaces")
        else:
            print("⚠️ MOYEN: Des améliorations supplémentaires sont nécessaires")

    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
