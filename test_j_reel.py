#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test spécifique pour l'image de J fournie par l'utilisateur
"""

import sys
import os
import cv2
import numpy as np
import time
import base64
from io import BytesIO
from PIL import Image

# Ajouter le répertoire de détection au path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Détection des regions'))

try:
    from detector import Detector
    print("✅ Module detector importé avec succès")
except ImportError as e:
    print(f"❌ Erreur d'importation du module detector: {e}")
    sys.exit(1)

def create_j_image_from_description():
    """Crée une image de J basée sur la description (vert avec J blanc)"""
    # Créer une image verte avec un J blanc
    img = np.full((50, 50, 3), (0, 128, 0), dtype=np.uint8)  # Fond vert
    
    # Dessiner un J blanc
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 2.0
    color = (255, 255, 255)  # Blanc
    thickness = 3
    
    # Calculer la position du texte
    text = "J"
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    x = (50 - text_size[0]) // 2
    y = (50 + text_size[1]) // 2
    
    cv2.putText(img, text, (x, y), font, font_scale, color, thickness)
    
    return img

def test_j_detection_complete(image, test_name="Test J"):
    """Test complet de détection du J avec toutes les méthodes"""
    print(f"\n🔍 === {test_name.upper()} ===")
    
    # Initialiser le détecteur
    try:
        detector = Detector()
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur d'initialisation du détecteur: {e}")
        return False
    
    # Afficher les informations sur l'image
    height, width = image.shape[:2]
    print(f"📏 Taille de l'image: {width}x{height} pixels")
    
    # Sauvegarder l'image pour inspection
    filename = f"{test_name.lower().replace(' ', '_')}.png"
    cv2.imwrite(filename, image)
    print(f"💾 Image sauvée: {filename}")
    
    # Test 1: Méthode simple (standard)
    print("\n🔍 Test 1: Détection simple (méthode standard)")
    start_time = time.time()
    try:
        result_simple = detector.detect_text_simple(image, is_hand_card=True)
        time_simple = time.time() - start_time
        print(f"   Résultat: '{result_simple}' (temps: {time_simple:.3f}s)")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        result_simple = ""
        time_simple = 0
    
    # Test 2: Détection J améliorée
    print("\n🔍 Test 2: Détection J améliorée")
    start_time = time.time()
    try:
        j_detected = detector.enhanced_j_detection_improved(image, is_hand_card=True)
        time_j = time.time() - start_time
        print(f"   Résultat: {j_detected} (temps: {time_j:.3f}s)")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        j_detected = False
        time_j = 0
    
    # Test 3: Méthode multi-OCR (si disponible)
    print("\n🔍 Test 3: Détection multi-OCR")
    start_time = time.time()
    try:
        result_multi = detector.detect_text_multi_ocr(image, is_hand_card=True, fast_mode=False)
        time_multi = time.time() - start_time
        print(f"   Résultat: '{result_multi}' (temps: {time_multi:.3f}s)")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        result_multi = ""
        time_multi = 0
    
    # Test 4: Correction de carte
    print("\n🔍 Test 4: Correction de carte")
    if result_simple:
        corrected = detector.correct_card_value(result_simple)
        print(f"   '{result_simple}' → '{corrected}'")
    else:
        corrected = ""
        print("   Aucun texte à corriger")
    
    # Analyse des couleurs
    print("\n🎨 Test 5: Analyse des couleurs")
    try:
        colors = detector.detect_colors(image)
        print(f"   Couleurs détectées: {colors}")
    except Exception as e:
        print(f"   ❌ Erreur détection couleurs: {e}")
        colors = []
    
    # Résumé
    print(f"\n📊 === RÉSUMÉ {test_name.upper()} ===")
    success = False
    
    if result_simple == 'J':
        print("✅ Méthode simple: SUCCÈS")
        success = True
    else:
        print(f"❌ Méthode simple: ÉCHEC ('{result_simple}')")
    
    if j_detected:
        print("✅ Détection J améliorée: SUCCÈS")
        success = True
    else:
        print("❌ Détection J améliorée: ÉCHEC")
    
    if result_multi == 'J':
        print("✅ Multi-OCR: SUCCÈS")
        success = True
    else:
        print(f"❌ Multi-OCR: ÉCHEC ('{result_multi}')")
    
    if corrected == 'J':
        print("✅ Correction: SUCCÈS")
        success = True
    else:
        print(f"❌ Correction: ÉCHEC ('{corrected}')")
    
    print(f"🎯 Résultat global: {'SUCCÈS' if success else 'ÉCHEC'}")
    print(f"⏱️ Temps total: {time_simple + time_j + time_multi:.3f}s")
    
    return success

def test_variations_j():
    """Test avec différentes variations de l'image J"""
    print("\n🔄 === TESTS DE VARIATIONS ===")
    
    # Image originale (simulée)
    original_j = create_j_image_from_description()
    success1 = test_j_detection_complete(original_j, "J Original")
    
    # Version redimensionnée (plus petite)
    small_j = cv2.resize(original_j, (25, 25), interpolation=cv2.INTER_AREA)
    success2 = test_j_detection_complete(small_j, "J Petit")
    
    # Version avec bruit
    noisy_j = original_j.copy()
    noise = np.random.randint(0, 50, noisy_j.shape, dtype=np.uint8)
    noisy_j = cv2.add(noisy_j, noise)
    success3 = test_j_detection_complete(noisy_j, "J Bruité")
    
    # Version avec contraste réduit
    low_contrast_j = cv2.convertScaleAbs(original_j, alpha=0.5, beta=50)
    success4 = test_j_detection_complete(low_contrast_j, "J Faible Contraste")
    
    # Statistiques finales
    total_tests = 4
    successful_tests = sum([success1, success2, success3, success4])
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"\n📈 === STATISTIQUES FINALES ===")
    print(f"Tests réussis: {successful_tests}/{total_tests}")
    print(f"Taux de réussite: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("🏆 EXCELLENT: Les améliorations fonctionnent très bien!")
    elif success_rate >= 50:
        print("👍 BON: Les améliorations sont efficaces")
    else:
        print("⚠️ MOYEN: Des améliorations supplémentaires sont nécessaires")
    
    return success_rate

def main():
    """Fonction principale"""
    print("🚀 === TEST SPÉCIFIQUE POUR IMAGE J ===")
    print("Ce script teste la détection du J avec l'image fournie")
    
    try:
        # Test avec l'image simulée
        success_rate = test_variations_j()
        
        print(f"\n🎯 === CONCLUSION ===")
        print(f"Les améliorations de détection du J ont un taux de réussite de {success_rate:.1f}%")
        
        if success_rate >= 75:
            print("✅ Les améliorations sont très efficaces!")
        elif success_rate >= 50:
            print("⚠️ Les améliorations sont partiellement efficaces")
        else:
            print("❌ Les améliorations nécessitent des ajustements")
            
        print("\n💡 CONSEILS:")
        print("- Vérifiez que la calibration des régions est précise")
        print("- Assurez-vous que l'éclairage est stable")
        print("- Testez avec différents facteurs de zoom")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
