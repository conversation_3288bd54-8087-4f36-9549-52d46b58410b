#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test avec l'image J fournie par l'utilisateur
"""

import sys
import os
import cv2
import numpy as np
import time

# Ajouter le répertoire de détection au path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Détection des regions'))

try:
    from detector import Detector
    print("✅ Module detector importé avec succès")
except ImportError as e:
    print(f"❌ Erreur d'importation du module detector: {e}")
    sys.exit(1)

def create_j_from_user_description():
    """Crée une image de J basée sur la description de l'utilisateur (vert avec J blanc)"""
    # Créer une image verte (fond vert comme une carte de trèfle)
    img = np.full((60, 60, 3), (34, 139, 34), dtype=np.uint8)  # Vert forêt
    
    # Dessiner un J blanc stylisé comme sur une vraie carte
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 2.5
    color = (255, 255, 255)  # Blanc
    thickness = 4
    
    # Position du J
    text = "J"
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    x = (60 - text_size[0]) // 2
    y = (60 + text_size[1]) // 2
    
    cv2.putText(img, text, (x, y), font, font_scale, color, thickness)
    
    return img

def test_image_complete(image, test_name="Test Image"):
    """Test complet avec toutes les méthodes de détection"""
    print(f"\n🔍 === {test_name.upper()} ===")
    
    # Initialiser le détecteur
    try:
        detector = Detector()
        print("✅ Détecteur initialisé")
    except Exception as e:
        print(f"❌ Erreur d'initialisation: {e}")
        return False
    
    # Informations sur l'image
    height, width = image.shape[:2]
    print(f"📏 Taille: {width}x{height} pixels")
    
    # Sauvegarder l'image
    filename = f"{test_name.lower().replace(' ', '_')}.png"
    cv2.imwrite(filename, image)
    print(f"💾 Image sauvée: {filename}")
    
    results = {}
    
    # Test 1: Méthode simple optimisée
    print("\n🔍 Test 1: Méthode simple optimisée")
    start_time = time.time()
    try:
        result_simple = detector.detect_text_simple(image, is_hand_card=True)
        time_simple = time.time() - start_time
        results['simple'] = result_simple
        print(f"   Résultat: '{result_simple}' (temps: {time_simple:.3f}s)")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        results['simple'] = ""
        time_simple = 0
    
    # Test 2: Détection J améliorée
    print("\n🔍 Test 2: Détection J améliorée")
    start_time = time.time()
    try:
        j_detected = detector.enhanced_j_detection_improved(image, is_hand_card=True)
        time_j = time.time() - start_time
        results['j_improved'] = j_detected
        print(f"   Résultat: {j_detected} (temps: {time_j:.3f}s)")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        results['j_improved'] = False
        time_j = 0
    
    # Test 3: Multi-OCR
    print("\n🔍 Test 3: Multi-OCR")
    start_time = time.time()
    try:
        result_multi = detector.detect_text_multi_ocr(image, is_hand_card=True, fast_mode=False)
        time_multi = time.time() - start_time
        results['multi'] = result_multi
        print(f"   Résultat: '{result_multi}' (temps: {time_multi:.3f}s)")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        results['multi'] = ""
        time_multi = 0
    
    # Test 4: Correction
    print("\n🔍 Test 4: Correction de carte")
    if results['simple']:
        corrected = detector.correct_card_value(results['simple'])
        results['corrected'] = corrected
        print(f"   '{results['simple']}' → '{corrected}'")
    else:
        results['corrected'] = ""
        print("   Aucun texte à corriger")
    
    # Test 5: Couleurs
    print("\n🎨 Test 5: Détection des couleurs")
    try:
        colors = detector.detect_colors(image)
        results['colors'] = colors
        print(f"   Couleurs: {colors}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        results['colors'] = []
    
    # Résumé
    print(f"\n📊 === RÉSUMÉ {test_name.upper()} ===")
    success = False
    
    # Vérifier les succès
    if results['simple'] == 'J':
        print("✅ Méthode simple: SUCCÈS")
        success = True
    else:
        print(f"❌ Méthode simple: ÉCHEC ('{results['simple']}')")
    
    if results['j_improved']:
        print("✅ Détection J améliorée: SUCCÈS")
        success = True
    else:
        print("❌ Détection J améliorée: ÉCHEC")
    
    if results['multi'] == 'J':
        print("✅ Multi-OCR: SUCCÈS")
        success = True
    else:
        print(f"❌ Multi-OCR: ÉCHEC ('{results['multi']}')")
    
    if results['corrected'] == 'J':
        print("✅ Correction: SUCCÈS")
        success = True
    else:
        print(f"❌ Correction: ÉCHEC ('{results['corrected']}')")
    
    print(f"🎯 Résultat global: {'SUCCÈS' if success else 'ÉCHEC'}")
    print(f"⏱️ Temps total: {time_simple + time_j + time_multi:.3f}s")
    
    return success

def test_variations():
    """Test avec différentes variations de l'image J"""
    print("🔄 === TESTS DE VARIATIONS DE L'IMAGE J ===")
    
    # Image de base (simulée d'après votre description)
    base_image = create_j_from_user_description()
    success1 = test_image_complete(base_image, "J Base")
    
    # Version plus petite (comme une carte en main)
    small_image = cv2.resize(base_image, (30, 30), interpolation=cv2.INTER_AREA)
    success2 = test_image_complete(small_image, "J Petit")
    
    # Version très petite (cas extrême)
    tiny_image = cv2.resize(base_image, (20, 20), interpolation=cv2.INTER_AREA)
    success3 = test_image_complete(tiny_image, "J Très Petit")
    
    # Version avec bruit
    noisy_image = base_image.copy()
    noise = np.random.randint(0, 30, noisy_image.shape, dtype=np.uint8)
    noisy_image = cv2.add(noisy_image, noise)
    success4 = test_image_complete(noisy_image, "J Bruité")
    
    # Version floue
    blurred_image = cv2.GaussianBlur(base_image, (3, 3), 1.0)
    success5 = test_image_complete(blurred_image, "J Flou")
    
    # Statistiques
    total_tests = 5
    successful_tests = sum([success1, success2, success3, success4, success5])
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"\n📈 === STATISTIQUES FINALES ===")
    print(f"Tests réussis: {successful_tests}/{total_tests}")
    print(f"Taux de réussite: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🏆 EXCELLENT: Les améliorations sont très efficaces!")
    elif success_rate >= 60:
        print("👍 BON: Les améliorations fonctionnent bien")
    else:
        print("⚠️ MOYEN: Des ajustements peuvent être nécessaires")
    
    return success_rate

def main():
    """Fonction principale"""
    print("🚀 === TEST AVEC IMAGE J UTILISATEUR ===")
    print("Test basé sur votre image: J blanc sur fond vert")
    
    try:
        success_rate = test_variations()
        
        print(f"\n🎯 === CONCLUSION ===")
        print(f"Taux de réussite global: {success_rate:.1f}%")
        
        print(f"\n💡 === RECOMMANDATIONS ===")
        if success_rate >= 80:
            print("✅ Les améliorations sont très efficaces pour votre type d'image")
            print("✅ La détection du J devrait bien fonctionner dans votre application")
        elif success_rate >= 60:
            print("⚠️ Les améliorations fonctionnent mais peuvent être optimisées")
            print("💡 Conseil: Vérifiez la calibration des régions de cartes en main")
        else:
            print("❌ Des ajustements supplémentaires sont nécessaires")
            print("💡 Conseil: Contactez le support pour des optimisations spécifiques")
        
        print(f"\n📋 === PROCHAINES ÉTAPES ===")
        print("1. Intégrez ces améliorations dans votre application principale")
        print("2. Testez avec de vraies captures d'écran de votre table de poker")
        print("3. Ajustez la calibration si nécessaire")
        print("4. Surveillez les performances en conditions réelles")
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
