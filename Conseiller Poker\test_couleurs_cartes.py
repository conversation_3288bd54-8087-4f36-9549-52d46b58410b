#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'affichage des couleurs des cartes dans le conseiller
============================================================

Ce script teste l'affichage des cartes avec leurs couleurs respectives :
- Cœur (♥) : <PERSON>
<PERSON> <PERSON><PERSON> (♠) : <PERSON><PERSON> clair
- Trèfle (♣) : Vert
- <PERSON><PERSON> (♦) : Bleu

Auteur: Augment Agent
Date: 2024
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QTextEdit, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QTextCursor, QTextCharFormat, QColor

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importer les constantes de couleurs
try:
    from poker_advisor_app import CARD_COLORS
except ImportError:
    # Définir les couleurs si l'import échoue
    CARD_COLORS = {
        "Cœur": QColor("#FF4444"),      # Rouge pour cœur
        "Pique": QColor("#CCCCCC"),     # Gris clair pour pique
        "Trèfle": QColor("#44FF44"),    # Vert pour trèfle
        "Carreau": QColor("#4444FF"),   # Bleu pour carreau
        "Valeur": QColor("#FFFFFF")     # Blanc pour les lettres et chiffres
    }

class TestCouleursCartes(QMainWindow):
    """Fenêtre de test pour l'affichage des couleurs des cartes"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Couleurs des Cartes - Conseiller Poker")
        self.setGeometry(100, 100, 800, 600)
        
        # Fond noir comme dans l'application principale
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
            QTextEdit {
                background-color: #1e1e1e;
                color: white;
                border: 1px solid #555;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 14px;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Zone d'affichage
        self.text_display = QTextEdit()
        self.text_display.setReadOnly(True)
        layout.addWidget(self.text_display)
        
        # Tester l'affichage des couleurs
        self.test_couleurs_cartes()
    
    def format_cards(self, cursor, cards_text):
        """
        Formate les cartes avec leurs couleurs respectives
        (Copie de la fonction du conseiller principal)
        """
        if not cards_text or cards_text == "Non détecté":
            cursor.insertText(cards_text)
            return

        # Diviser le texte en cartes individuelles
        cards = cards_text.split()

        for i, card in enumerate(cards):
            if len(card) >= 2:
                # Extraire le symbole de couleur (dernier caractère)
                suit = card[-1]

                # Créer le format pour cette carte ENTIÈRE (valeur + symbole)
                card_format = QTextCharFormat()

                # Appliquer la couleur appropriée à TOUTE LA CARTE selon sa couleur
                if suit == "♥":  # Cœur - ROUGE
                    card_format.setForeground(CARD_COLORS["Cœur"])
                elif suit == "♠":  # Pique - GRIS CLAIR
                    card_format.setForeground(CARD_COLORS["Pique"])
                elif suit == "♣":  # Trèfle - VERT
                    card_format.setForeground(CARD_COLORS["Trèfle"])
                elif suit == "♦":  # Carreau - BLEU
                    card_format.setForeground(CARD_COLORS["Carreau"])
                else:
                    card_format.setForeground(CARD_COLORS["Valeur"])

                # Insérer la carte ENTIÈRE avec la couleur appropriée
                cursor.insertText(card, card_format)

                # Ajouter un espace entre les cartes
                if i < len(cards) - 1:
                    cursor.insertText(" ")
            else:
                # Si le format n'est pas reconnu, afficher tel quel en blanc
                value_format = QTextCharFormat()
                value_format.setForeground(CARD_COLORS["Valeur"])
                cursor.insertText(card, value_format)
                if i < len(cards) - 1:
                    cursor.insertText(" ")
    
    def test_couleurs_cartes(self):
        """Teste l'affichage des couleurs pour différentes cartes"""
        cursor = self.text_display.textCursor()
        
        # Titre
        title_format = QTextCharFormat()
        title_format.setForeground(QColor("#FFFFFF"))
        cursor.insertText("🃏 TEST AFFICHAGE COULEURS DES CARTES\n", title_format)
        cursor.insertText("="*50 + "\n\n", title_format)
        
        # Test 1: Cartes individuelles de chaque couleur
        cursor.insertText("1. Test cartes individuelles :\n", title_format)
        
        cursor.insertText("   Cœur (rouge) : ", title_format)
        self.format_cards(cursor, "A♥")
        cursor.insertText("\n", title_format)
        
        cursor.insertText("   Pique (gris) : ", title_format)
        self.format_cards(cursor, "K♠")
        cursor.insertText("\n", title_format)
        
        cursor.insertText("   Trèfle (vert) : ", title_format)
        self.format_cards(cursor, "Q♣")
        cursor.insertText("\n", title_format)
        
        cursor.insertText("   Carreau (bleu) : ", title_format)
        self.format_cards(cursor, "J♦")
        cursor.insertText("\n\n", title_format)
        
        # Test 2: Main complète
        cursor.insertText("2. Test main complète :\n", title_format)
        cursor.insertText("   Main : ", title_format)
        self.format_cards(cursor, "A♥ K♠")
        cursor.insertText("\n\n", title_format)
        
        # Test 3: Board complet
        cursor.insertText("3. Test board complet :\n", title_format)
        cursor.insertText("   Board : ", title_format)
        self.format_cards(cursor, "Q♣ J♦ 10♥ 9♠ 8♣")
        cursor.insertText("\n\n", title_format)
        
        # Test 4: Toutes les valeurs pour chaque couleur
        cursor.insertText("4. Test toutes les valeurs :\n", title_format)
        
        # Cœur
        cursor.insertText("   Cœur : ", title_format)
        self.format_cards(cursor, "A♥ K♥ Q♥ J♥ 10♥ 9♥ 8♥ 7♥ 6♥ 5♥ 4♥ 3♥ 2♥")
        cursor.insertText("\n", title_format)
        
        # Pique
        cursor.insertText("   Pique : ", title_format)
        self.format_cards(cursor, "A♠ K♠ Q♠ J♠ 10♠ 9♠ 8♠ 7♠ 6♠ 5♠ 4♠ 3♠ 2♠")
        cursor.insertText("\n", title_format)
        
        # Trèfle
        cursor.insertText("   Trèfle : ", title_format)
        self.format_cards(cursor, "A♣ K♣ Q♣ J♣ 10♣ 9♣ 8♣ 7♣ 6♣ 5♣ 4♣ 3♣ 2♣")
        cursor.insertText("\n", title_format)
        
        # Carreau
        cursor.insertText("   Carreau : ", title_format)
        self.format_cards(cursor, "A♦ K♦ Q♦ J♦ 10♦ 9♦ 8♦ 7♦ 6♦ 5♦ 4♦ 3♦ 2♦")
        cursor.insertText("\n\n", title_format)
        
        # Test 5: Exemple de situation de jeu
        cursor.insertText("5. Exemple situation de jeu :\n", title_format)
        cursor.insertText("   Main : ", title_format)
        self.format_cards(cursor, "A♠ A♥")
        cursor.insertText("   (Paire d'As)\n", title_format)
        
        cursor.insertText("   Board : ", title_format)
        self.format_cards(cursor, "A♣ K♦ Q♠")
        cursor.insertText("   (Flop)\n", title_format)
        
        cursor.insertText("   → Brelan d'As avec ", title_format)
        self.format_cards(cursor, "A♠ A♥ A♣")
        cursor.insertText("\n\n", title_format)
        
        # Légende des couleurs
        cursor.insertText("6. Légende des couleurs :\n", title_format)
        
        # Cœur
        cursor.insertText("   ♥ Cœur : ", title_format)
        heart_format = QTextCharFormat()
        heart_format.setForeground(CARD_COLORS["Cœur"])
        cursor.insertText("ROUGE", heart_format)
        cursor.insertText("\n", title_format)
        
        # Pique
        cursor.insertText("   ♠ Pique : ", title_format)
        spade_format = QTextCharFormat()
        spade_format.setForeground(CARD_COLORS["Pique"])
        cursor.insertText("GRIS CLAIR", spade_format)
        cursor.insertText("\n", title_format)
        
        # Trèfle
        cursor.insertText("   ♣ Trèfle : ", title_format)
        club_format = QTextCharFormat()
        club_format.setForeground(CARD_COLORS["Trèfle"])
        cursor.insertText("VERT", club_format)
        cursor.insertText("\n", title_format)
        
        # Carreau
        cursor.insertText("   ♦ Carreau : ", title_format)
        diamond_format = QTextCharFormat()
        diamond_format.setForeground(CARD_COLORS["Carreau"])
        cursor.insertText("BLEU", diamond_format)
        cursor.insertText("\n\n", title_format)
        
        cursor.insertText("✅ Test terminé ! Les couleurs devraient être visibles ci-dessus.\n", title_format)

def main():
    """Fonction principale pour tester l'affichage des couleurs"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Créer et afficher la fenêtre de test
    test_window = TestCouleursCartes()
    test_window.show()
    
    print("🃏 Fenêtre de test des couleurs ouverte")
    print("Vérifiez que les cartes s'affichent dans les bonnes couleurs :")
    print("- Cœur (♥) : Rouge")
    print("- Pique (♠) : Gris clair")
    print("- Trèfle (♣) : Vert")
    print("- Carreau (♦) : Bleu")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
