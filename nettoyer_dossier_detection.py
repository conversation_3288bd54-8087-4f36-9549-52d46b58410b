#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nettoyeur du dossier Détection des regions
Déplace les fichiers inutiles vers le dossier Inutile
"""

import os
import shutil
import sys
from pathlib import Path

# Changer vers le bon répertoire
os.chdir(r"C:\Users\<USER>\PokerAdvisor\Détection des regions")

def identifier_fichiers_essentiels():
    """Identifie les fichiers essentiels à conserver"""
    
    # Fichiers ESSENTIELS pour l'application
    fichiers_essentiels = {
        # Scripts principaux
        'detector.py',
        'detector_gui.py', 
        'multi_ocr_detector.py',
        'poker_advisor_light.py',
        'poker_advisor_integration.py',
        'poker_logic_advanced.py',
        
        # Scripts de lancement
        'lancer_detector_cuda_advisor.bat',
        'realtime_advisor_gui.py',
        
        # Modules avancés
        'monte_carlo_simulator.py',
        'range_analyzer.py',
        'gto_solver.py',
        'variance_calculator.py',
        'session_analyzer.py',
        'integrate_advanced_logic.py',
        
        # Intégrations
        'pt4_integration.py',
        'poker_advisor_tracker_integration.py',
        'poker_tracker_intelligent.py',
        
        # Configuration
        'calibration_config.json',
        
        # Système d'apprentissage
        'learning_system.py',
        'auto_player_detection.py',
        
        # Surveillance et monitoring
        'monitor_app.py',
        'debug_crash.py',
        
        # Dossiers essentiels
        'learning_data',
        'export',
        'screenshots',
        '__pycache__'
    }
    
    return fichiers_essentiels

def identifier_fichiers_inutiles():
    """Identifie les fichiers à déplacer vers Inutile"""
    
    # Fichiers INUTILES (tests, debug, anciens, doublons)
    fichiers_inutiles = {
        # Tests et diagnostics
        'test_*.py',
        'diagnostic_*.py',
        'examiner_*.py',
        'corriger_*.py',
        'correctif_*.py',
        'verification_*.py',
        'analyse_*.py',
        
        # Debug et images de debug
        'debug_*.jpg',
        'debug_*.png',
        'debug_*.log',
        'resume_*.jpg',
        
        # Anciens fichiers et backups
        'detector_old.py',
        'detector_backup.py',
        'detector_broken.py',
        'detector_broken2.py',
        'detector.py.backup',
        'detector_gui_sauvegarde.py',
        'detector_cuda_optimized.py',
        
        # Fichiers temporaires et logs
        'log.txt',
        'monitor.log',
        'multi_ocr_log.txt',
        'detection_debug.jpg',
        'detection_results.json',
        
        # Scripts de test spécifiques
        'j_shape_analyzer.py',
        'demo_comparaison_trackers.py',
        'learning_dashboard.py',
        'debug_detection.py',
        'debug_quinte.py',
        'debug_mises_temps_reel.py',
        'fix_crash_detection.py',
        'force_cuda_config.py',
        'install_*.py',
        'lancer_*.py',  # Sauf lancer_detector_cuda_advisor.bat
        
        # Dossiers de backup et archives
        'backup_*',
        'archive',
        'test_images',
        'test_integration_data',
        'test_learning_data',
        
        # Fichiers markdown de documentation
        '*.md',
        
        # Scripts PowerShell
        '*.ps1',
        
        # Images de test
        'test_*.jpg',
        'test_*.png'
    }
    
    return fichiers_inutiles

def creer_dossier_inutile():
    """Crée le dossier Inutile s'il n'existe pas"""
    dossier_inutile = Path("Inutile")
    if not dossier_inutile.exists():
        dossier_inutile.mkdir()
        print(f"✅ Dossier créé: {dossier_inutile}")
    return dossier_inutile

def deplacer_fichiers_inutiles():
    """Déplace les fichiers inutiles vers le dossier Inutile"""
    print("🧹 NETTOYAGE DU DOSSIER DÉTECTION DES RÉGIONS")
    print("=" * 60)
    
    dossier_courant = Path(".")
    dossier_inutile = creer_dossier_inutile()
    
    fichiers_essentiels = identifier_fichiers_essentiels()
    patterns_inutiles = identifier_fichiers_inutiles()
    
    fichiers_deplaces = 0
    fichiers_conserves = 0
    erreurs = 0
    
    print("\n🔍 ANALYSE DES FICHIERS...")
    
    # Parcourir tous les fichiers du dossier courant
    for item in dossier_courant.iterdir():
        if item.name == "Inutile":
            continue  # Ignorer le dossier Inutile lui-même
        
        # Vérifier si le fichier est essentiel
        if item.name in fichiers_essentiels:
            print(f"✅ CONSERVÉ: {item.name}")
            fichiers_conserves += 1
            continue
        
        # Vérifier si le fichier correspond aux patterns inutiles
        est_inutile = False
        for pattern in patterns_inutiles:
            if pattern.endswith('*'):
                # Pattern avec wildcard
                prefix = pattern[:-1]
                if item.name.startswith(prefix):
                    est_inutile = True
                    break
            elif pattern.startswith('*'):
                # Pattern avec wildcard au début
                suffix = pattern[1:]
                if item.name.endswith(suffix):
                    est_inutile = True
                    break
            elif item.name == pattern:
                # Correspondance exacte
                est_inutile = True
                break
        
        if est_inutile:
            try:
                destination = dossier_inutile / item.name
                
                # Si le fichier existe déjà dans Inutile, ajouter un suffixe
                if destination.exists():
                    base_name = item.stem
                    extension = item.suffix
                    counter = 1
                    while destination.exists():
                        destination = dossier_inutile / f"{base_name}_{counter}{extension}"
                        counter += 1
                
                if item.is_file():
                    shutil.move(str(item), str(destination))
                    print(f"📦 DÉPLACÉ: {item.name} → Inutile/")
                elif item.is_dir():
                    shutil.move(str(item), str(destination))
                    print(f"📁 DOSSIER DÉPLACÉ: {item.name} → Inutile/")
                
                fichiers_deplaces += 1
                
            except Exception as e:
                print(f"❌ ERREUR: {item.name} - {e}")
                erreurs += 1
        else:
            # Fichier non identifié - demander confirmation
            print(f"❓ NON IDENTIFIÉ: {item.name}")
            fichiers_conserves += 1
    
    return fichiers_deplaces, fichiers_conserves, erreurs

def analyser_avant_nettoyage():
    """Analyse le contenu avant nettoyage"""
    print("📊 ANALYSE AVANT NETTOYAGE")
    print("-" * 40)
    
    dossier_courant = Path(".")
    fichiers_essentiels = identifier_fichiers_essentiels()
    patterns_inutiles = identifier_fichiers_inutiles()
    
    total_fichiers = 0
    essentiels_count = 0
    inutiles_count = 0
    non_identifies = 0
    
    for item in dossier_courant.iterdir():
        if item.name == "Inutile":
            continue
        
        total_fichiers += 1
        
        if item.name in fichiers_essentiels:
            essentiels_count += 1
        else:
            # Vérifier patterns inutiles
            est_inutile = False
            for pattern in patterns_inutiles:
                if pattern.endswith('*'):
                    if item.name.startswith(pattern[:-1]):
                        est_inutile = True
                        break
                elif pattern.startswith('*'):
                    if item.name.endswith(pattern[1:]):
                        est_inutile = True
                        break
                elif item.name == pattern:
                    est_inutile = True
                    break
            
            if est_inutile:
                inutiles_count += 1
            else:
                non_identifies += 1
    
    print(f"📁 Total fichiers/dossiers: {total_fichiers}")
    print(f"✅ Essentiels: {essentiels_count}")
    print(f"📦 À déplacer: {inutiles_count}")
    print(f"❓ Non identifiés: {non_identifies}")
    
    return total_fichiers, essentiels_count, inutiles_count, non_identifies

def lister_fichiers_essentiels_conserves():
    """Liste les fichiers essentiels qui seront conservés"""
    print("\n✅ FICHIERS ESSENTIELS CONSERVÉS:")
    print("-" * 40)
    
    fichiers_essentiels = identifier_fichiers_essentiels()
    dossier_courant = Path(".")
    
    for item in dossier_courant.iterdir():
        if item.name in fichiers_essentiels and item.exists():
            if item.is_file():
                print(f"   📄 {item.name}")
            elif item.is_dir():
                print(f"   📁 {item.name}/")

def main():
    """Fonction principale"""
    print("🧹 NETTOYEUR DOSSIER DÉTECTION DES RÉGIONS")
    print("=" * 70)
    print("Déplace les fichiers inutiles vers le dossier Inutile")
    print()
    
    # Vérifier qu'on est dans le bon dossier
    if not Path("detector.py").exists():
        print("❌ ERREUR: Ce script doit être exécuté dans le dossier 'Détection des regions'")
        return False
    
    # Analyser avant nettoyage
    total, essentiels, inutiles, non_id = analyser_avant_nettoyage()
    
    # Lister les fichiers essentiels
    lister_fichiers_essentiels_conserves()
    
    # Demander confirmation
    print(f"\n🤔 CONFIRMATION:")
    print(f"   📦 {inutiles} fichiers/dossiers seront déplacés vers Inutile/")
    print(f"   ✅ {essentiels} fichiers essentiels seront conservés")
    print(f"   ❓ {non_id} fichiers non identifiés seront conservés par sécurité")
    
    reponse = input("\n   Continuer le nettoyage ? (o/N): ").lower().strip()
    
    if reponse not in ['o', 'oui', 'y', 'yes']:
        print("❌ Nettoyage annulé")
        return False
    
    # Effectuer le nettoyage
    print("\n🧹 NETTOYAGE EN COURS...")
    deplaces, conserves, erreurs = deplacer_fichiers_inutiles()
    
    # Résumé final
    print(f"\n" + "=" * 70)
    print("📊 RÉSUMÉ DU NETTOYAGE")
    print("-" * 70)
    print(f"📦 Fichiers déplacés: {deplaces}")
    print(f"✅ Fichiers conservés: {conserves}")
    print(f"❌ Erreurs: {erreurs}")
    
    if erreurs == 0:
        print("\n🎉 NETTOYAGE TERMINÉ AVEC SUCCÈS!")
        print("   Le dossier est maintenant plus propre et organisé")
        print("   Les fichiers inutiles sont dans le dossier Inutile/")
    else:
        print(f"\n⚠️ NETTOYAGE TERMINÉ AVEC {erreurs} ERREUR(S)")
    
    return erreurs == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
