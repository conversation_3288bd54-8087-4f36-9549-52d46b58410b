#!/usr/bin/env python3
"""
Installation simple et automatique de Tesseract OCR
"""

import os
import sys
import subprocess
import urllib.request
import tempfile
import shutil

def download_and_install_tesseract():
    """Télécharge et installe Tesseract OCR automatiquement"""
    print("🔽 Téléchargement de Tesseract OCR...")
    
    # URL de l'installateur Tesseract
    tesseract_url = "https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.1.20230401.exe"
    
    # Créer un dossier temporaire
    temp_dir = tempfile.mkdtemp()
    installer_path = os.path.join(temp_dir, "tesseract-installer.exe")
    
    try:
        # Télécharger l'installateur
        urllib.request.urlretrieve(tesseract_url, installer_path)
        print("✅ Téléchargement terminé")
        
        # Installer en mode silencieux
        print("📦 Installation de Tesseract OCR en cours...")
        
        # Installation silencieuse avec paramètres par défaut
        install_cmd = f'"{installer_path}" /S /D="C:\\Program Files\\Tesseract-OCR"'
        result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Tesseract OCR installé avec succès")
            
            # Ajouter au PATH
            tesseract_path = "C:\\Program Files\\Tesseract-OCR"
            current_path = os.environ.get("PATH", "")
            if tesseract_path not in current_path:
                os.environ["PATH"] = f"{tesseract_path};{current_path}"
                print("✅ Tesseract ajouté au PATH")
            
            return True
        else:
            print(f"❌ Erreur lors de l'installation: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        # Nettoyer
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def test_tesseract():
    """Teste si Tesseract fonctionne"""
    try:
        result = subprocess.run("tesseract --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Tesseract fonctionne correctement")
            print(f"Version: {result.stdout.split()[1]}")
            return True
        else:
            print("❌ Tesseract ne fonctionne pas")
            return False
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def main():
    print("🚀 Installation automatique de Tesseract OCR")
    print("=" * 50)
    
    # Vérifier si Tesseract est déjà installé
    if test_tesseract():
        print("✅ Tesseract est déjà installé et fonctionnel")
        return
    
    # Installer Tesseract
    if download_and_install_tesseract():
        # Tester l'installation
        if test_tesseract():
            print("\n🎉 Installation réussie !")
            print("Tesseract OCR est maintenant disponible pour votre application.")
        else:
            print("\n⚠️ Installation terminée mais Tesseract ne répond pas.")
            print("Vous devrez peut-être redémarrer votre terminal.")
    else:
        print("\n❌ Échec de l'installation")

if __name__ == "__main__":
    main()
