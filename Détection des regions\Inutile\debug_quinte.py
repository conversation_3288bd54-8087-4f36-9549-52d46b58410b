#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de debug pour analyser en détail la détection des quintes.
"""

import sys
from PyQt5.QtWidgets import QApplication
from detector_gui import DetectorGUI

def debug_straight_detection():
    """Debug détaillé de la détection des quintes"""
    print("🔍 DEBUG DÉTAILLÉ DE LA DÉTECTION DES QUINTES")
    print("=" * 60)
    
    # Créer une application Qt
    app = QApplication(sys.argv)
    detector_gui = DetectorGUI()
    
    # Test 3: A, 7, K, 3, 2 - Pas de quinte possible
    print("\n🧪 DEBUG Test 3: A, 7, K, 3, 2")
    hand_values = ["A", "7"]
    hand_suits = ["Cœur", "Pique"]
    board_values = ["K", "3", "2"]
    board_suits = ["Trèfle", "Carreau", "Cœur"]
    
    all_values = hand_values + board_values
    numeric_values = [detector_gui.card_to_numeric(v) for v in all_values]
    unique_values = sorted(set(numeric_values), reverse=True)
    
    print(f"   Toutes les valeurs: {all_values}")
    print(f"   Valeurs numériques: {numeric_values}")
    print(f"   Valeurs uniques triées: {unique_values}")
    
    # Vérifier manuellement les quintes possibles
    print("\n   Vérification manuelle des quintes:")
    
    # Quinte A-K-Q-J-10 : A(14), K(13) présents, manque Q(12), J(11), 10(10)
    akqj10 = [14, 13, 12, 11, 10]
    present_akqj10 = [v for v in unique_values if v in akqj10]
    print(f"   Quinte A-K-Q-J-10: {akqj10} -> présents: {present_akqj10} ({len(present_akqj10)}/5)")
    
    # Quinte blanche A-5-4-3-2 : A(14), 3(3), 2(2) présents, manque 5(5), 4(4)
    wheel = [14, 5, 4, 3, 2]
    present_wheel = [v for v in unique_values if v in wheel]
    print(f"   Quinte blanche A-5-4-3-2: {wheel} -> présents: {present_wheel} ({len(present_wheel)}/5)")
    
    # Autres quintes possibles
    for high_card in range(13, 4, -1):  # K à 5
        straight_vals = list(range(high_card, high_card-5, -1))
        present_vals = [v for v in unique_values if v in straight_vals]
        if len(present_vals) >= 2:
            print(f"   Quinte à {detector_gui.numeric_to_card(high_card)}: {straight_vals} -> présents: {present_vals} ({len(present_vals)}/5)")
    
    straight_possible, straight_desc = detector_gui.check_straight_possibility(
        hand_values, hand_suits, board_values, board_suits
    )
    print(f"\n   Résultat: {straight_possible} - {straight_desc}")
    
    # Test 4: A, 2, 5, 4, 3 - Quinte blanche complète
    print("\n🧪 DEBUG Test 4: A, 2, 5, 4, 3")
    hand_values = ["A", "2"]
    hand_suits = ["Cœur", "Pique"]
    board_values = ["5", "4", "3"]
    board_suits = ["Trèfle", "Carreau", "Cœur"]
    
    all_values = hand_values + board_values
    numeric_values = [detector_gui.card_to_numeric(v) for v in all_values]
    unique_values = sorted(set(numeric_values), reverse=True)
    
    print(f"   Toutes les valeurs: {all_values}")
    print(f"   Valeurs numériques: {numeric_values}")
    print(f"   Valeurs uniques triées: {unique_values}")
    
    # Vérifier la quinte blanche
    wheel = [14, 5, 4, 3, 2]
    present_wheel = [v for v in unique_values if v in wheel]
    print(f"   Quinte blanche A-5-4-3-2: {wheel} -> présents: {present_wheel} ({len(present_wheel)}/5)")
    
    if len(present_wheel) == 5:
        print("   ✅ Quinte blanche complète détectée!")
    else:
        print(f"   ❌ Quinte blanche incomplète: manque {[v for v in wheel if v not in present_wheel]}")
    
    straight_possible, straight_desc = detector_gui.check_straight_possibility(
        hand_values, hand_suits, board_values, board_suits
    )
    print(f"\n   Résultat: {straight_possible} - {straight_desc}")
    
    # Test 5: 9, 8, 7, 6, J, Q, 5 - Quinte 9-8-7-6-5 complète
    print("\n🧪 DEBUG Test 5: 9, 8, 7, 6, J, Q, 5")
    hand_values = ["9", "8"]
    hand_suits = ["Cœur", "Pique"]
    board_values = ["7", "6", "J", "Q", "5"]
    board_suits = ["Trèfle", "Carreau", "Cœur", "Pique", "Trèfle"]
    
    all_values = hand_values + board_values
    numeric_values = [detector_gui.card_to_numeric(v) for v in all_values]
    unique_values = sorted(set(numeric_values), reverse=True)
    
    print(f"   Toutes les valeurs: {all_values}")
    print(f"   Valeurs numériques: {numeric_values}")
    print(f"   Valeurs uniques triées: {unique_values}")
    
    # Vérifier les quintes possibles
    straight_98765 = [9, 8, 7, 6, 5]
    present_98765 = [v for v in unique_values if v in straight_98765]
    print(f"   Quinte 9-8-7-6-5: {straight_98765} -> présents: {present_98765} ({len(present_98765)}/5)")
    
    if len(present_98765) == 5:
        print("   ✅ Quinte 9-8-7-6-5 complète détectée!")
    else:
        print(f"   ❌ Quinte 9-8-7-6-5 incomplète: manque {[v for v in straight_98765 if v not in present_98765]}")
    
    straight_possible, straight_desc = detector_gui.check_straight_possibility(
        hand_values, hand_suits, board_values, board_suits
    )
    print(f"\n   Résultat: {straight_possible} - {straight_desc}")
    
    app.quit()
    print("\n👋 Debug terminé")

if __name__ == "__main__":
    debug_straight_detection()
