#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour vérifier si le fichier de configuration existe et afficher son contenu
"""

import os
import json
import sys

def check_config():
    """Vérifie si le fichier de configuration existe et affiche son contenu"""
    # Chemin du fichier de configuration de calibrage
    calibration_config = os.path.join('C:', 'Users', 'Tomz', 'PokerAdvisor', 'Calibration', 'config', 'poker_advisor_config.json')
    
    # Vérifier si le fichier existe
    if os.path.exists(calibration_config):
        print(f"✅ Le fichier de configuration de calibrage existe: {calibration_config}")
        
        # Afficher les informations sur le fichier
        file_size = os.path.getsize(calibration_config) / 1024  # Taille en Ko
        file_mtime = os.path.getmtime(calibration_config)  # Date de modification
        
        print(f"   - Taille: {file_size:.2f} Ko")
        print(f"   - Dernière modification: {os.path.getmtime(calibration_config)}")
        
        # Essayer de charger le fichier
        try:
            with open(calibration_config, 'r') as f:
                config = json.load(f)
                
                # Afficher les sections du fichier
                print("\nContenu du fichier de configuration:")
                print("-----------------------------------")
                
                # Afficher les sections principales
                for section, content in config.items():
                    if isinstance(content, dict):
                        print(f"Section '{section}': {len(content)} éléments")
                    else:
                        print(f"Section '{section}': {content}")
                
                # Afficher les régions disponibles
                regions = config.get('all_regions', config.get('roi', {}))
                if regions:
                    print(f"\nRégions disponibles ({len(regions)}):")
                    for i, (name, coords) in enumerate(regions.items(), 1):
                        if i <= 5:  # Limiter l'affichage aux 5 premières régions
                            print(f"   - {name}: {coords}")
                        elif i == 6:
                            print(f"   - ... et {len(regions) - 5} autres régions")
                            break
                else:
                    print("\nAucune région définie dans la configuration")
                
        except json.JSONDecodeError as e:
            print(f"❌ Erreur lors du décodage du fichier JSON: {e}")
        except Exception as e:
            print(f"❌ Erreur lors de la lecture du fichier: {e}")
    else:
        print(f"❌ Le fichier de configuration de calibrage n'existe pas: {calibration_config}")
        
        # Vérifier si le dossier existe
        config_dir = os.path.dirname(calibration_config)
        if os.path.exists(config_dir):
            print(f"   - Le dossier existe: {config_dir}")
            print(f"   - Contenu du dossier:")
            for file in os.listdir(config_dir):
                print(f"     * {file}")
        else:
            print(f"   - Le dossier n'existe pas: {config_dir}")
            
        # Vérifier le fichier de configuration local
        local_config = os.path.join('config', 'poker_advisor_config.json')
        if os.path.exists(local_config):
            print(f"\n✅ Le fichier de configuration local existe: {local_config}")
        else:
            print(f"\n❌ Le fichier de configuration local n'existe pas: {local_config}")

if __name__ == "__main__":
    check_config()
    
    print("\nAppuyez sur Entrée pour quitter...")
    input()
