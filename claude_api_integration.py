#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Intégration API Claude pour le projet Poker
Utilisation économique et optimisée
"""

import anthropic
import base64
import os
from typing import Optional
import json

class ClaudeAPIManager:
    def __init__(self, api_key: str = None):
        """
        Initialise le gestionnaire API Claude
        
        Args:
            api_key: Clé API Claude (ou via variable d'environnement ANTHROPIC_API_KEY)
        """
        self.api_key = api_key or os.getenv('ANTHROPIC_API_KEY')
        if not self.api_key:
            raise ValueError("Clé API Claude requise. Définissez ANTHROPIC_API_KEY ou passez api_key")
        
        self.client = anthropic.Anthropic(api_key=self.api_key)
        self.usage_tracker = {
            'total_input_tokens': 0,
            'total_output_tokens': 0,
            'total_cost': 0.0,
            'requests_count': 0
        }
    
    def estimate_cost(self, input_tokens: int, output_tokens: int, model: str = "claude-3-5-sonnet-20241022") -> float:
        """Estime le coût d'une requête"""
        pricing = {
            "claude-3-5-sonnet-20241022": {"input": 3.0, "output": 15.0},  # $ per 1M tokens
            "claude-3-opus-20240229": {"input": 15.0, "output": 75.0},
            "claude-3-haiku-20240307": {"input": 0.25, "output": 1.25}
        }
        
        rates = pricing.get(model, pricing["claude-3-5-sonnet-20241022"])
        cost = (input_tokens * rates["input"] + output_tokens * rates["output"]) / 1_000_000
        return cost
    
    def analyze_code(self, code: str, description: str = "", model: str = "claude-3-5-sonnet-20241022") -> dict:
        """
        Analyse du code avec estimation de coût
        
        Args:
            code: Code à analyser
            description: Description du problème
            model: Modèle Claude à utiliser
            
        Returns:
            dict avec 'response', 'cost', 'tokens'
        """
        prompt = f"""Tu es un expert en programmation Python spécialisé dans les applications de poker et vision par ordinateur.

{f'Contexte: {description}' if description else ''}

Analyse ce code Python et fournis:
1. 🐛 Bugs potentiels identifiés
2. ⚡ Optimisations possibles
3. 🎯 Améliorations recommandées
4. 💡 Bonnes pratiques à appliquer

Code à analyser:
```python
{code}
```

Réponds de manière concise et pratique en français."""

        try:
            # Estimation préalable (approximative)
            estimated_input_tokens = len(prompt) // 4  # Approximation
            estimated_output_tokens = 500  # Estimation conservative
            estimated_cost = self.estimate_cost(estimated_input_tokens, estimated_output_tokens, model)
            
            print(f"💰 Coût estimé: ~{estimated_cost:.4f}$ ({estimated_input_tokens + estimated_output_tokens} tokens)")
            
            # Confirmation utilisateur pour coûts élevés
            if estimated_cost > 0.05:  # Plus de 5 centimes
                confirm = input(f"⚠️ Coût estimé: {estimated_cost:.4f}$. Continuer ? (o/N): ")
                if confirm.lower() not in ['o', 'oui']:
                    return {"error": "Annulé par l'utilisateur", "cost": 0, "tokens": 0}
            
            # Appel API
            message = self.client.messages.create(
                model=model,
                max_tokens=1000,
                messages=[{"role": "user", "content": prompt}]
            )
            
            # Calcul du coût réel
            input_tokens = message.usage.input_tokens
            output_tokens = message.usage.output_tokens
            real_cost = self.estimate_cost(input_tokens, output_tokens, model)
            
            # Mise à jour du tracker
            self.usage_tracker['total_input_tokens'] += input_tokens
            self.usage_tracker['total_output_tokens'] += output_tokens
            self.usage_tracker['total_cost'] += real_cost
            self.usage_tracker['requests_count'] += 1
            
            return {
                "response": message.content[0].text,
                "cost": real_cost,
                "tokens": {"input": input_tokens, "output": output_tokens},
                "model": model
            }
            
        except Exception as e:
            return {"error": str(e), "cost": 0, "tokens": 0}
    
    def analyze_poker_screenshot(self, image_path: str, question: str = "", model: str = "claude-3-5-sonnet-20241022") -> dict:
        """
        Analyse d'une capture d'écran de poker
        
        Args:
            image_path: Chemin vers l'image
            question: Question spécifique sur l'image
            model: Modèle Claude à utiliser
        """
        try:
            # Lire et encoder l'image
            with open(image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Déterminer le type MIME
            if image_path.lower().endswith('.png'):
                media_type = "image/png"
            elif image_path.lower().endswith(('.jpg', '.jpeg')):
                media_type = "image/jpeg"
            else:
                return {"error": "Format d'image non supporté", "cost": 0, "tokens": 0}
            
            prompt = f"""Tu es un expert en poker. Analyse cette capture d'écran de table de poker et fournis:

1. 🃏 Cartes communes visibles
2. 💰 Informations sur les mises et le pot
3. 👥 Positions et stacks des joueurs
4. 🎯 Situation de jeu (preflop/flop/turn/river)
5. 💡 Analyse stratégique si applicable

{f'Question spécifique: {question}' if question else ''}

Réponds en français de manière détaillée et structurée."""

            # Estimation de coût (images coûtent plus cher)
            estimated_cost = 0.10  # Estimation pour image + analyse
            print(f"💰 Coût estimé pour analyse d'image: ~{estimated_cost:.2f}$")
            
            confirm = input(f"⚠️ Analyser l'image coûtera ~{estimated_cost:.2f}$. Continuer ? (o/N): ")
            if confirm.lower() not in ['o', 'oui']:
                return {"error": "Annulé par l'utilisateur", "cost": 0, "tokens": 0}
            
            # Appel API avec image
            message = self.client.messages.create(
                model=model,
                max_tokens=1500,
                messages=[{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": media_type,
                                "data": image_data
                            }
                        }
                    ]
                }]
            )
            
            # Calcul du coût réel
            input_tokens = message.usage.input_tokens
            output_tokens = message.usage.output_tokens
            real_cost = self.estimate_cost(input_tokens, output_tokens, model)
            
            # Mise à jour du tracker
            self.usage_tracker['total_input_tokens'] += input_tokens
            self.usage_tracker['total_output_tokens'] += output_tokens
            self.usage_tracker['total_cost'] += real_cost
            self.usage_tracker['requests_count'] += 1
            
            return {
                "response": message.content[0].text,
                "cost": real_cost,
                "tokens": {"input": input_tokens, "output": output_tokens},
                "model": model
            }
            
        except Exception as e:
            return {"error": str(e), "cost": 0, "tokens": 0}
    
    def get_usage_report(self) -> str:
        """Génère un rapport d'utilisation et de coûts"""
        tracker = self.usage_tracker
        
        report = f"""
📊 RAPPORT D'UTILISATION API CLAUDE
{'='*50}

📈 Statistiques:
   Requêtes totales: {tracker['requests_count']}
   Tokens input: {tracker['total_input_tokens']:,}
   Tokens output: {tracker['total_output_tokens']:,}
   Tokens total: {tracker['total_input_tokens'] + tracker['total_output_tokens']:,}

💰 Coûts:
   Coût total: {tracker['total_cost']:.4f}$
   Coût moyen/requête: {tracker['total_cost']/max(tracker['requests_count'], 1):.4f}$
   
📊 Projections mensuelles (si usage constant):
   ~{tracker['total_cost'] * 30:.2f}$ par mois
   ~{(tracker['total_input_tokens'] + tracker['total_output_tokens']) * 30:,} tokens/mois
"""
        return report
    
    def save_usage_log(self, filename: str = "claude_api_usage.json"):
        """Sauvegarde les statistiques d'utilisation"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.usage_tracker, f, indent=2)
            return f"✅ Statistiques sauvées dans {filename}"
        except Exception as e:
            return f"❌ Erreur sauvegarde: {e}"

def main():
    """Interface de test pour l'API Claude"""
    print("🤖 GESTIONNAIRE API CLAUDE POUR POKER")
    print("=" * 50)
    
    # Vérifier la clé API
    api_key = os.getenv('ANTHROPIC_API_KEY')
    if not api_key:
        print("❌ Clé API manquante!")
        print("💡 Définissez la variable d'environnement ANTHROPIC_API_KEY")
        print("   Ou obtenez une clé sur: https://console.anthropic.com/")
        return
    
    try:
        claude = ClaudeAPIManager(api_key)
        print("✅ API Claude initialisée")
        
        while True:
            print("\n📋 OPTIONS:")
            print("1. 🔍 Analyser du code")
            print("2. 🖼️ Analyser une capture d'écran poker")
            print("3. 📊 Voir le rapport d'utilisation")
            print("4. 💾 Sauvegarder les statistiques")
            print("5. 🚪 Quitter")
            
            choice = input("\n🎯 Votre choix: ").strip()
            
            if choice == "1":
                print("\n📝 Collez votre code (terminez par une ligne vide):")
                code_lines = []
                while True:
                    line = input()
                    if line.strip() == "":
                        break
                    code_lines.append(line)
                
                code = "\n".join(code_lines)
                description = input("📋 Description (optionnel): ")
                
                print("\n🔍 Analyse en cours...")
                result = claude.analyze_code(code, description)
                
                if "error" in result:
                    print(f"❌ Erreur: {result['error']}")
                else:
                    print(f"\n🤖 Réponse:\n{result['response']}")
                    print(f"\n💰 Coût: {result['cost']:.4f}$ ({result['tokens']['input']+result['tokens']['output']} tokens)")
            
            elif choice == "2":
                image_path = input("📁 Chemin vers l'image: ").strip()
                if not os.path.exists(image_path):
                    print("❌ Fichier introuvable")
                    continue
                
                question = input("❓ Question sur l'image (optionnel): ")
                
                print("\n🔍 Analyse en cours...")
                result = claude.analyze_poker_screenshot(image_path, question)
                
                if "error" in result:
                    print(f"❌ Erreur: {result['error']}")
                else:
                    print(f"\n🤖 Réponse:\n{result['response']}")
                    print(f"\n💰 Coût: {result['cost']:.4f}$ ({result['tokens']['input']+result['tokens']['output']} tokens)")
            
            elif choice == "3":
                print(claude.get_usage_report())
            
            elif choice == "4":
                result = claude.save_usage_log()
                print(result)
            
            elif choice == "5":
                print("👋 Au revoir !")
                break
            
            else:
                print("❌ Choix invalide")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    main()
