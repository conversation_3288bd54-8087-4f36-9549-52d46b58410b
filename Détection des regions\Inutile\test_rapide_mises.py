#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide des mises après recalibration
"""

import subprocess
import sys

def test_rapide_mises():
    """Test rapide des mises"""
    print("🧪 TEST RAPIDE - MISES APRÈS RECALIBRATION")
    print("=" * 50)
    
    try:
        # Test diagnostic
        print("1. Lancement diagnostic...")
        result = subprocess.run([sys.executable, "diagnostic_mises_jaunes.py"], 
                              capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            output = result.stdout
            
            # Compter les mises détectées
            mises_detectees = output.count("✅ MISE DÉTECTÉE:")
            mises_totales = 7
            
            print(f"📊 Résultat: {mises_detectees}/{mises_totales} mises détectées")
            
            if mises_detectees >= 3:
                print("✅ SUCCÈS - Amélioration significative!")
            elif mises_detectees >= 1:
                print("⚠️ PROGRÈS - Quelques mises détectées")
            else:
                print("❌ ÉCHEC - Aucune mise détectée")
            
            return mises_detectees >= 1
        else:
            print("❌ Erreur lors du test")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    test_rapide_mises()
