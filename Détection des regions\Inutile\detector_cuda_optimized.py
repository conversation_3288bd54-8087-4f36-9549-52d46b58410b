#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Détecteur optimisé CUDA pour l'application poker
Utilise EasyOCR avec accélération GPU pour des performances maximales
"""

import os
import sys
import time
import json
import cv2
import numpy as np
import torch

# Configuration CUDA forcée
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
os.environ['USE_CUDA'] = '1'

class CudaOptimizedDetector:
    """Détecteur optimisé utilisant EasyOCR avec CUDA"""

    def __init__(self, config_path=None, use_cuda=True):
        """Initialise le détecteur optimisé CUDA"""
        print("🔥 Initialisation du détecteur CUDA optimisé...")

        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.config_path = config_path
        self.config = self._load_config(config_path)

        # Vérifier CUDA
        if self.use_cuda:
            print(f"✅ CUDA activé: {torch.cuda.get_device_name(0)}")
            print(f"✅ VRAM disponible: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")

            # Optimisations CUDA
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            torch.cuda.empty_cache()
        else:
            print("⚠️ CUDA non disponible, utilisation du CPU")

        # Initialiser EasyOCR
        self._init_easyocr()

        # Définir les plages de couleurs HSV
        self.color_ranges = {
            'red': [
                {'lower': np.array([0, 100, 100]), 'upper': np.array([10, 255, 255])},
                {'lower': np.array([165, 100, 100]), 'upper': np.array([179, 255, 255])}
            ],
            'orange': [
                {'lower': np.array([10, 100, 100]), 'upper': np.array([25, 255, 255])}
            ],
            'green': [
                {'lower': np.array([40, 70, 70]), 'upper': np.array([80, 255, 255])}
            ],
            'blue': [
                {'lower': np.array([100, 100, 100]), 'upper': np.array([130, 255, 255])}
            ],
            'black': [
                {'lower': np.array([0, 0, 0]), 'upper': np.array([180, 50, 50])}
            ],
            'white': [
                {'lower': np.array([0, 0, 180]), 'upper': np.array([180, 30, 255])}
            ]
        }

        # Correspondance couleurs -> symboles
        self.color_to_suit = {
            'red': 'hearts',
            'green': 'clubs',
            'blue': 'diamonds',
            'black': 'spades'
        }

        print("✅ Détecteur CUDA optimisé initialisé")

    def _init_easyocr(self):
        """Initialise EasyOCR avec CUDA"""
        try:
            import easyocr

            print("🔄 Initialisation EasyOCR avec CUDA...")
            start_time = time.time()

            # Initialiser EasyOCR avec GPU
            self.ocr = easyocr.Reader(
                ['en', 'fr'],  # Langues supportées
                gpu=self.use_cuda,  # Utiliser GPU si disponible
                verbose=False,  # Pas de logs verbeux
                download_enabled=True  # Télécharger les modèles si nécessaire
            )

            init_time = time.time() - start_time
            gpu_status = "GPU" if self.use_cuda else "CPU"
            print(f"✅ EasyOCR initialisé ({gpu_status}): {init_time:.2f}s")

        except Exception as e:
            print(f"❌ Erreur initialisation EasyOCR: {e}")
            self.ocr = None

    def _load_config(self, config_path):
        """Charge la configuration depuis un fichier JSON"""
        if not config_path:
            config_path = os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json')

        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                print(f"✅ Configuration chargée: {config_path}")
                return config
        except Exception as e:
            print(f"❌ Erreur chargement config: {e}")
            return {"roi": {}}

    def extract_regions(self, image):
        """Extrait les régions d'intérêt de l'image"""
        regions = {}

        # Utiliser 'all_regions' si disponible, sinon 'roi'
        region_config = self.config.get('all_regions', self.config.get('roi', {}))

        if not region_config:
            print("⚠️ Aucune région définie")
            return regions

        for name, coords in region_config.items():
            try:
                # Récupérer les coordonnées
                if 'x' in coords and 'y' in coords:
                    x, y = coords['x'], coords['y']
                elif 'left' in coords and 'top' in coords:
                    x, y = coords['left'], coords['top']
                else:
                    continue

                width = coords.get('width', 0)
                height = coords.get('height', 0)

                # Vérifier les dimensions
                if width <= 0 or height <= 0:
                    continue

                # Vérifier les limites
                if x < 0 or y < 0 or x + width > image.shape[1] or y + height > image.shape[0]:
                    continue

                # Extraire la région
                region = image[y:y+height, x:x+width]
                regions[name] = region

            except Exception as e:
                print(f"❌ Erreur extraction région {name}: {e}")

        return regions

    def preprocess_for_ocr(self, image, is_hand_card=False):
        """Prétraite l'image pour optimiser la détection OCR"""
        try:
            if image is None or image.size == 0:
                return image

            # Convertir en niveaux de gris
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Améliorer le contraste
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)

            # Redimensionner si trop petit
            h, w = enhanced.shape
            min_size = 100 if is_hand_card else 60
            if h < min_size or w < min_size:
                scale_factor = max(min_size / h, min_size / w)
                enhanced = cv2.resize(enhanced, (0, 0), fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_CUBIC)

            # Binarisation adaptative
            binary = cv2.adaptiveThreshold(
                enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 11, 2
            )

            # Convertir en BGR pour EasyOCR
            processed = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)

            return processed

        except Exception as e:
            print(f"❌ Erreur prétraitement: {e}")
            return image

    def detect_text_fast(self, image, is_hand_card=False):
        """Détection rapide de texte avec EasyOCR"""
        try:
            if self.ocr is None:
                return ""

            # Prétraiter l'image
            processed = self.preprocess_for_ocr(image, is_hand_card)

            # Détecter avec EasyOCR
            start_time = time.time()
            results = self.ocr.readtext(processed, detail=0, paragraph=False)
            detection_time = time.time() - start_time

            # Nettoyer et combiner les résultats
            text_results = []
            for text in results:
                cleaned = text.strip().upper()
                # Accepter tous les résultats non vides pour le debug
                if cleaned:
                    text_results.append(cleaned)
                    print(f"🔍 Texte détecté: '{cleaned}'")

            final_result = ' '.join(text_results) if text_results else ""
            print(f"🔍 Résultat final: '{final_result}'")

            # Nettoyer la mémoire GPU
            if self.use_cuda:
                torch.cuda.empty_cache()

            return final_result

        except Exception as e:
            print(f"❌ Erreur détection texte: {e}")
            return ""

    def detect_colors_fast(self, image):
        """Détection rapide de couleurs"""
        try:
            # Convertir en HSV
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            color_percentages = {}
            total_pixels = image.shape[0] * image.shape[1]

            # Détecter chaque couleur
            for color_name, ranges in self.color_ranges.items():
                total_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)

                for color_range in ranges:
                    mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])
                    total_mask = cv2.bitwise_or(total_mask, mask)

                # Calculer le pourcentage
                color_pixels = cv2.countNonZero(total_mask)
                percentage = (color_pixels / total_pixels) * 100
                color_percentages[color_name] = percentage

            # Retourner la couleur dominante
            dominant_color = max(color_percentages, key=color_percentages.get)
            return dominant_color

        except Exception as e:
            print(f"❌ Erreur détection couleur: {e}")
            return "unknown"

    def process_image_direct(self, image, fast_mode=True, parallel=False):
        """Traite une image directement (sans fichier)"""
        try:
            start_time = time.time()

            # Extraire les régions
            regions = self.extract_regions(image)

            results = {}

            # Traiter chaque région
            for region_name, region_image in regions.items():
                if region_image is None or region_image.size == 0:
                    continue

                # Détecter le texte
                text = self.detect_text_fast(region_image, 'hand' in region_name.lower())

                # Détecter la couleur
                color = self.detect_colors_fast(region_image)

                # Stocker les résultats
                results[region_name] = {
                    'text': text,
                    'color': color,
                    'suit': self.color_to_suit.get(color, 'unknown')
                }

            total_time = time.time() - start_time
            print(f"⚡ Traitement terminé en {total_time:.3f}s")

            return results

        except Exception as e:
            print(f"❌ Erreur traitement image: {e}")
            return {}

    def _cleanup_gpu_memory(self):
        """Nettoie la mémoire GPU"""
        try:
            if self.use_cuda:
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                print("🧹 Mémoire GPU nettoyée")
        except Exception as e:
            print(f"⚠️ Erreur nettoyage GPU: {e}")

# Test rapide
if __name__ == "__main__":
    print("🔥 Test du détecteur CUDA optimisé")

    # Créer le détecteur
    detector = CudaOptimizedDetector()

    # Créer une image de test
    test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
    cv2.putText(test_image, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 3, (0, 0, 0), 4)

    # Test de détection
    start = time.time()
    result = detector.detect_text_fast(test_image)
    elapsed = time.time() - start

    print(f"✅ Résultat: '{result}' en {elapsed:.3f}s")
    print("✅ Test terminé")
