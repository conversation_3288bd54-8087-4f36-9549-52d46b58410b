#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyse des améliorations de détection depuis les optimisations
"""

import json
import os
from datetime import datetime, timedelta
from collections import defaultdict

def analyser_corrections_recentes():
    """Analyse les corrections récentes pour voir les améliorations"""
    print("🔍 ANALYSE DES AMÉLIORATIONS DE DÉTECTION")
    print("=" * 60)
    
    # Charger les corrections
    corrections_file = "learning_data/corrections.json"
    if not os.path.exists(corrections_file):
        print("❌ Fichier de corrections non trouvé")
        return
    
    with open(corrections_file, 'r', encoding='utf-8') as f:
        corrections = json.load(f)
    
    # Analyser par période
    maintenant = datetime.now()
    hier = maintenant - timedelta(days=1)
    semaine = maintenant - timedelta(days=7)
    
    corrections_hier = []
    corrections_semaine = []
    corrections_total = []
    
    for correction in corrections:
        timestamp = datetime.fromisoformat(correction['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
        corrections_total.append(correction)
        
        if timestamp >= hier:
            corrections_hier.append(correction)
        if timestamp >= semaine:
            corrections_semaine.append(correction)
    
    print(f"📊 STATISTIQUES GÉNÉRALES:")
    print(f"   Total corrections: {len(corrections_total)}")
    print(f"   Corrections cette semaine: {len(corrections_semaine)}")
    print(f"   Corrections hier: {len(corrections_hier)}")
    
    # Analyser les types d'erreurs
    print(f"\n🎯 ANALYSE DES ERREURS:")
    
    erreurs_par_type = defaultdict(int)
    erreurs_par_region = defaultdict(int)
    
    for correction in corrections_total:
        erreurs_par_type[correction.get('correction_type', 'unknown')] += 1
        erreurs_par_region[correction['region_name']] += 1
    
    print(f"   Types d'erreurs:")
    for type_erreur, count in sorted(erreurs_par_type.items(), key=lambda x: x[1], reverse=True):
        print(f"     {type_erreur}: {count}")
    
    print(f"   Erreurs par région:")
    for region, count in sorted(erreurs_par_region.items(), key=lambda x: x[1], reverse=True):
        print(f"     {region}: {count}")
    
    # Analyser les améliorations récentes
    print(f"\n📈 AMÉLIORATIONS RÉCENTES:")
    
    if len(corrections_hier) == 0:
        print("   ✅ Aucune correction hier - Excellente performance !")
    else:
        print(f"   ⚠️ {len(corrections_hier)} corrections hier")
        for correction in corrections_hier[-3:]:  # Dernières 3
            print(f"     {correction['region_name']}: {correction['detected_value']} → {correction['corrected_value']}")
    
    # Tendance d'amélioration
    corrections_par_jour = defaultdict(int)
    for correction in corrections_semaine:
        timestamp = datetime.fromisoformat(correction['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
        jour = timestamp.strftime('%Y-%m-%d')
        corrections_par_jour[jour] += 1
    
    print(f"\n📅 TENDANCE HEBDOMADAIRE:")
    for jour in sorted(corrections_par_jour.keys()):
        print(f"   {jour}: {corrections_par_jour[jour]} corrections")
    
    return corrections_total, corrections_semaine, corrections_hier

def analyser_performance_detection():
    """Analyse la performance de détection récente"""
    print(f"\n🚀 ANALYSE DE PERFORMANCE:")
    
    # Analyser les résultats récents
    results_file = "detection_results.json"
    if os.path.exists(results_file):
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # Compter les détections réussies
        detections_reussies = 0
        detections_totales = 0
        
        for region, data in results.items():
            detections_totales += 1
            if data.get('text', '').strip():
                detections_reussies += 1
        
        taux_reussite = (detections_reussies / detections_totales * 100) if detections_totales > 0 else 0
        
        print(f"   Détections réussies: {detections_reussies}/{detections_totales}")
        print(f"   Taux de réussite: {taux_reussite:.1f}%")
        
        # Analyser les types de régions
        regions_cartes = [r for r in results.keys() if 'card' in r or 'carte' in r]
        regions_montants = [r for r in results.keys() if 'jeton' in r or 'mise' in r or 'pot' in r]
        
        print(f"   Régions cartes: {len(regions_cartes)}")
        print(f"   Régions montants: {len(regions_montants)}")
        
        # Détections de cartes
        cartes_detectees = 0
        for region in regions_cartes:
            if results[region].get('text', '').strip():
                cartes_detectees += 1
        
        print(f"   Cartes détectées: {cartes_detectees}/{len(regions_cartes)}")
        
        # Détections de montants
        montants_detectes = 0
        for region in regions_montants:
            if results[region].get('text', '').strip():
                montants_detectes += 1
        
        print(f"   Montants détectés: {montants_detectes}/{len(regions_montants)}")
    
    else:
        print("   ⚠️ Aucun résultat récent trouvé")

def analyser_optimisations_cuda():
    """Analyse l'impact des optimisations CUDA"""
    print(f"\n⚡ ANALYSE OPTIMISATIONS CUDA:")
    
    # Analyser les logs de debug
    debug_log = "debug_crash.log"
    if os.path.exists(debug_log):
        with open(debug_log, 'r', encoding='utf-8') as f:
            logs = f.readlines()
        
        # Compter les détections récentes
        detections_aujourd_hui = 0
        for line in logs:
            if "🎯 Début de la détection des cartes" in line:
                # Vérifier si c'est aujourd'hui
                if datetime.now().strftime('%Y-%m-%d') in line:
                    detections_aujourd_hui += 1
        
        print(f"   Détections aujourd'hui: {detections_aujourd_hui}")
        
        # Analyser la stabilité
        crashes = 0
        for line in logs:
            if "💀" in line or "❌" in line:
                crashes += 1
        
        print(f"   Incidents détectés: {crashes}")
        
        if detections_aujourd_hui > 0:
            stabilite = ((detections_aujourd_hui - crashes) / detections_aujourd_hui * 100)
            print(f"   Stabilité: {stabilite:.1f}%")
    
    else:
        print("   ⚠️ Logs de debug non trouvés")

def generer_rapport_ameliorations():
    """Génère un rapport complet des améliorations"""
    print(f"\n📋 RAPPORT D'AMÉLIORATIONS")
    print("=" * 60)
    
    corrections_total, corrections_semaine, corrections_hier = analyser_corrections_recentes()
    analyser_performance_detection()
    analyser_optimisations_cuda()
    
    # Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    
    if len(corrections_hier) == 0:
        print("   ✅ Performance excellente - Continuez ainsi !")
    elif len(corrections_hier) < 3:
        print("   ✅ Performance bonne - Quelques ajustements mineurs")
    else:
        print("   ⚠️ Performance à améliorer - Vérifiez la calibration")
    
    # Analyser les erreurs fréquentes
    erreurs_frequentes = defaultdict(int)
    for correction in corrections_semaine:
        if correction.get('correction_type') == 'misidentification':
            detected = correction.get('detected_value', '')
            corrected = correction.get('corrected_value', '')
            if detected and corrected:
                erreurs_frequentes[f"{detected}→{corrected}"] += 1
    
    if erreurs_frequentes:
        print(f"   🎯 Erreurs fréquentes à corriger:")
        for erreur, count in sorted(erreurs_frequentes.items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"     {erreur}: {count} fois")
    
    # Optimisations suggérées
    print(f"\n🔧 OPTIMISATIONS SUGGÉRÉES:")
    print("   1. Continuer l'apprentissage avec les corrections")
    print("   2. Ajuster les seuils de détection si nécessaire")
    print("   3. Vérifier la calibration des régions problématiques")
    print("   4. Utiliser l'optimisation RTX 3060 Ti pour la vitesse")

def main():
    """Fonction principale"""
    try:
        generer_rapport_ameliorations()
        
        print(f"\n" + "=" * 60)
        print("✅ ANALYSE TERMINÉE")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
