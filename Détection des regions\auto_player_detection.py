#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 DÉTECTION AUTOMATIQUE DES JOUEURS ET CONSEILS EN TEMPS RÉEL
==============================================================

Module qui capture automatiquement les pseudos des joueurs à votre table
et donne des conseils personnalisés en temps réel basés sur leurs actions.
"""

import json
import time
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import mss
import cv2
import numpy as np
from poker_tracker_intelligent import IntelligentTracker
from poker_advisor_tracker_integration import PokerAdvisorWithTracker

@dataclass
class PlayerAction:
    """Action d'un joueur détectée"""
    player_name: str
    action: str  # fold, call, bet, raise, check, all-in
    amount: int = 0
    timestamp: float = 0.0
    position: str = ""

@dataclass
class TableState:
    """État actuel de la table"""
    active_players: List[str]
    current_pot: int = 0
    my_chips: int = 0
    my_cards: List[str] = None
    board_cards: List[str] = None
    recent_actions: List[PlayerAction] = None
    dealer_position: str = ""

    def __post_init__(self):
        if self.my_cards is None:
            self.my_cards = []
        if self.board_cards is None:
            self.board_cards = []
        if self.recent_actions is None:
            self.recent_actions = []

class AutoPlayerDetector:
    """Détecteur automatique des joueurs et de leurs actions"""

    def __init__(self, config_path: str = None):
        # Charger la configuration
        self.config_path = config_path or r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
        self.config = self._load_config()

        # Initialiser le tracker intelligent
        self.tracker = PokerAdvisorWithTracker()

        # État de la table
        self.table_state = TableState(active_players=[])
        self.last_detection_time = 0
        self.detection_interval = 2.0  # Détecter toutes les 2 secondes

        # Cache des pseudos détectés
        self.player_cache = {}
        self.last_player_scan = 0
        self.player_scan_interval = 10.0  # Scanner les pseudos toutes les 10 secondes

        # Surveillance automatique
        self.monitoring = False
        self.monitor_thread = None

        # Capture d'écran
        self.sct = mss.mss()

        print("🎯 Détecteur automatique des joueurs initialisé")
        print(f"📁 Configuration: {self.config_path}")

    def _load_config(self) -> Dict:
        """Charge la configuration"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"✅ Configuration chargée: {len(config.get('all_regions', {}))} régions")
                return config
        except Exception as e:
            print(f"❌ Erreur chargement config: {e}")
            return {"all_regions": {}}

    def start_monitoring(self):
        """Démarre la surveillance automatique"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("🔄 Surveillance automatique démarrée")

    def stop_monitoring(self):
        """Arrête la surveillance"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("⏹️ Surveillance automatique arrêtée")

    def _monitor_loop(self):
        """Boucle principale de surveillance"""
        while self.monitoring:
            try:
                current_time = time.time()

                # Scanner les pseudos périodiquement
                if current_time - self.last_player_scan > self.player_scan_interval:
                    self._scan_player_names()
                    self.last_player_scan = current_time

                # Détecter les actions périodiquement
                if current_time - self.last_detection_time > self.detection_interval:
                    self._detect_table_state()
                    self.last_detection_time = current_time

                time.sleep(1)  # Pause courte entre les vérifications

            except Exception as e:
                print(f"⚠️ Erreur surveillance: {e}")
                time.sleep(5)

    def _scan_player_names(self):
        """Scanne les pseudos des joueurs présents"""
        try:
            # Capturer l'écran
            screenshot = self._capture_screen()
            if screenshot is None:
                return

            detected_players = []

            # Scanner chaque région de pseudo
            for i in range(1, 8):
                region_name = f"pseudo_joueur{i}"
                if region_name in self.config.get('all_regions', {}):
                    region = self.config['all_regions'][region_name]

                    # Extraire la région
                    x, y, w, h = region['x'], region['y'], region['width'], region['height']
                    player_region = screenshot[y:y+h, x:x+w]

                    # Détecter le texte (pseudo)
                    player_name = self._detect_text_in_region(player_region, is_player_name=True)

                    if player_name and len(player_name) > 2:  # Pseudo valide
                        detected_players.append(player_name)
                        self.player_cache[f"joueur{i}"] = player_name
                        print(f"👤 Joueur{i} détecté: {player_name}")

            # Mettre à jour l'état de la table
            if detected_players:
                self.table_state.active_players = detected_players

                # Informer le tracker des joueurs présents
                self.tracker.set_current_table(detected_players)

                print(f"🎲 Table mise à jour: {len(detected_players)} joueurs détectés")
                print(f"👥 Joueurs: {', '.join(detected_players)}")

        except Exception as e:
            print(f"❌ Erreur scan pseudos: {e}")

    def _detect_table_state(self):
        """Détecte l'état actuel de la table"""
        try:
            # Capturer l'écran
            screenshot = self._capture_screen()
            if screenshot is None:
                return

            # Détecter mes cartes
            my_cards = self._detect_my_cards(screenshot)
            if my_cards:
                self.table_state.my_cards = my_cards

            # Détecter les cartes du board
            board_cards = self._detect_board_cards(screenshot)
            if board_cards:
                self.table_state.board_cards = board_cards

            # Détecter le pot
            pot_amount = self._detect_pot_amount(screenshot)
            if pot_amount:
                self.table_state.current_pot = pot_amount

            # Détecter mes jetons
            my_chips = self._detect_my_chips(screenshot)
            if my_chips:
                self.table_state.my_chips = my_chips

            # Détecter les actions des adversaires
            self._detect_opponent_actions(screenshot)

        except Exception as e:
            print(f"❌ Erreur détection état table: {e}")

    def _capture_screen(self) -> Optional[np.ndarray]:
        """Capture l'écran"""
        try:
            # Utiliser la région de la table entière ou une région par défaut
            table_region = self.config.get('all_regions', {}).get('table', {
                'x': 0, 'y': 0, 'width': 1920, 'height': 1080
            })

            monitor = {
                "top": table_region.get('y', 0),
                "left": table_region.get('x', 0),
                "width": table_region.get('width', 1920),
                "height": table_region.get('height', 1080)
            }

            screenshot = self.sct.grab(monitor)
            img = np.array(screenshot)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

            return img

        except Exception as e:
            print(f"❌ Erreur capture écran: {e}")
            return None

    def _detect_text_in_region(self, region_img: np.ndarray, is_player_name: bool = False) -> str:
        """Détecte le texte dans une région"""
        try:
            # Prétraitement pour les pseudos
            if is_player_name:
                # Convertir en niveaux de gris
                gray = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)

                # Améliorer le contraste
                gray = cv2.convertScaleAbs(gray, alpha=1.5, beta=30)

                # Seuillage adaptatif
                binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                             cv2.THRESH_BINARY, 11, 2)

                # Utiliser EasyOCR pour la détection
                try:
                    import easyocr
                    reader = easyocr.Reader(['en', 'fr'], gpu=False, verbose=False)
                    results = reader.readtext(binary, detail=0, paragraph=False)

                    # Nettoyer et valider le résultat
                    for text in results:
                        cleaned = text.strip()
                        # Valider que c'est un pseudo valide (lettres, chiffres, certains symboles)
                        if len(cleaned) >= 3 and len(cleaned) <= 20:
                            # Filtrer les caractères valides pour un pseudo
                            valid_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-.')
                            if all(c in valid_chars for c in cleaned):
                                return cleaned

                except Exception as e:
                    print(f"⚠️ Erreur EasyOCR: {e}")

            return ""

        except Exception as e:
            print(f"❌ Erreur détection texte: {e}")
            return ""

    def _detect_my_cards(self, screenshot: np.ndarray) -> List[str]:
        """Détecte mes cartes en main"""
        try:
            cards = []

            # Détecter carte_1m et carte_2m
            for card_name in ['carte_1m', 'carte_2m']:
                if card_name in self.config.get('all_regions', {}):
                    region = self.config['all_regions'][card_name]
                    x, y, w, h = region['x'], region['y'], region['width'], region['height']
                    card_region = screenshot[y:y+h, x:x+w]

                    # Utiliser votre système de détection existant
                    card_value = self._detect_card_value(card_region)
                    if card_value:
                        cards.append(card_value)

            return cards

        except Exception as e:
            print(f"❌ Erreur détection mes cartes: {e}")
            return []

    def _detect_board_cards(self, screenshot: np.ndarray) -> List[str]:
        """Détecte les cartes du board"""
        try:
            cards = []

            # Détecter card_1 à card_5
            for i in range(1, 6):
                card_name = f"card_{i}"
                if card_name in self.config.get('all_regions', {}):
                    region = self.config['all_regions'][card_name]
                    x, y, w, h = region['x'], region['y'], region['width'], region['height']
                    card_region = screenshot[y:y+h, x:x+w]

                    # Utiliser votre système de détection existant
                    card_value = self._detect_card_value(card_region)
                    if card_value:
                        cards.append(card_value)

            return cards

        except Exception as e:
            print(f"❌ Erreur détection board: {e}")
            return []

    def _detect_card_value(self, card_region: np.ndarray) -> str:
        """Détecte la valeur d'une carte (utilise votre système existant)"""
        # Placeholder - intégrer avec votre système de détection de cartes existant
        # Vous pouvez utiliser votre multi_ocr_detector ou detector_cuda_optimized ici
        return ""

    def _detect_pot_amount(self, screenshot: np.ndarray) -> int:
        """Détecte le montant du pot"""
        try:
            if 'pot_total' in self.config.get('all_regions', {}):
                region = self.config['all_regions']['pot_total']
                x, y, w, h = region['x'], region['y'], region['width'], region['height']
                pot_region = screenshot[y:y+h, x:x+w]

                # Détecter le texte du montant
                amount_text = self._detect_amount_in_region(pot_region)
                if amount_text:
                    return self._parse_amount(amount_text)

            return 0

        except Exception as e:
            print(f"❌ Erreur détection pot: {e}")
            return 0

    def _detect_my_chips(self, screenshot: np.ndarray) -> int:
        """Détecte mes jetons"""
        try:
            if 'mes_jetons' in self.config.get('all_regions', {}):
                region = self.config['all_regions']['mes_jetons']
                x, y, w, h = region['x'], region['y'], region['width'], region['height']
                chips_region = screenshot[y:y+h, x:x+w]

                # Détecter le texte du montant
                amount_text = self._detect_amount_in_region(chips_region)
                if amount_text:
                    return self._parse_amount(amount_text)

            return 0

        except Exception as e:
            print(f"❌ Erreur détection mes jetons: {e}")
            return 0

    def _detect_opponent_actions(self, screenshot: np.ndarray):
        """Détecte les actions des adversaires"""
        try:
            # Détecter les mises des adversaires
            for i in range(1, 8):
                bet_region_name = f"mise_joueur{i}"
                if bet_region_name in self.config.get('all_regions', {}):
                    region = self.config['all_regions'][bet_region_name]
                    x, y, w, h = region['x'], region['y'], region['width'], region['height']
                    bet_region = screenshot[y:y+h, x:x+w]

                    # Détecter le montant de la mise
                    bet_amount_text = self._detect_amount_in_region(bet_region)
                    if bet_amount_text:
                        bet_amount = self._parse_amount(bet_amount_text)
                        player_name = self.player_cache.get(f"joueur{i}", f"Joueur{i}")

                        # Détecter le type d'action basé sur la couleur et le montant
                        action_type = self._determine_action_type(bet_region, bet_amount)

                        # Enregistrer l'action
                        action = PlayerAction(
                            player_name=player_name,
                            action=action_type,
                            amount=bet_amount,
                            timestamp=time.time(),
                            position=f"joueur{i}"
                        )

                        self.table_state.recent_actions.append(action)

                        # Garder seulement les 10 dernières actions
                        if len(self.table_state.recent_actions) > 10:
                            self.table_state.recent_actions = self.table_state.recent_actions[-10:]

                        print(f"🎯 Action détectée: {player_name} {action_type} {bet_amount}")

                        # Générer un conseil en temps réel
                        self._generate_realtime_advice(action)

        except Exception as e:
            print(f"❌ Erreur détection actions: {e}")

    def _detect_amount_in_region(self, region_img: np.ndarray) -> str:
        """Détecte un montant dans une région"""
        try:
            # Prétraitement pour les montants
            gray = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)

            # Améliorer le contraste pour les chiffres
            gray = cv2.convertScaleAbs(gray, alpha=2.0, beta=50)

            # Seuillage pour isoler le texte
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Utiliser EasyOCR pour détecter les chiffres
            try:
                import easyocr
                reader = easyocr.Reader(['en'], gpu=False, verbose=False)
                results = reader.readtext(binary, detail=0, paragraph=False,
                                        allowlist='0123456789.,KM ')

                # Combiner tous les résultats numériques
                amount_text = ""
                for text in results:
                    cleaned = text.strip()
                    if any(c.isdigit() for c in cleaned):
                        amount_text += cleaned + " "

                return amount_text.strip()

            except Exception as e:
                print(f"⚠️ Erreur EasyOCR montant: {e}")
                return ""

        except Exception as e:
            print(f"❌ Erreur détection montant: {e}")
            return ""

    def _parse_amount(self, amount_text: str) -> int:
        """Parse un montant depuis le texte détecté"""
        try:
            # Nettoyer le texte
            cleaned = amount_text.replace(" ", "").replace(",", "").upper()

            # Extraire les chiffres
            import re
            numbers = re.findall(r'\d+', cleaned)

            if numbers:
                base_amount = int(numbers[0])

                # Gérer les suffixes K et M
                if 'K' in cleaned:
                    return base_amount * 1000
                elif 'M' in cleaned:
                    return base_amount * 1000000
                else:
                    return base_amount

            return 0

        except Exception as e:
            print(f"❌ Erreur parse montant: {e}")
            return 0

    def _determine_action_type(self, bet_region: np.ndarray, amount: int) -> str:
        """Détermine le type d'action basé sur la région et le montant"""
        try:
            # Analyser les couleurs pour détecter les all-ins (rouge)
            hsv = cv2.cvtColor(bet_region, cv2.COLOR_BGR2HSV)

            # Masque pour détecter le rouge (all-in)
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([170, 50, 50])
            upper_red2 = np.array([180, 255, 255])

            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            red_mask = mask1 + mask2

            red_pixels = cv2.countNonZero(red_mask)
            total_pixels = bet_region.shape[0] * bet_region.shape[1]
            red_ratio = red_pixels / total_pixels

            # Si beaucoup de rouge, c'est un all-in
            if red_ratio > 0.1:
                return "all-in"

            # Sinon, déterminer selon le montant
            if amount == 0:
                return "check"  # ou fold
            elif amount > 0:
                # Vous pouvez affiner cette logique selon vos besoins
                return "bet"  # ou call/raise selon le contexte

            return "unknown"

        except Exception as e:
            print(f"❌ Erreur détermination action: {e}")
            return "unknown"

    def _generate_realtime_advice(self, action: PlayerAction):
        """Génère un conseil en temps réel basé sur l'action d'un adversaire"""
        try:
            # Obtenir le profil du joueur depuis le tracker
            player_profile = self.tracker.tracker.get_player_profile(action.player_name)

            if player_profile:
                stats = player_profile['stats']
                style = player_profile['style']
                recommendations = player_profile['recommendations']

                print(f"\n🎯 CONSEIL TEMPS RÉEL pour {action.player_name}")
                print(f"Action: {action.action} {action.amount}")
                print(f"Style: {style['type']} (confiance: {style['confidence']*100:.0f}%)")
                print(f"Stats: VPIP {stats.vpip:.1f}% | PFR {stats.pfr:.1f}% | AF {stats.aggression_factor:.1f}")

                # Conseil spécifique à l'action
                if action.action == "all-in":
                    if stats.aggression_factor > 3:
                        print("⚠️ ATTENTION: Joueur agressif en all-in - main forte probable!")
                    else:
                        print("🚨 ALERTE: Joueur passif en all-in - NUTS probable!")

                elif action.action == "bet" and action.amount > self.table_state.current_pot:
                    if "Loose-Aggressive" in style['type']:
                        print("💡 Grosse mise d'un LAG - peut être un bluff")
                    elif "Tight" in style['type']:
                        print("⚠️ Grosse mise d'un joueur serré - main très forte!")

                # Afficher les recommandations générales
                print("💡 Recommandations contre ce joueur:")
                for rec in recommendations[:2]:
                    print(f"  • {rec}")

                print("-" * 50)

            else:
                print(f"🔍 {action.player_name} : Joueur inconnu - observez attentivement")

        except Exception as e:
            print(f"❌ Erreur génération conseil: {e}")

    def get_current_advice(self) -> Dict[str, Any]:
        """Obtient les conseils actuels pour la situation"""
        try:
            if not self.table_state.active_players:
                return {"error": "Aucun joueur détecté"}

            # Obtenir une recommandation améliorée du tracker
            recommendation = self.tracker.get_enhanced_recommendation(
                hand_cards=self.table_state.my_cards,
                board_cards=self.table_state.board_cards,
                pot_size=self.table_state.current_pot,
                my_stack=self.table_state.my_chips,
                opponent_bets={action.player_name: action.amount
                             for action in self.table_state.recent_actions[-3:]}
            )

            # Ajouter les informations de la table
            recommendation["table_state"] = {
                "players": self.table_state.active_players,
                "pot": self.table_state.current_pot,
                "my_chips": self.table_state.my_chips,
                "my_cards": self.table_state.my_cards,
                "board": self.table_state.board_cards,
                "recent_actions": [
                    f"{a.player_name}: {a.action} {a.amount}"
                    for a in self.table_state.recent_actions[-3:]
                ]
            }

            return recommendation

        except Exception as e:
            print(f"❌ Erreur obtention conseil: {e}")
            return {"error": str(e)}

    def force_player_scan(self):
        """Force un scan immédiat des joueurs"""
        print("🔄 Scan forcé des joueurs...")
        self._scan_player_names()

    def force_table_update(self):
        """Force une mise à jour immédiate de l'état de la table"""
        print("🔄 Mise à jour forcée de la table...")
        self._detect_table_state()

    def get_table_summary(self) -> Dict[str, Any]:
        """Obtient un résumé de l'état actuel de la table"""
        return {
            "active_players": self.table_state.active_players,
            "player_count": len(self.table_state.active_players),
            "pot": self.table_state.current_pot,
            "my_chips": self.table_state.my_chips,
            "my_cards": self.table_state.my_cards,
            "board_cards": self.table_state.board_cards,
            "recent_actions_count": len(self.table_state.recent_actions),
            "monitoring": self.monitoring
        }

# Fonctions utilitaires
def create_auto_detector(config_path: str = None) -> AutoPlayerDetector:
    """Crée un détecteur automatique"""
    return AutoPlayerDetector(config_path)

def start_realtime_monitoring(config_path: str = None) -> AutoPlayerDetector:
    """Démarre la surveillance en temps réel"""
    detector = create_auto_detector(config_path)
    detector.start_monitoring()
    return detector

if __name__ == "__main__":
    # Test du détecteur automatique
    print("🎯 Test du Détecteur Automatique de Joueurs")
    print("=" * 50)

    try:
        # Créer le détecteur
        detector = create_auto_detector()

        # Démarrer la surveillance
        detector.start_monitoring()

        print("✅ Surveillance démarrée")
        print("🔄 Le système détecte automatiquement les joueurs et leurs actions...")
        print("💡 Appuyez sur Ctrl+C pour arrêter")

        # Boucle de test
        try:
            while True:
                time.sleep(5)

                # Afficher un résumé périodique
                summary = detector.get_table_summary()
                print(f"\n📊 Résumé table: {summary['player_count']} joueurs, "
                      f"Pot: {summary['pot']}, Actions: {summary['recent_actions_count']}")

        except KeyboardInterrupt:
            print("\n⏹️ Arrêt demandé par l'utilisateur")

        # Arrêter la surveillance
        detector.stop_monitoring()
        print("✅ Surveillance arrêtée")

    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
