#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyse simple des améliorations
"""

import json
import os
from datetime import datetime

def main():
    print("🔍 ANALYSE DES AMÉLIORATIONS DE DÉTECTION")
    print("=" * 50)
    
    # 1. Analyser les corrections
    try:
        with open("learning_data/corrections.json", 'r', encoding='utf-8') as f:
            corrections = json.load(f)
        
        print(f"📊 Total corrections: {len(corrections)}")
        
        # Corrections récentes (dernières 10)
        corrections_recentes = corrections[-10:] if len(corrections) > 10 else corrections
        print(f"📅 Corrections récentes: {len(corrections_recentes)}")
        
        # Types d'erreurs
        types_erreurs = {}
        for c in corrections:
            type_err = c.get('correction_type', 'unknown')
            types_erreurs[type_err] = types_erreurs.get(type_err, 0) + 1
        
        print(f"🎯 Types d'erreurs:")
        for type_err, count in types_erreurs.items():
            print(f"   {type_err}: {count}")
            
    except Exception as e:
        print(f"⚠️ Erreur lecture corrections: {e}")
    
    # 2. Analyser les résultats récents
    try:
        with open("detection_results.json", 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        detections_ok = 0
        total_regions = 0
        
        for region, data in results.items():
            total_regions += 1
            if data.get('text', '').strip():
                detections_ok += 1
        
        taux = (detections_ok / total_regions * 100) if total_regions > 0 else 0
        print(f"🚀 Performance actuelle:")
        print(f"   Détections réussies: {detections_ok}/{total_regions}")
        print(f"   Taux de réussite: {taux:.1f}%")
        
    except Exception as e:
        print(f"⚠️ Erreur lecture résultats: {e}")
    
    # 3. Analyser les logs
    try:
        with open("debug_crash.log", 'r', encoding='utf-8') as f:
            logs = f.readlines()
        
        detections_today = 0
        for line in logs:
            if "🎯 Début de la détection des cartes" in line:
                if "2025-05-29" in line:  # Aujourd'hui
                    detections_today += 1
        
        print(f"📈 Activité aujourd'hui:")
        print(f"   Détections lancées: {detections_today}")
        
    except Exception as e:
        print(f"⚠️ Erreur lecture logs: {e}")
    
    # 4. Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    print("   ✅ Optimisation RTX 3060 Ti implémentée")
    print("   ✅ Système d'apprentissage actif")
    print("   ✅ Détecteur persistant pour la vitesse")
    print("   🎯 Continuez les corrections pour améliorer la précision")
    
    print(f"\n" + "=" * 50)
    print("✅ ANALYSE TERMINÉE")

if __name__ == "__main__":
    main()
