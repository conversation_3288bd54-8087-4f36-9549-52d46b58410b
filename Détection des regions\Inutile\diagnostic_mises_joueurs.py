#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic complet de la détection des mises de joueurs
Vérifie tous les aspects de la détection des mises
"""

import sys
import os
import re

def verifier_detection_mises_detector():
    """Vérifie la détection des mises dans detector.py"""
    print("🔍 VÉRIFICATION DETECTOR.PY")
    print("=" * 50)
    
    try:
        with open("detector.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Vérifications de la détection des mises
        checks = [
            ("mise_joueur", "✅ Détection des régions mise_joueur"),
            ("startswith(\"mise_joueur\")", "✅ Traitement spécial mises joueurs"),
            ("detect_colors_fast", "✅ Détection couleurs pour all-in"),
            ("💲 MISE JOUEUR", "✅ Logs spéciaux pour mises"),
            ("colors = self.detect_colors", "✅ Détection couleurs activée"),
            ("len(text) < 2", "✅ Vérification montants tronqués")
        ]
        
        print("🔍 Vérifications dans detector.py :")
        print("-" * 40)
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        # Vérifier les seuils de détection
        if "threshold = 8.0" in content:
            print("✅ Seuil rouge ajusté pour éviter faux positifs")
        else:
            print("⚠️ Seuil rouge peut causer des faux positifs")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def verifier_traitement_mises_advisor():
    """Vérifie le traitement des mises dans poker_advisor_light.py"""
    print("\n💡 VÉRIFICATION POKER_ADVISOR_LIGHT.PY")
    print("=" * 50)
    
    try:
        with open("poker_advisor_light.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Vérifications du traitement des mises
        checks = [
            ("region_name.startswith(\"mise_joueur\")", "✅ Traitement mises joueurs"),
            ("bet_text = region_data.get(\"text\"", "✅ Extraction texte mise"),
            ("colors = region_data.get(\"colors\"", "✅ Extraction couleurs mise"),
            ("convert_to_float(bet_text)", "✅ Conversion montant mise"),
            ("player_bets[f\"joueur{player_num}\"]", "✅ Stockage mise par joueur"),
            ("💲 Texte brut pour", "✅ Logs de débogage mises"),
            ("💲 Valeur convertie pour", "✅ Logs valeur convertie"),
            ("is_allin = data[\"allin_indicators\"]", "✅ Détection all-in"),
            ("data[\"pot_total\"] += bet_value", "✅ Ajout au pot total")
        ]
        
        print("🔍 Vérifications dans poker_advisor_light.py :")
        print("-" * 40)
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        # Vérifier la gestion des erreurs
        if "if bet_value > 0:" in content:
            print("✅ Vérification valeur positive")
        else:
            print("❌ Pas de vérification valeur positive")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def verifier_integration_mises():
    """Vérifie l'intégration des mises dans poker_advisor_integration.py"""
    print("\n🔗 VÉRIFICATION POKER_ADVISOR_INTEGRATION.PY")
    print("=" * 50)
    
    try:
        with open("poker_advisor_integration.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Vérifications de l'intégration
        checks = [
            ("'mise_joueur' in region_name", "✅ Détection régions mise_joueur"),
            ("player_num = region_name.replace('mise_joueur'", "✅ Extraction numéro joueur"),
            ("1 <= int(player_num) <= 6", "✅ Limitation table 6 joueurs"),
            ("analysis['player_bets']", "✅ Stockage dans analyse"),
            ("🎯 Joueur", "✅ Logs mises par joueur"),
            ("float(text.replace(',', '.'))", "✅ Conversion avec virgules"),
            ("except:", "✅ Gestion erreurs conversion")
        ]
        
        print("🔍 Vérifications dans poker_advisor_integration.py :")
        print("-" * 40)
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def verifier_affichage_mises_gui():
    """Vérifie l'affichage des mises dans detector_gui.py"""
    print("\n🖥️ VÉRIFICATION DETECTOR_GUI.PY")
    print("=" * 50)
    
    try:
        with open("detector_gui.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Vérifications de l'affichage
        checks = [
            ("mise_joueur", "✅ Gestion régions mise_joueur"),
            ("player_bets", "✅ Affichage mises joueurs"),
            ("game_analysis", "✅ Intégration analyse de jeu"),
            ("button_position", "✅ Position bouton dealer"),
            ("update_advisor_analysis", "✅ Mise à jour analyse conseiller")
        ]
        
        print("🔍 Vérifications dans detector_gui.py :")
        print("-" * 40)
        
        for check, message in checks:
            if check in content:
                print(message)
            else:
                print(f"❌ MANQUANT: {check}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def identifier_problemes_potentiels():
    """Identifie les problèmes potentiels dans la détection des mises"""
    print("\n🚨 PROBLÈMES POTENTIELS IDENTIFIÉS")
    print("=" * 50)
    
    problemes = []
    
    # Vérifier la configuration
    config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
    if not os.path.exists(config_path):
        problemes.append("❌ Fichier de configuration manquant")
    else:
        try:
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Vérifier les régions de mises
            all_regions = config.get('all_regions', {})
            mise_regions = [name for name in all_regions.keys() if name.startswith('mise_joueur')]
            
            if len(mise_regions) == 0:
                problemes.append("❌ Aucune région mise_joueur calibrée")
            elif len(mise_regions) < 6:
                problemes.append(f"⚠️ Seulement {len(mise_regions)} régions mise_joueur calibrées (recommandé: 6)")
            else:
                print(f"✅ {len(mise_regions)} régions mise_joueur calibrées")
                
        except Exception as e:
            problemes.append(f"❌ Erreur lecture configuration: {e}")
    
    # Problèmes courants
    problemes_courants = [
        "🔍 PROBLÈMES COURANTS POSSIBLES :",
        "",
        "1. **Régions mal calibrées**",
        "   - Régions mise_joueur trop petites",
        "   - Régions qui incluent du texte parasite",
        "   - Régions qui ne couvrent pas tout le montant",
        "",
        "2. **Problèmes OCR**",
        "   - Police des montants difficile à lire",
        "   - Contraste insuffisant",
        "   - Montants partiellement cachés",
        "",
        "3. **Problèmes de couleurs**",
        "   - All-in (rouge) non détecté",
        "   - Confusion entre couleurs",
        "   - Seuils de détection inadaptés",
        "",
        "4. **Problèmes de conversion**",
        "   - Virgules vs points décimaux",
        "   - Caractères spéciaux dans les montants",
        "   - Montants avec unités (BB, €, etc.)",
        "",
        "5. **Problèmes de logique**",
        "   - Mises non prises en compte dans le conseiller",
        "   - Position du bouton incorrecte",
        "   - Calculs de pot odds erronés"
    ]
    
    if problemes:
        print("🚨 PROBLÈMES DÉTECTÉS :")
        print("-" * 40)
        for probleme in problemes:
            print(probleme)
    else:
        print("✅ Aucun problème majeur détecté")
    
    print("\n" + "\n".join(problemes_courants))

def recommandations_debug():
    """Fournit des recommandations pour déboguer les mises"""
    print("\n🔧 RECOMMANDATIONS DE DÉBOGAGE")
    print("=" * 50)
    
    recommendations = [
        "1. **Vérifier les logs en temps réel**",
        "   - Chercher les messages '💲 MISE JOUEUR'",
        "   - Vérifier les valeurs converties",
        "   - Observer les couleurs détectées",
        "",
        "2. **Tester la calibration**",
        "   - Ouvrir calibration_simple.py",
        "   - Vérifier chaque région mise_joueur",
        "   - S'assurer qu'elles couvrent bien les montants",
        "",
        "3. **Vérifier l'OCR**",
        "   - Tester avec des montants simples (1, 5, 10)",
        "   - Vérifier la lisibilité des polices",
        "   - Ajuster les régions si nécessaire",
        "",
        "4. **Déboguer les couleurs**",
        "   - Vérifier la détection rouge pour all-in",
        "   - Tester avec différents montants",
        "   - Observer les pourcentages de couleurs",
        "",
        "5. **Vérifier l'intégration**",
        "   - S'assurer que les mises apparaissent dans l'analyse",
        "   - Vérifier les calculs de pot odds",
        "   - Contrôler la position du bouton dealer"
    ]
    
    for rec in recommendations:
        print(rec)

def main():
    """Fonction principale de diagnostic"""
    print("🔍 DIAGNOSTIC COMPLET - DÉTECTION DES MISES DE JOUEURS")
    print("=" * 60)
    print("Vérification de tous les aspects de la détection des mises")
    print()
    
    # Changer vers le bon répertoire
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Effectuer toutes les vérifications
    verifications = [
        verifier_detection_mises_detector(),
        verifier_traitement_mises_advisor(),
        verifier_integration_mises(),
        verifier_affichage_mises_gui()
    ]
    
    # Identifier les problèmes
    identifier_problemes_potentiels()
    
    # Recommandations
    recommandations_debug()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DU DIAGNOSTIC")
    print("-" * 60)
    
    success_count = sum(verifications)
    total_count = len(verifications)
    
    if success_count == total_count:
        print("✅ TOUS LES COMPOSANTS VÉRIFIÉS AVEC SUCCÈS")
        print("🎯 La détection des mises semble bien implémentée")
        print()
        print("🔍 SI VOUS AVEZ ENCORE DES PROBLÈMES :")
        print("   1. Vérifiez la calibration des régions mise_joueur")
        print("   2. Observez les logs en temps réel")
        print("   3. Testez avec des montants simples")
        print("   4. Vérifiez que les mises apparaissent dans l'analyse")
    else:
        print(f"⚠️ {success_count}/{total_count} composants vérifiés")
        print("Certains éléments peuvent nécessiter une attention.")
    
    print("\n🚀 POUR TESTER :")
    print("   1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("   2. Activez le conseiller poker")
    print("   3. Observez les logs pour les messages '💲 MISE JOUEUR'")
    print("   4. Vérifiez que les mises apparaissent dans l'analyse")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
