@echo off
echo ===================================================
echo Installation de PaddleOCR pour Poker Advisor
echo ===================================================
echo.

echo Verification de Python...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo Python n'est pas installe ou n'est pas dans le PATH.
    echo Veuillez installer Python 3.8 ou superieur.
    pause
    exit /b 1
)

echo.
echo Desinstallation des versions precedentes...
pip uninstall -y paddlepaddle paddleocr

echo.
echo Installation de PaddlePaddle...
pip install paddlepaddle==2.5.2
if %ERRORLEVEL% NEQ 0 (
    echo Tentative avec une autre version...
    pip install paddlepaddle==2.6.0
    if %ERRORLEVEL% NEQ 0 (
        echo Installation de la derniere version...
        pip install paddlepaddle
    )
)

echo.
echo Installation de PaddleOCR...
pip install paddleocr==*******
if %ERRORLEVEL% NEQ 0 (
    echo Tentative avec une autre version...
    pip install paddleocr==*******
    if %ERRORLEVEL% NEQ 0 (
        echo Installation de la derniere version...
        pip install paddleocr
    )
)

echo.
echo Test de PaddleOCR...
python -c "from paddleocr import PaddleOCR; print('Test de PaddleOCR...'); ocr = PaddleOCR(use_angle_cls=False, lang='en', use_gpu=False, show_log=False); print('PaddleOCR fonctionne correctement!')"
if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors du test de PaddleOCR.
    echo Veuillez verifier l'installation manuellement.
) else (
    echo.
    echo Installation reussie!
)

echo.
pause
