#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Assistant Ollama pour le projet Poker
Intègre Ollama directement dans votre environnement de développement
"""

import requests
import json
import sys
import os

class OllamaAssistant:
    def __init__(self, model="mistral:7b", host="http://localhost:11434"):
        self.model = model
        self.host = host
        self.conversation_history = []
    
    def is_ollama_running(self):
        """Vérifie si Ollama est en cours d'exécution"""
        try:
            response = requests.get(f"{self.host}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def list_models(self):
        """Liste les modèles disponibles"""
        try:
            response = requests.get(f"{self.host}/api/tags")
            if response.status_code == 200:
                models = response.json()
                return [model['name'] for model in models.get('models', [])]
            return []
        except:
            return []
    
    def ask(self, question, context=""):
        """Pose une question à Ollama"""
        if not self.is_ollama_running():
            return "❌ Erreur: Ollama n'est pas en cours d'exécution. Lancez 'ollama serve' d'abord."
        
        # Construire le prompt avec contexte
        full_prompt = f"""Tu es un assistant spécialisé en programmation Python et développement d'applications de poker.
        
Contexte du projet: Application de détection de cartes de poker avec OCR, conseiller poker intelligent, et interface PyQt.

{context}

Question: {question}

Réponds en français de manière précise et technique."""

        try:
            payload = {
                "model": self.model,
                "prompt": full_prompt,
                "stream": False
            }
            
            response = requests.post(f"{self.host}/api/generate", json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                answer = result.get('response', 'Pas de réponse')
                
                # Sauvegarder dans l'historique
                self.conversation_history.append({
                    'question': question,
                    'answer': answer,
                    'context': context
                })
                
                return answer
            else:
                return f"❌ Erreur HTTP: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return "⏱️ Timeout: La requête a pris trop de temps"
        except Exception as e:
            return f"❌ Erreur: {str(e)}"
    
    def analyze_code(self, code, description=""):
        """Analyse un morceau de code"""
        context = f"Code à analyser: {description}" if description else "Analyse de code"
        question = f"""Analyse ce code Python et identifie :
1. Les bugs potentiels
2. Les améliorations possibles
3. Les optimisations
4. Les bonnes pratiques à appliquer

Code:
```python
{code}
```"""
        
        return self.ask(question, context)
    
    def debug_error(self, error_message, code_context=""):
        """Aide au debug d'une erreur"""
        question = f"""J'ai cette erreur dans mon code:

Erreur: {error_message}

{f'Contexte du code: {code_context}' if code_context else ''}

Peux-tu m'aider à comprendre et corriger cette erreur ?"""
        
        return self.ask(question, "Debug d'erreur")
    
    def suggest_improvement(self, current_code, goal):
        """Suggère des améliorations pour atteindre un objectif"""
        question = f"""J'ai ce code qui fonctionne mais je veux {goal}.

Code actuel:
```python
{current_code}
```

Peux-tu suggérer des améliorations ou une refactorisation ?"""
        
        return self.ask(question, f"Amélioration pour: {goal}")
    
    def explain_concept(self, concept):
        """Explique un concept de programmation"""
        question = f"Peux-tu m'expliquer le concept de '{concept}' en programmation Python, avec des exemples pratiques ?"
        return self.ask(question, "Explication de concept")
    
    def save_conversation(self, filename="conversation_ollama.json"):
        """Sauvegarde la conversation"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
            return f"✅ Conversation sauvée dans {filename}"
        except Exception as e:
            return f"❌ Erreur sauvegarde: {e}"

def main():
    """Interface en ligne de commande"""
    print("🤖 ASSISTANT OLLAMA POKER")
    print("=" * 50)
    
    # Initialiser l'assistant
    assistant = OllamaAssistant()
    
    # Vérifier si Ollama fonctionne
    if not assistant.is_ollama_running():
        print("❌ Ollama n'est pas en cours d'exécution.")
        print("💡 Lancez 'ollama serve' dans un autre terminal d'abord.")
        return
    
    # Lister les modèles disponibles
    models = assistant.list_models()
    if models:
        print(f"✅ Modèles disponibles: {', '.join(models)}")
        print(f"🎯 Modèle actuel: {assistant.model}")
    else:
        print("⚠️ Aucun modèle trouvé. Téléchargez un modèle avec 'ollama pull mistral:7b'")
        return
    
    print("\n💡 Commandes disponibles:")
    print("  'code' - Analyser du code")
    print("  'debug' - Aide au debug")
    print("  'improve' - Suggérer des améliorations")
    print("  'explain' - Expliquer un concept")
    print("  'save' - Sauvegarder la conversation")
    print("  'quit' - Quitter")
    print()
    
    while True:
        try:
            command = input("🎯 Que voulez-vous faire ? ").strip().lower()
            
            if command == 'quit':
                break
            elif command == 'code':
                print("📝 Collez votre code (terminez par une ligne vide):")
                code_lines = []
                while True:
                    line = input()
                    if line.strip() == "":
                        break
                    code_lines.append(line)
                
                code = "\n".join(code_lines)
                description = input("📋 Description du code (optionnel): ")
                
                print("\n🔍 Analyse en cours...")
                result = assistant.analyze_code(code, description)
                print(f"\n🤖 Réponse:\n{result}\n")
            
            elif command == 'debug':
                error = input("❌ Décrivez l'erreur: ")
                context = input("📋 Contexte du code (optionnel): ")
                
                print("\n🔍 Analyse en cours...")
                result = assistant.debug_error(error, context)
                print(f"\n🤖 Réponse:\n{result}\n")
            
            elif command == 'improve':
                print("📝 Collez votre code actuel (terminez par une ligne vide):")
                code_lines = []
                while True:
                    line = input()
                    if line.strip() == "":
                        break
                    code_lines.append(line)
                
                code = "\n".join(code_lines)
                goal = input("🎯 Quel est votre objectif d'amélioration ? ")
                
                print("\n🔍 Analyse en cours...")
                result = assistant.suggest_improvement(code, goal)
                print(f"\n🤖 Réponse:\n{result}\n")
            
            elif command == 'explain':
                concept = input("🧠 Quel concept voulez-vous que j'explique ? ")
                
                print("\n🔍 Recherche en cours...")
                result = assistant.explain_concept(concept)
                print(f"\n🤖 Réponse:\n{result}\n")
            
            elif command == 'save':
                result = assistant.save_conversation()
                print(f"{result}\n")
            
            else:
                # Question libre
                print("\n🔍 Analyse en cours...")
                result = assistant.ask(command)
                print(f"\n🤖 Réponse:\n{result}\n")
        
        except KeyboardInterrupt:
            print("\n👋 Au revoir !")
            break
        except Exception as e:
            print(f"❌ Erreur: {e}\n")

# Exemples d'utilisation rapide
def exemple_analyse_poker():
    """Exemple d'analyse de code poker"""
    assistant = OllamaAssistant()
    
    code_exemple = """
def detect_colors_fast(self, image):
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    if pourcentage > 5.0:
        return "orange"
    return "rien"
"""
    
    print("🧪 EXEMPLE - Analyse de code poker")
    result = assistant.analyze_code(code_exemple, "Détection de couleurs pour cartes de poker")
    print(result)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "exemple":
        exemple_analyse_poker()
    else:
        main()
