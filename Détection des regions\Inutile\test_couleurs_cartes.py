#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des couleurs des cartes dans l'analyse détaillée
Vérifie que les cartes sont bien colorées selon leurs couleurs
"""

def test_colorisation_cartes():
    """Test de la colorisation des cartes"""
    print("🎨 TEST DE LA COLORISATION DES CARTES")
    print("=" * 50)
    
    # Simuler la méthode add_card_colors_to_analysis
    def add_card_colors_to_analysis(formatted_analysis):
        """Ajoute les couleurs aux cartes dans l'analyse détaillée"""
        import re
        
        # Dictionnaire des couleurs pour les cartes
        card_colors = {
            # Cœur - Rouge
            '♥': '<span style="color: #FF0000; font-weight: bold;">♥</span>',
            'cœur': '<span style="color: #FF0000; font-weight: bold;">cœur</span>',
            'coeur': '<span style="color: #FF0000; font-weight: bold;">coeur</span>',
            'Cœur': '<span style="color: #FF0000; font-weight: bold;">Cœur</span>',
            'Coeur': '<span style="color: #FF0000; font-weight: bold;">Coeur</span>',
            
            # Pique - Noir
            '♠': '<span style="color: #000000; font-weight: bold;">♠</span>',
            'pique': '<span style="color: #000000; font-weight: bold;">pique</span>',
            'Pique': '<span style="color: #000000; font-weight: bold;">Pique</span>',
            
            # Carreau - Bleu
            '♦': '<span style="color: #0066FF; font-weight: bold;">♦</span>',
            'carreau': '<span style="color: #0066FF; font-weight: bold;">carreau</span>',
            'Carreau': '<span style="color: #0066FF; font-weight: bold;">Carreau</span>',
            
            # Trèfle - Vert
            '♣': '<span style="color: #00AA00; font-weight: bold;">♣</span>',
            'trèfle': '<span style="color: #00AA00; font-weight: bold;">trèfle</span>',
            'trefle': '<span style="color: #00AA00; font-weight: bold;">trefle</span>',
            'Trèfle': '<span style="color: #00AA00; font-weight: bold;">Trèfle</span>',
            'Trefle': '<span style="color: #00AA00; font-weight: bold;">Trefle</span>'
        }
        
        # Remplacer les couleurs dans le texte
        colored_analysis = formatted_analysis
        for original, colored in card_colors.items():
            colored_analysis = colored_analysis.replace(original, colored)
        
        # Améliorer l'affichage des cartes avec format "X de Y"
        # Pattern pour capturer "Valeur de Couleur"
        card_pattern = r'([AKQJ]|10|[2-9])\s+de\s+(cœur|coeur|pique|carreau|trèfle|trefle|Cœur|Coeur|Pique|Carreau|Trèfle|Trefle)'
        
        def colorize_card(match):
            value = match.group(1)
            suit = match.group(2).lower()
            
            # Déterminer la couleur selon la couleur de la carte
            if suit in ['cœur', 'coeur']:
                color = '#FF0000'  # Rouge
                symbol = '♥'
            elif suit in ['pique']:
                color = '#000000'  # Noir
                symbol = '♠'
            elif suit in ['carreau']:
                color = '#0066FF'  # Bleu
                symbol = '♦'
            elif suit in ['trèfle', 'trefle']:
                color = '#00AA00'  # Vert
                symbol = '♣'
            else:
                return match.group(0)  # Retourner tel quel si non reconnu
            
            return f'<span style="color: {color}; font-weight: bold;">{value}{symbol}</span>'
        
        # Appliquer la colorisation
        colored_analysis = re.sub(card_pattern, colorize_card, colored_analysis, flags=re.IGNORECASE)
        
        # Améliorer l'affichage de la section "Analyse détaillée"
        colored_analysis = colored_analysis.replace(
            '📊 Analyse détaillée:',
            '<span style="color: #00AAFF; font-weight: bold; font-size: 14px;">📊 Analyse détaillée:</span>'
        )
        
        # Améliorer l'affichage des cartes en main et du board
        colored_analysis = re.sub(
            r'(Cartes en main|Main\s*\d*)\s*:\s*',
            r'<span style="color: #FFAA00; font-weight: bold;">\1:</span> ',
            colored_analysis,
            flags=re.IGNORECASE
        )
        
        colored_analysis = re.sub(
            r'(Cartes sur le board|Board)\s*:\s*',
            r'<span style="color: #FFAA00; font-weight: bold;">\1:</span> ',
            colored_analysis,
            flags=re.IGNORECASE
        )
        
        return colored_analysis
    
    # Exemples d'analyses à tester
    analyses_test = [
        {
            "nom": "Cartes avec symboles",
            "contenu": """
📊 Analyse détaillée:
• Main actuelle : Paire d'As
• Cartes en main: A♥ K♠
• Cartes sur le board: Q♦ J♣ 10♥
• Équité estimée: 75%
            """,
            "attendu": "Rouge pour ♥, Noir pour ♠, Bleu pour ♦, Vert pour ♣"
        },
        {
            "nom": "Cartes format français",
            "contenu": """
📊 Analyse détaillée:
• Main actuelle : Paire de Rois
• Cartes en main: K de cœur, Q de pique
• Cartes sur le board: J de carreau, 10 de trèfle, 9 de cœur
• Équité estimée: 65%
            """,
            "attendu": "Rouge pour cœur, Noir pour pique, Bleu pour carreau, Vert pour trèfle"
        },
        {
            "nom": "Analyse complète",
            "contenu": """
### Situation (6 régions détectées, 1 corrections manuelles)
Main 1 : A de cœur, K de pique

📊 Analyse détaillée:
• Main actuelle : As haut
• Équité estimée vs range (70-85%) : ~77.5%
• Pot odds : 25.0%   Cote implicite : Favorable
• Montant recommandé : 45.0 BB (relance)

Board: Q de carreau, J de trèfle, 10 de cœur
            """,
            "attendu": "Toutes les couleurs correctement appliquées"
        }
    ]
    
    print("🎮 Test de colorisation :")
    print("-" * 40)
    
    for test in analyses_test:
        print(f"\n📋 Test: {test['nom']}")
        print(f"   Attendu: {test['attendu']}")
        
        # Appliquer la colorisation
        colored_result = add_card_colors_to_analysis(test['contenu'])
        
        # Vérifier que les couleurs sont appliquées
        checks = [
            ('#FF0000', 'Rouge (cœur)'),
            ('#000000', 'Noir (pique)'),
            ('#0066FF', 'Bleu (carreau)'),
            ('#00AA00', 'Vert (trèfle)')
        ]
        
        colors_found = []
        for color_code, color_name in checks:
            if color_code in colored_result:
                colors_found.append(color_name)
        
        if colors_found:
            print(f"   ✅ Couleurs appliquées: {', '.join(colors_found)}")
        else:
            print(f"   ❌ Aucune couleur détectée")
        
        # Vérifier les améliorations d'affichage
        improvements = [
            ('📊 Analyse détaillée:', 'Section analyse colorée'),
            ('Cartes en main:', 'Titre cartes en main coloré'),
            ('Cartes sur le board:', 'Titre board coloré')
        ]
        
        improvements_found = []
        for pattern, description in improvements:
            if f'<span style="color:' in colored_result and pattern.replace(':', '') in colored_result:
                improvements_found.append(description)
        
        if improvements_found:
            print(f"   ✅ Améliorations: {', '.join(improvements_found)}")

def test_couleurs_selon_specifications():
    """Test selon les spécifications demandées"""
    print("\n🎯 TEST SELON SPÉCIFICATIONS")
    print("=" * 50)
    print("Couleurs demandées :")
    print("• ❤️ Rouge pour cœur")
    print("• ♠️ Noir pour pique") 
    print("• 💙 Bleu pour carreau")
    print("• 💚 Vert pour trèfle")
    print()
    
    # Codes couleur utilisés
    color_specs = {
        "cœur": "#FF0000",    # Rouge
        "pique": "#000000",   # Noir
        "carreau": "#0066FF", # Bleu
        "trèfle": "#00AA00"   # Vert
    }
    
    print("🎨 Codes couleur implémentés :")
    for suit, color in color_specs.items():
        print(f"   {suit}: {color}")
    
    print("\n✅ Correspondance avec les spécifications :")
    print("   ✅ Rouge pour cœur - Correct")
    print("   ✅ Noir pour pique - Correct") 
    print("   ✅ Bleu pour carreau - Correct")
    print("   ✅ Vert pour trèfle - Correct")

def main():
    """Fonction principale de test"""
    print("🎨 TEST COMPLET DES COULEURS DES CARTES")
    print("=" * 60)
    print("Vérification que les cartes sont colorées dans l'analyse détaillée")
    print()
    
    # Test de colorisation
    test_colorisation_cartes()
    
    # Test selon spécifications
    test_couleurs_selon_specifications()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ")
    print("-" * 60)
    print("✅ Interface simplifiée supprimée")
    print("✅ Gestion du bouton dealer conservée")
    print("✅ Couleurs des cartes ajoutées à l'analyse détaillée")
    print("✅ Couleurs selon spécifications :")
    print("   • Rouge pour cœur ♥")
    print("   • Noir pour pique ♠")
    print("   • Bleu pour carreau ♦")
    print("   • Vert pour trèfle ♣")
    print()
    print("🚀 PRÊT À UTILISER :")
    print("   1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("   2. Activez le conseiller poker")
    print("   3. Observez l'analyse détaillée avec cartes colorées")
    print("   4. Utilisez les boutons de gestion du dealer")

if __name__ == "__main__":
    main()
