#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test complet du conseiller poker avec interface simplifiée
Vérifie : montants, position du bouton, logique avancée
"""

import sys
import os
import time
import json

def create_complete_test_data():
    """Crée des données de test complètes avec tous les éléments"""
    
    # Situation 1: FOLD - Main faible, grosse mise à suivre
    fold_situation = {
        # Cartes
        "card_1": {"text": "A", "colors": ["rouge"]},  # A♥
        "card_2": {"text": "K", "colors": ["noir"]},   # K♠
        "card_3": {"text": "Q", "colors": ["rouge"]},  # Q♦
        "carte_1m": {"text": "2", "colors": ["vert"]}, # 2♣
        "carte_2m": {"text": "7", "colors": ["rouge"]}, # 7♦
        
        # Jetons et mises
        "mes_jetons": {"text": "150"},
        "jetons_joueur1": {"text": "200"},
        "jetons_joueur2": {"text": "180"},
        "jetons_joueur3": {"text": "220"},
        
        # Mises (grosse mise à suivre)
        "mise_joueur1": {"text": "50"},  # Grosse mise
        "mise_joueur2": {"text": "0"},
        "mise_joueur3": {"text": "25"},
        
        # Pot
        "pot_total": {"text": "120"},
        
        # Montants spécifiques
        "montant_call": {"text": "50"},  # Montant à suivre
        "montant_relance": {"text": "100"},  # Montant de relance suggéré
        
        # Position du bouton (sera définie manuellement)
        "button_position": "joueur2"
    }
    
    # Situation 2: CALL - Main moyenne, pot odds favorables
    call_situation = {
        # Cartes
        "card_1": {"text": "2", "colors": ["rouge"]},  # 2♥
        "card_2": {"text": "5", "colors": ["vert"]},   # 5♣
        "card_3": {"text": "K", "colors": ["noir"]},   # K♠
        "carte_1m": {"text": "8", "colors": ["rouge"]}, # 8♥
        "carte_2m": {"text": "8", "colors": ["noir"]},  # 8♠
        
        # Jetons et mises
        "mes_jetons": {"text": "180"},
        "jetons_joueur1": {"text": "150"},
        "jetons_joueur2": {"text": "220"},
        "jetons_joueur3": {"text": "190"},
        
        # Mises (mise raisonnable)
        "mise_joueur1": {"text": "20"},  # Mise modérée
        "mise_joueur2": {"text": "0"},
        "mise_joueur3": {"text": "10"},
        
        # Pot
        "pot_total": {"text": "100"},
        
        # Montants spécifiques
        "montant_call": {"text": "20"},  # Montant à suivre
        "montant_relance": {"text": "60"},  # Montant de relance suggéré
        
        # Position du bouton
        "button_position": "joueur3"
    }
    
    # Situation 3: RAISE - Main forte, position favorable
    raise_situation = {
        # Cartes
        "card_1": {"text": "2", "colors": ["rouge"]},  # 2♥
        "card_2": {"text": "7", "colors": ["vert"]},   # 7♣
        "card_3": {"text": "9", "colors": ["noir"]},   # 9♠
        "carte_1m": {"text": "A", "colors": ["noir"]},  # A♠
        "carte_2m": {"text": "A", "colors": ["rouge"]}, # A♥
        
        # Jetons et mises
        "mes_jetons": {"text": "280"},
        "jetons_joueur1": {"text": "250"},
        "jetons_joueur2": {"text": "320"},
        "jetons_joueur3": {"text": "200"},
        
        # Mises (petites mises, opportunité de relance)
        "mise_joueur1": {"text": "15"},  # Petite mise
        "mise_joueur2": {"text": "30"},  # Relance modérée
        "mise_joueur3": {"text": "0"},
        
        # Pot
        "pot_total": {"text": "80"},
        
        # Montants spécifiques
        "montant_call": {"text": "30"},  # Montant à suivre
        "montant_relance": {"text": "90"},  # Montant de relance suggéré
        
        # Position du bouton
        "button_position": "joueur1"
    }
    
    # Situation 4: ALL-IN - Main monstre, petit tapis
    allin_situation = {
        # Cartes
        "card_1": {"text": "K", "colors": ["noir"]},   # K♠
        "card_2": {"text": "2", "colors": ["vert"]},   # 2♣
        "card_3": {"text": "7", "colors": ["rouge"]},  # 7♦
        "card_4": {"text": "8", "colors": ["rouge"]},  # 8♥
        "carte_1m": {"text": "K", "colors": ["rouge"]}, # K♥
        "carte_2m": {"text": "K", "colors": ["rouge"]}, # K♦ (brelan de rois)
        
        # Jetons et mises (petit tapis)
        "mes_jetons": {"text": "80"},   # Petit tapis
        "jetons_joueur1": {"text": "120"},
        "jetons_joueur2": {"text": "90"},
        "jetons_joueur3": {"text": "150"},
        
        # Mises (all-in détecté)
        "mise_joueur1": {"text": "40"},
        "mise_joueur2": {"text": "80", "colors": ["rouge"]},  # All-in (rouge)
        "mise_joueur3": {"text": "0"},
        
        # Pot
        "pot_total": {"text": "200"},
        
        # Montants spécifiques
        "montant_call": {"text": "80"},  # Montant à suivre (all-in)
        "montant_relance": {"text": "80"},  # Mon all-in
        
        # Position du bouton
        "button_position": "joueur4"
    }
    
    return {
        "fold": fold_situation,
        "call": call_situation,
        "raise": raise_situation,
        "allin": allin_situation
    }

def test_conseiller_complet():
    """Test complet du conseiller avec toutes les fonctionnalités"""
    print("🎯 TEST COMPLET DU CONSEILLER POKER")
    print("=" * 60)
    print("✅ Vérification de :")
    print("   - Interface simplifiée avec montants")
    print("   - Gestion manuelle du bouton dealer")
    print("   - Intégration dans la logique avancée")
    print("   - Recommandations avec montants précis")
    print("   - Prise en compte de tous les éléments")
    print()
    
    # Créer les données de test
    test_data = create_complete_test_data()
    
    print("📋 SITUATIONS DE TEST CRÉÉES :")
    print("-" * 40)
    
    for situation_name, data in test_data.items():
        print(f"\n🎮 {situation_name.upper()} :")
        
        # Afficher les informations clés
        mes_jetons = data.get("mes_jetons", {}).get("text", "0")
        pot_total = data.get("pot_total", {}).get("text", "0")
        montant_call = data.get("montant_call", {}).get("text", "0")
        montant_relance = data.get("montant_relance", {}).get("text", "0")
        button_pos = data.get("button_position", "joueur1")
        
        print(f"   💰 Mes jetons: {mes_jetons} BB")
        print(f"   🏆 Pot total: {pot_total} BB")
        print(f"   📞 À suivre: {montant_call} BB")
        print(f"   ⬆️ Relance suggérée: {montant_relance} BB")
        print(f"   🔘 Bouton: {button_pos}")
        
        # Cartes
        carte1m = data.get("carte_1m", {}).get("text", "?")
        carte2m = data.get("carte_2m", {}).get("text", "?")
        card1 = data.get("card_1", {}).get("text", "?")
        card2 = data.get("card_2", {}).get("text", "?")
        card3 = data.get("card_3", {}).get("text", "?")
        
        print(f"   🃏 Main: {carte1m} {carte2m}")
        print(f"   🃏 Board: {card1} {card2} {card3}")
        
        # Analyser la situation attendue
        if situation_name == "fold":
            print(f"   🎯 Attendu: 🚫 FOLD (main faible, grosse mise {montant_call}BB)")
        elif situation_name == "call":
            print(f"   🎯 Attendu: 📞 CALL {montant_call}BB (pot odds favorables)")
        elif situation_name == "raise":
            print(f"   🎯 Attendu: ⬆️ RAISE {montant_relance}BB (main forte)")
        elif situation_name == "allin":
            print(f"   🎯 Attendu: 💰 ALL-IN {montant_relance}BB (main monstre)")
    
    print("\n" + "=" * 60)
    print("🚀 INSTRUCTIONS POUR TESTER :")
    print("-" * 40)
    print("1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("2. Activez le conseiller poker")
    print("3. Utilisez ces données pour tester chaque situation")
    print("4. Vérifiez que :")
    print("   ✅ Les montants sont affichés (ex: 📞 CALL 20BB)")
    print("   ✅ Le bouton dealer peut être déplacé manuellement")
    print("   ✅ La position influence les recommandations")
    print("   ✅ Les gros montants déclenchent FOLD")
    print("   ✅ Les bonnes mains déclenchent RAISE/ALL-IN")
    print()
    
    # Sauvegarder les données de test
    export_dir = r"C:\Users\<USER>\PokerAdvisor\Détection des regions\export"
    os.makedirs(export_dir, exist_ok=True)
    
    for situation_name, data in test_data.items():
        # Convertir en format texte pour le conseiller
        content_lines = []
        
        # Ajouter les cartes
        for key, value in data.items():
            if isinstance(value, dict) and "text" in value:
                content_lines.append(f"{key}: {value['text']}")
        
        content = "\n".join(content_lines)
        
        # Sauvegarder
        file_path = os.path.join(export_dir, f"test_{situation_name}.txt")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"💾 Données {situation_name} sauvées: {file_path}")
    
    print("\n✅ Test complet préparé !")
    print("🎯 Le conseiller prend maintenant en compte :")
    print("   - Position manuelle du bouton dealer")
    print("   - Montants précis de call/raise")
    print("   - Tous les jetons et mises détectés")
    print("   - Logique avancée complète")
    print("   - Interface simplifiée avec montants")

if __name__ == "__main__":
    test_conseiller_complet()
