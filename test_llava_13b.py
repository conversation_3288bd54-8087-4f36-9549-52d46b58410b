#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 TEST SPÉCIALISÉ LLAVA 13B
Test optimisé pour l'analyse précise de votre capture poker
"""

import subprocess
import time
import json
import base64
import os
import requests

class TestLLaVA13B:
    def __init__(self):
        self.modele = "llava:13b"
        self.image_path = "capture_poker.png"
        
    def verifier_ollama(self) -> bool:
        """Vérifie si Ollama fonctionne"""
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False
            
    def encoder_image_base64(self, image_path: str) -> str:
        """Encode l'image en base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except FileNotFoundError:
            print(f"❌ Image non trouvée: {image_path}")
            return None

    def creer_prompt_ultra_precis(self) -> str:
        """Prompt ultra-précis pour LLaVA 13B"""
        return """🔍 ANALYSE OCR ULTRA-PRÉCISE - TABLE DE POKER

⚠️ RÈGLES STRICTES :
1. LIS EXACTEMENT le texte visible - NE PAS INVENTER
2. Si tu ne vois pas clairement, écris "NON VISIBLE"
3. Utilise le format exact demandé

📖 **LECTURE OBLIGATOIRE** :

🃏 **CARTES COMMUNES (centre table)** :
- Compte : [nombre] cartes visibles
- Carte 1 : [valeur][couleur] (ex: 6♠)
- Carte 2 : [valeur][couleur] 
- Carte 3 : [valeur][couleur]
- Carte 4 : [valeur][couleur]
- Carte 5 : [valeur][couleur]

💰 **MONTANTS (lis les chiffres exacts)** :
- Pot central : [X,X] BB
- Joueur haut : [nom] - [X,X] BB
- Joueur droite : [nom] - [X,X] BB  
- Joueur bas droite : [nom] - [X,X] BB
- Joueur bas : [nom] - [X,X] BB
- Joueur gauche : [nom] - [X,X] BB
- Joueur haut gauche : [nom] - [X,X] BB

👥 **STATUTS VISIBLES** :
- Qui a "SUIT" affiché ?
- Qui a "ALL-IN" affiché ?
- Autres statuts visibles ?

🎯 **ANALYSE TECHNIQUE** :
- Phase : [preflop/flop/turn/river]
- Board : [type de texture]
- Mains fortes possibles : [liste]

RÉPONDS EN FRANÇAIS - FORMAT EXACT DEMANDÉ"""

    def installer_llava_13b(self) -> bool:
        """Installe LLaVA 13B si nécessaire"""
        print(f"🔍 Vérification de {self.modele}...")
        
        # Vérifier si installé
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if self.modele in result.stdout:
                print(f"✅ {self.modele} déjà installé")
                return True
        except:
            pass
        
        # Proposer l'installation
        print(f"📥 {self.modele} non trouvé")
        print("⚠️  Attention: LLaVA 13B fait ~7GB")
        response = input("Installer maintenant ? (o/N): ")
        
        if response.lower() in ['o', 'oui']:
            print(f"📦 Installation de {self.modele}...")
            print("⏳ Cela peut prendre 10-15 minutes...")
            
            try:
                subprocess.run(['ollama', 'pull', self.modele], check=True)
                print(f"✅ {self.modele} installé avec succès")
                return True
            except:
                print(f"❌ Erreur installation {self.modele}")
                return False
        
        return False

    def tester_llava_13b(self) -> dict:
        """Test complet de LLaVA 13B"""
        print(f"🧪 TEST LLAVA 13B - ANALYSE POKER")
        print("-" * 50)
        
        if not os.path.exists(self.image_path):
            return {
                "succes": False,
                "erreur": f"Image {self.image_path} non trouvée"
            }
        
        # Encoder l'image
        image_b64 = self.encoder_image_base64(self.image_path)
        if not image_b64:
            return {
                "succes": False,
                "erreur": "Erreur encodage image"
            }
        
        try:
            start_time = time.time()
            
            # Requête optimisée pour LLaVA 13B
            url = "http://localhost:11434/api/generate"
            payload = {
                "model": self.modele,
                "prompt": self.creer_prompt_ultra_precis(),
                "images": [image_b64],
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Plus précis
                    "top_p": 0.9,
                    "repeat_penalty": 1.1
                }
            }
            
            print("🔄 Analyse en cours...")
            response = requests.post(url, json=payload, timeout=180)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                reponse_text = result.get("response", "")
                
                return {
                    "succes": True,
                    "reponse": reponse_text,
                    "duree": duration,
                    "longueur": len(reponse_text),
                    "modele": self.modele
                }
            else:
                return {
                    "succes": False,
                    "erreur": f"HTTP {response.status_code}: {response.text}",
                    "duree": duration
                }
                
        except requests.exceptions.Timeout:
            return {
                "succes": False,
                "erreur": "Timeout (>180s)",
                "duree": 180
            }
        except Exception as e:
            return {
                "succes": False,
                "erreur": str(e),
                "duree": time.time() - start_time if 'start_time' in locals() else 0
            }

    def analyser_resultats(self, resultat: dict):
        """Analyse détaillée des résultats"""
        print("\n📊 ANALYSE DÉTAILLÉE LLAVA 13B")
        print("=" * 60)
        
        if not resultat["succes"]:
            print(f"❌ Échec: {resultat['erreur']}")
            return
        
        print(f"✅ Test réussi en {resultat['duree']:.1f}s")
        print(f"📝 Réponse: {resultat['longueur']} caractères")
        print(f"⚡ Vitesse: {resultat['longueur'] / resultat['duree']:.0f} car/s")
        
        print(f"\n📋 RÉPONSE COMPLÈTE:")
        print("-" * 50)
        print(resultat['reponse'])
        
        # Analyse de la qualité
        reponse = resultat['reponse'].lower()
        
        print(f"\n🎯 ÉVALUATION QUALITÉ:")
        print("-" * 30)
        
        # Vérifications spécifiques
        checks = {
            "Cartes détectées": any(x in reponse for x in ['♠', '♥', '♦', '♣', 'pique', 'coeur', 'carreau', 'trefle']),
            "Montants BB": 'bb' in reponse,
            "Noms joueurs": any(x in reponse for x in ['sucre', 'metra', 'fred', 'risk', 'drxg', 'cuervvo']),
            "Pot central": 'pot' in reponse,
            "Phase identifiée": any(x in reponse for x in ['flop', 'turn', 'river', 'preflop']),
            "Analyse stratégique": any(x in reponse for x in ['paire', 'brelan', 'quinte', 'full'])
        }
        
        for critere, detecte in checks.items():
            status = "✅" if detecte else "❌"
            print(f"{status} {critere}")
        
        score = sum(checks.values()) / len(checks) * 100
        print(f"\n🏆 SCORE GLOBAL: {score:.0f}%")
        
        if score >= 80:
            print("🎉 Excellente performance !")
        elif score >= 60:
            print("👍 Bonne performance")
        else:
            print("⚠️ Performance à améliorer")

    def sauvegarder_resultats(self, resultat: dict):
        """Sauvegarde les résultats"""
        filename = f"llava_13b_test_{int(time.time())}.json"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(resultat, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Résultats sauvés: {filename}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")

def main():
    """Fonction principale"""
    print("🎯 TEST SPÉCIALISÉ LLAVA 13B")
    print("Analyse ultra-précise de votre capture poker")
    print("=" * 60)
    
    # Vérifier Ollama
    tester = TestLLaVA13B()
    if not tester.verifier_ollama():
        print("❌ Ollama non disponible")
        print("💡 Installez depuis ollama.ai")
        return
    
    # Installer LLaVA 13B si nécessaire
    if not tester.installer_llava_13b():
        print("❌ LLaVA 13B non disponible")
        return
    
    # Vérifier l'image
    if not os.path.exists(tester.image_path):
        print(f"❌ Image {tester.image_path} non trouvée")
        print("💡 Lancez d'abord: python sauvegarder_capture.py")
        return
    
    print(f"\n🚀 LANCEMENT DU TEST")
    print(f"📸 Image: {tester.image_path}")
    print(f"🤖 Modèle: {tester.modele}")
    print()
    
    # Exécuter le test
    resultat = tester.tester_llava_13b()
    
    # Analyser les résultats
    tester.analyser_resultats(resultat)
    
    # Sauvegarder
    if resultat["succes"]:
        tester.sauvegarder_resultats(resultat)
    
    print("\n💡 RECOMMANDATIONS:")
    print("-" * 30)
    if resultat["succes"]:
        print("✅ Comparez avec votre détection actuelle")
        print("🔄 Testez avec d'autres captures")
        print("🎯 Considérez l'approche hybride (OCR + IA)")
    else:
        print("🔧 Vérifiez la configuration Ollama")
        print("📸 Vérifiez la qualité de l'image")
    
    print("\n🎉 Test terminé !")

if __name__ == "__main__":
    main()
