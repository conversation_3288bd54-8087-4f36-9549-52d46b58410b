#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'interface simplifiée du conseiller poker
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poker_advisor_app import PokerAdvisorApp
from PyQt5.QtWidgets import QApplication

def create_test_data():
    """Crée des données de test pour l'interface"""
    test_content = """Cartes en main: A♥ K♠
Cartes sur le board: Q♦ J♣ 10♠
Probabilité de gagner: 85.5%
Action recommandée: value-bet 3/4 pot
Pot: 50
Pot total: 75
Tapis effectif: 200
Mes jetons: 180
Jetons joueur1: 150
Jetons joueur2: 220
Jetons joueur3: 180
Mise joueur1: 10
Mise joueur2: 0
Mise joueur3: 25
"""
    return test_content

def test_interface():
    """Test de l'interface simplifiée"""
    app = QApplication(sys.argv)
    
    # Créer l'application
    poker_app = PokerAdvisorApp()
    
    # Simuler des données de test
    test_data = create_test_data()
    
    # Traiter les données de test
    poker_app.process_file_content(test_data)
    
    # Afficher l'interface
    poker_app.show()
    
    print("🎯 Interface simplifiée du conseiller poker lancée")
    print("✅ Fonctionnalités testées :")
    print("   - Affichage simplifié des recommandations (FOLD/CALL/RAISE)")
    print("   - Gestion manuelle du bouton dealer")
    print("   - Positions des joueurs sur table de 6")
    print("   - Boutons de navigation (Précédent/Suivant)")
    print("   - Conservation de la logique avancée existante")
    print("\n🔘 Testez les boutons de déplacement du dealer !")
    print("⬅️ Bouton 'Précédent' pour déplacer le dealer")
    print("➡️ Bouton 'Suivant' pour déplacer le dealer")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(test_interface())
