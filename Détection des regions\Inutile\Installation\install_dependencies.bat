@echo off
echo ===================================================
echo Installation des dependances pour Poker Advisor
echo ===================================================
echo.

echo Verification de Python...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo Python n'est pas installe ou n'est pas dans le PATH.
    echo Veuillez installer Python 3.8 ou superieur.
    pause
    exit /b 1
)

echo.
echo Verification de la version de Python...
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set pyver=%%i
echo Version de Python: %pyver%

echo.
echo Desinstallation de PyTorch si deja installe...
pip uninstall -y torch torchvision torchaudio

echo.
echo Installation de PyTorch avec support CUDA...
echo Cette etape peut prendre quelques minutes...

REM Installer PyTorch avec CUDA 11.8 pour Python 3.11
pip install torch==2.0.1+cu118 torchvision==0.15.2+cu118 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118

if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors de l'installation de PyTorch avec CUDA 11.8.
    echo Tentative avec CUDA 11.7...
    pip install torch==2.0.1+cu117 torchvision==0.15.2+cu117 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu117

    if %ERRORLEVEL% NEQ 0 (
        echo Erreur lors de l'installation de PyTorch avec CUDA 11.7.
        echo Installation de PyTorch sans CUDA...
        pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2
    )
)

echo.
echo Verification de l'installation de PyTorch...
python -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA disponible: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda if torch.cuda.is_available() else \"Non disponible\"}')"

echo.
echo Installation de PaddlePaddle...
pip uninstall -y paddlepaddle paddleocr
pip install paddlepaddle==2.5.2
if %ERRORLEVEL% NEQ 0 (
    echo Tentative avec une autre version de PaddlePaddle...
    pip install paddlepaddle==2.6.0
    if %ERRORLEVEL% NEQ 0 (
        echo Erreur lors de l'installation de PaddlePaddle.
        echo Installation de la dernière version...
        pip install paddlepaddle
    )
)

echo.
echo Installation des autres packages requis...
pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors de l'installation des packages.
    echo Tentative d'installation individuelle...
    pip install opencv-python==********
    pip install numpy==1.24.3
    pip install paddleocr==*******
    pip install PyQt5==5.15.9
)

echo.
echo Verification de CUDA pour PaddleOCR...
python -c "import torch; print(f'CUDA disponible: {torch.cuda.is_available()}')"
set USE_GPU=False
if %ERRORLEVEL% EQU 0 (
    python -c "import torch; use_gpu = torch.cuda.is_available(); print('True' if use_gpu else 'False')" > temp.txt
    set /p USE_GPU=<temp.txt
    del temp.txt
)

echo.
echo Telechargement des modeles PaddleOCR (GPU: %USE_GPU%)...
python -c "from paddleocr import PaddleOCR; print('Initialisation de PaddleOCR...'); ocr = PaddleOCR(use_angle_cls=False, lang='en', use_gpu=False, show_log=True, download_model=True)"
if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors du telechargement des modeles PaddleOCR.
    echo Tentative avec des parametres minimaux et sans telechargement...
    python -c "from paddleocr import PaddleOCR; print('Initialisation de PaddleOCR sans telechargement...'); ocr = PaddleOCR(use_angle_cls=False, lang='en', use_gpu=False, show_log=False, download_model=False)"
)

echo.
echo Test de PaddleOCR...
python -c "from paddleocr import PaddleOCR; print('Test de PaddleOCR...'); ocr = PaddleOCR(use_angle_cls=False, lang='en', use_gpu=False, show_log=False); print('PaddleOCR fonctionne correctement!')"
if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors du test de PaddleOCR.
    echo Veuillez verifier l'installation manuellement.
)

echo.
echo Creation des dossiers necessaires...
if not exist config mkdir config
if not exist screenshots mkdir screenshots

echo.
echo Installation terminee avec succes!
echo Vous pouvez maintenant lancer l'application avec run_detector.bat
echo.
pause
