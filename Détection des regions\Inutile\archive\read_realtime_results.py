#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pour lire les résultats en temps réel exportés par detector_gui.py

Ce script peut être utilisé par une autre application pour lire les résultats
de détection en temps réel.
"""

import os
import json
import time
import sys

def read_realtime_results(file_path="export/realtime_results.json"):
    """Lit les résultats en temps réel depuis un fichier JSON
    
    Args:
        file_path: Chemin du fichier JSON contenant les résultats
        
    Returns:
        Un dictionnaire contenant les résultats, ou None si le fichier n'existe pas
        ou si une erreur se produit
    """
    try:
        if not os.path.exists(file_path):
            print(f"Le fichier {file_path} n'existe pas.")
            return None
            
        # Vérifier l'âge du fichier (ne pas lire s'il est trop ancien)
        file_age = time.time() - os.path.getmtime(file_path)
        if file_age > 10:  # Plus de 10 secondes
            print(f"Le fichier {file_path} est trop ancien ({file_age:.1f} secondes).")
            return None
            
        # Lire le fichier JSON
        with open(file_path, 'r') as f:
            data = json.load(f)
            
        return data
        
    except Exception as e:
        print(f"Erreur lors de la lecture des résultats: {e}")
        return None

def extract_cards(results):
    """Extrait les cartes détectées des résultats
    
    Args:
        results: Dictionnaire contenant les résultats de la détection
        
    Returns:
        Un dictionnaire contenant les cartes détectées par région
    """
    if not results or "results" not in results:
        return {}
        
    cards = {}
    for region_name, region_data in results["results"].items():
        if region_name.startswith("card_") and region_data.get("text"):
            cards[region_name] = region_data["text"]
            
    return cards

def extract_hand_cards(results):
    """Extrait les cartes en main détectées des résultats
    
    Args:
        results: Dictionnaire contenant les résultats de la détection
        
    Returns:
        Une liste contenant les cartes en main détectées
    """
    if not results or "results" not in results:
        return []
        
    hand_cards = []
    for region_name, region_data in results["results"].items():
        if region_name.startswith("hand_card_") and region_data.get("text"):
            hand_cards.append(region_data["text"])
            
    return hand_cards

def main():
    """Fonction principale"""
    print("Lecture des résultats en temps réel...")
    print("Appuyez sur Ctrl+C pour quitter.")
    
    try:
        while True:
            # Lire les résultats
            results = read_realtime_results()
            
            if results:
                print("\n" + "="*50)
                print(f"Résultats du {results['datetime']}:")
                
                # Extraire les cartes
                cards = extract_cards(results)
                if cards:
                    print("\nCartes du board:")
                    for name, card in cards.items():
                        print(f"  {name}: {card}")
                
                # Extraire les cartes en main
                hand_cards = extract_hand_cards(results)
                if hand_cards:
                    print("\nCartes en main:")
                    for card in hand_cards:
                        print(f"  {card}")
                
                print("="*50)
            
            # Attendre avant la prochaine lecture
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nArrêt de la lecture des résultats.")
        
if __name__ == "__main__":
    main()
