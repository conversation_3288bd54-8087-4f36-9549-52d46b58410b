#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide des améliorations de détection
==========================================

Script de test rapide pour vérifier que les améliorations fonctionnent
avec les fichiers existants de l'application.

Auteur: Augment Agent
Date: 2024
"""

import os
import sys
import json
import numpy as np
import cv2

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import_detector():
    """Test d'importation du détecteur amélioré"""
    print("🔍 Test d'importation du détecteur...")
    
    try:
        from detector import Detector
        print("✅ Détecteur importé avec succès")
        
        # Vérifier que les nouvelles méthodes existent
        detector = Detector()
        
        # Vérifier les nouvelles méthodes
        methods_to_check = [
            'detect_colors_button',
            'detect_player_name',
            '_preprocess_for_player_name',
            '_is_valid_player_name'
        ]
        
        for method in methods_to_check:
            if hasattr(detector, method):
                print(f"✅ Méthode {method} disponible")
            else:
                print(f"❌ Méthode {method} manquante")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'importation: {e}")
        return False

def test_config_existante():
    """Test avec la configuration existante"""
    print("\n🔧 Test avec configuration existante...")
    
    # Chercher les configurations existantes
    config_paths = [
        os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'),
        'calibration_config.json'
    ]
    
    config_found = None
    for path in config_paths:
        if os.path.exists(path):
            config_found = path
            break
    
    if not config_found:
        print("⚠️ Aucune configuration existante trouvée")
        return True  # Pas d'erreur, juste pas de config
    
    try:
        from detector import Detector
        
        print(f"📁 Configuration trouvée: {config_found}")
        detector = Detector(config_path=config_found)
        
        # Charger la configuration pour voir les régions
        with open(config_found, 'r') as f:
            config = json.load(f)
        
        # Compter les régions par type
        all_regions = config.get('all_regions', config.get('regions', {}))
        
        bouton_regions = [name for name in all_regions.keys() if name.startswith('bouton_')]
        pseudo_regions = [name for name in all_regions.keys() if name.startswith('pseudo_')]
        
        print(f"🔘 Régions boutons trouvées: {len(bouton_regions)}")
        for region in bouton_regions[:3]:  # Afficher les 3 premières
            print(f"   - {region}")
        if len(bouton_regions) > 3:
            print(f"   ... et {len(bouton_regions) - 3} autres")
        
        print(f"👤 Régions pseudos trouvées: {len(pseudo_regions)}")
        for region in pseudo_regions[:3]:  # Afficher les 3 premières
            print(f"   - {region}")
        if len(pseudo_regions) > 3:
            print(f"   ... et {len(pseudo_regions) - 3} autres")
        
        print("✅ Configuration chargée avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de configuration: {e}")
        return False

def test_detection_basique():
    """Test de détection basique avec images synthétiques"""
    print("\n🧪 Test de détection basique...")
    
    try:
        from detector import Detector
        detector = Detector()
        
        # Test 1: Détection bouton avec orange
        print("🔘 Test détection bouton orange...")
        orange_img = np.zeros((30, 30, 3), dtype=np.uint8)
        orange_img[:, :] = [0, 165, 255]  # Orange en BGR
        
        colors = detector.detect_colors_button(orange_img)
        print(f"   Couleurs détectées: {colors}")
        
        # Test 2: Détection bouton avec blanc
        print("🔘 Test détection bouton blanc...")
        white_img = np.zeros((30, 30, 3), dtype=np.uint8)
        white_img[:, :] = [255, 255, 255]  # Blanc
        
        colors = detector.detect_colors_button(white_img)
        print(f"   Couleurs détectées: {colors}")
        
        # Test 3: Validation pseudos
        print("👤 Test validation pseudos...")
        test_pseudos = ["Player123", "AB", "ValidUser", "FOLD", "Test_User"]
        
        for pseudo in test_pseudos:
            is_valid = detector._is_valid_player_name(pseudo)
            status = "✅" if is_valid else "❌"
            print(f"   {status} '{pseudo}': {is_valid}")
        
        print("✅ Tests de détection basique réussis")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests basiques: {e}")
        return False

def test_integration_poker_advisor():
    """Test d'intégration avec les fichiers poker advisor existants"""
    print("\n🎮 Test d'intégration avec poker advisor...")
    
    try:
        # Vérifier si les fichiers poker advisor existent
        poker_files = [
            'poker_advisor_light.py',
            'poker_advisor_integration.py'
        ]
        
        found_files = []
        for file in poker_files:
            if os.path.exists(file):
                found_files.append(file)
        
        if not found_files:
            print("⚠️ Aucun fichier poker advisor trouvé")
            return True  # Pas d'erreur, juste pas de fichiers
        
        print(f"📁 Fichiers poker advisor trouvés: {found_files}")
        
        # Essayer d'importer et vérifier la compatibilité
        for file in found_files:
            try:
                module_name = file.replace('.py', '')
                print(f"🔍 Test d'importation: {module_name}")
                
                # Import dynamique pour test
                spec = __import__(module_name)
                print(f"✅ {module_name} importé avec succès")
                
            except Exception as e:
                print(f"⚠️ Erreur lors de l'importation de {module_name}: {e}")
        
        print("✅ Tests d'intégration terminés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors des tests d'intégration: {e}")
        return False

def test_performance():
    """Test de performance basique"""
    print("\n⚡ Test de performance...")
    
    try:
        from detector import Detector
        import time
        
        detector = Detector()
        
        # Créer une image de test
        test_img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # Test de vitesse détection bouton
        start_time = time.time()
        for _ in range(10):
            colors = detector.detect_colors_button(test_img)
        button_time = (time.time() - start_time) / 10
        
        # Test de vitesse validation pseudo
        start_time = time.time()
        for _ in range(100):
            is_valid = detector._is_valid_player_name("TestPlayer123")
        pseudo_time = (time.time() - start_time) / 100
        
        print(f"⏱️ Temps moyen détection bouton: {button_time*1000:.2f}ms")
        print(f"⏱️ Temps moyen validation pseudo: {pseudo_time*1000:.2f}ms")
        
        # Vérifier que les temps sont raisonnables
        if button_time < 0.1 and pseudo_time < 0.001:
            print("✅ Performance acceptable")
            return True
        else:
            print("⚠️ Performance plus lente que prévu")
            return True  # Pas d'échec, juste un avertissement
        
    except Exception as e:
        print(f"❌ Erreur lors du test de performance: {e}")
        return False

def main():
    """Fonction principale de test rapide"""
    print("🚀 TESTS RAPIDES DES AMÉLIORATIONS")
    print("="*50)
    
    tests = [
        ("Import détecteur", test_import_detector),
        ("Configuration existante", test_config_existante),
        ("Détection basique", test_detection_basique),
        ("Intégration poker advisor", test_integration_poker_advisor),
        ("Performance", test_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur inattendue dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "="*50)
    print("📊 RÉSUMÉ DES TESTS RAPIDES")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Score: {passed}/{total}")
    
    if passed == total:
        print("🎉 Tous les tests rapides sont passés!")
        print("\n💡 Les améliorations sont opérationnelles:")
        print("   ✅ Détection boutons avec orange affiné")
        print("   ✅ Détection pseudos avec validation")
        print("   ✅ Intégration dans le détecteur principal")
        print("\n🚀 Vous pouvez maintenant utiliser les nouvelles fonctionnalités!")
    else:
        print(f"⚠️ {total - passed} test(s) ont échoué")
        print("🔧 Vérifiez les messages d'erreur ci-dessus")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    if success:
        print("✅ Tests rapides terminés avec succès")
    else:
        print("❌ Certains tests ont échoué")
    print("="*50)
    
    sys.exit(0 if success else 1)
