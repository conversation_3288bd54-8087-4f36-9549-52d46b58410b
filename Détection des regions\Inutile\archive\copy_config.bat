@echo off
echo ===================================================
echo Copie du fichier de configuration
echo ===================================================
echo.

set SOURCE="C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
set DEST="C:\Users\<USER>\PokerAdvisor\Détection des regions\config\poker_advisor_config.json"

echo Source: %SOURCE%
echo Destination: %DEST%
echo.

if not exist %SOURCE% (
    echo ERREUR: Le fichier source n'existe pas!
    goto end
)

echo Copie du fichier en cours...
copy %SOURCE% %DEST%

if %ERRORLEVEL% NEQ 0 (
    echo ERREUR: La copie a echoue!
) else (
    echo Copie reussie!
)

:end
echo.
pause
