#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du correctif pour les recommandations en tapis court
Vérifie que les all-in systématiques sont corrigés
"""

import sys
import os

def test_recommandations_tapis_court():
    """Test des nouvelles recommandations pour tapis court"""
    print("🧪 TEST DU CORRECTIF TAPIS COURT")
    print("=" * 50)
    
    try:
        from poker_advisor_light import PokerAdvisorLight
        
        advisor = PokerAdvisorLight()
        print("✅ PokerAdvisorLight importé")
        
        # Scénarios de test
        scenarios = [
            {
                "nom": "Main premium - Tapis très court",
                "equity": (75, 85),  # AA, KK
                "stack": 7,
                "bet_to_call": 0,
                "attendu": "all-in (push/fold obligatoire)"
            },
            {
                "nom": "Main premium - Tapis court",
                "equity": (75, 85),  # AA, KK
                "stack": 12,
                "bet_to_call": 0,
                "attendu": "raise 3-4 BB (pas all-in)"
            },
            {
                "nom": "Main forte - Tapis court",
                "equity": (60, 70),  # QQ, JJ, AK
                "stack": 12,
                "bet_to_call": 0,
                "attendu": "raise 2-3 BB (pas all-in)"
            },
            {
                "nom": "Main forte - Tapis court avec mise",
                "equity": (60, 70),  # QQ, JJ, AK
                "stack": 12,
                "bet_to_call": 2,
                "attendu": "call (pas all-in)"
            },
            {
                "nom": "Main solide - Tapis court",
                "equity": (50, 60),  # TT, 99, AQ
                "stack": 12,
                "bet_to_call": 0,
                "attendu": "raise 2-2.5 BB (pas all-in)"
            },
            {
                "nom": "Main solide - Tapis court avec mise",
                "equity": (50, 60),  # TT, 99, AQ
                "stack": 12,
                "bet_to_call": 1.5,
                "attendu": "call (pas all-in)"
            }
        ]
        
        print("\n📊 TESTS DES SCÉNARIOS :")
        print("-" * 50)
        
        all_in_count = 0
        non_all_in_count = 0
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n{i}. {scenario['nom']}")
            print(f"   Stack: {scenario['stack']} BB")
            print(f"   Équité: {scenario['equity'][0]}-{scenario['equity'][1]}%")
            print(f"   À suivre: {scenario['bet_to_call']} BB")
            
            # Tester la recommandation
            try:
                action, reason = advisor.recommend_action(
                    equity=scenario['equity'],
                    pot_odds=20.0,
                    stack_to_pot_ratio=5.0,
                    my_stack=scenario['stack'],
                    pot=10,
                    bet_to_call=scenario['bet_to_call']
                )
                
                print(f"   🎯 Action: {action}")
                print(f"   📝 Raison: {reason}")
                print(f"   ✅ Attendu: {scenario['attendu']}")
                
                # Analyser le résultat
                if "all-in" in action.lower():
                    all_in_count += 1
                    if "très court" in scenario['nom'] or scenario['stack'] < 8:
                        print(f"   ✅ ALL-IN justifié pour tapis très court")
                    else:
                        print(f"   ⚠️ ALL-IN pour tapis court (peut être évité)")
                else:
                    non_all_in_count += 1
                    print(f"   ✅ Action nuancée (pas all-in)")
                
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
        
        print(f"\n📊 RÉSUMÉ DES RÉSULTATS :")
        print(f"   All-in: {all_in_count}/{len(scenarios)}")
        print(f"   Actions nuancées: {non_all_in_count}/{len(scenarios)}")
        
        if non_all_in_count >= 4:  # Au moins 4 actions non all-in sur 6
            print(f"   ✅ CORRECTIF RÉUSSI - Moins d'all-in systématiques")
            return True
        else:
            print(f"   ⚠️ CORRECTIF PARTIEL - Encore trop d'all-in")
            return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparaison_avant_apres():
    """Compare les recommandations avant/après le correctif"""
    print("\n📊 COMPARAISON AVANT/APRÈS CORRECTIF")
    print("=" * 50)
    
    print("🔍 AVANT LE CORRECTIF :")
    print("   - Tapis < 20 BB → ALL-IN systématique")
    print("   - Aucune nuance selon la force de la main")
    print("   - Pas de prise en compte de la mise à suivre")
    print("   - 22 occurrences d'all-in automatiques")
    
    print("\n✅ APRÈS LE CORRECTIF :")
    print("   - Tapis < 8 BB → ALL-IN (push/fold strict)")
    print("   - Tapis 8-15 BB → Actions nuancées (raise, call)")
    print("   - Prise en compte de la force de la main")
    print("   - Adaptation selon la mise à suivre")
    print("   - Recommandations proportionnelles au stack")

def test_seuils_stack():
    """Test des nouveaux seuils de stack"""
    print("\n🎯 TEST DES NOUVEAUX SEUILS")
    print("=" * 50)
    
    seuils = [
        {"stack": 5, "category": "très_court", "strategy": "Push/fold strict"},
        {"stack": 10, "category": "court", "strategy": "Actions limitées mais nuancées"},
        {"stack": 18, "category": "moyen", "strategy": "Jeu plus ouvert"},
        {"stack": 50, "category": "profond", "strategy": "Jeu standard"}
    ]
    
    print("📋 NOUVEAUX SEUILS :")
    for seuil in seuils:
        print(f"   {seuil['stack']} BB → {seuil['category']} → {seuil['strategy']}")

def recommandations_utilisation():
    """Recommandations pour l'utilisation"""
    print("\n🚀 RECOMMANDATIONS D'UTILISATION")
    print("=" * 50)
    
    print("1. **Testez en situation réelle :**")
    print("   - Lancez l'application avec le conseiller")
    print("   - Jouez avec différents stacks (8-20 BB)")
    print("   - Observez les recommandations")
    
    print("\n2. **Vérifiez les améliorations :**")
    print("   - Moins d'all-in automatiques")
    print("   - Plus de recommandations 'raise X BB'")
    print("   - Actions adaptées à votre stack")
    
    print("\n3. **Ajustements possibles :**")
    print("   - Si encore trop d'all-in → Réduire les seuils")
    print("   - Si pas assez agressif → Augmenter les montants")
    print("   - Adapter selon votre style de jeu")
    
    print("\n4. **Situations où all-in reste justifié :**")
    print("   - Stack < 8 BB (push/fold obligatoire)")
    print("   - Main premium face à grosse mise")
    print("   - Situation désespérée en fin de tournoi")

def main():
    """Fonction principale"""
    print("🎯 TEST DU CORRECTIF TAPIS COURT")
    print("=" * 60)
    print("Vérification que les all-in systématiques sont corrigés")
    print()
    
    # Test principal
    success = test_recommandations_tapis_court()
    
    # Comparaison
    test_comparaison_avant_apres()
    
    # Test des seuils
    test_seuils_stack()
    
    # Recommandations
    recommandations_utilisation()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DU TEST")
    print("-" * 60)
    
    if success:
        print("✅ CORRECTIF RÉUSSI !")
        print("   • Moins d'all-in systématiques")
        print("   • Actions plus nuancées en tapis court")
        print("   • Recommandations proportionnelles")
        print("   • Prise en compte de la situation")
        
        print("\n🚀 PROCHAINES ÉTAPES :")
        print("1. Testez avec l'application complète")
        print("2. Jouez quelques mains en tapis court")
        print("3. Vérifiez que les recommandations sont meilleures")
        print("4. Ajustez si nécessaire")
    else:
        print("⚠️ CORRECTIF PARTIEL")
        print("   Certaines améliorations appliquées")
        print("   Peut nécessiter des ajustements supplémentaires")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
