#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger la gestion des raccourcis dans detector_gui.py
"""

import os
import re

def fix_shortcut_handling(file_path):
    """Corrige la gestion des raccourcis dans le fichier detector_gui.py"""
    print(f"Correction de la gestion des raccourcis dans {file_path}...")
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Motif à rechercher
    pattern = r"""            # Utiliser uniquement le raccourci dans le dossier de détection des régions
            config_path = 'C:\\\\Users\\\\<USER>\\\\PokerAdvisor\\\\Détection des regions\\\\config\\\\poker_advisor_config - Raccourci'
            (?:self\.status_bar\.showMessage\(f"Utilisation du raccourci de configuration: {config_path}"\))?
            print\(f"✅ Utilisation du raccourci de configuration: {config_path}"\)

            (?:# Récupérer l'état de la case à cocher CUDA
            use_cuda = self\.cuda_checkbox\.isChecked\(\))?"""
    
    # Remplacement
    replacement = r"""            # Utiliser uniquement le raccourci dans le dossier de détection des régions
            shortcut_path = 'C:\\Users\\<USER>\\PokerAdvisor\\Détection des regions\\config\\poker_advisor_config - Raccourci'
            print(f"✅ Utilisation du raccourci de configuration: {shortcut_path}")
            
            # Lire le contenu du fichier de raccourci pour obtenir le chemin réel
            with open(shortcut_path, 'r') as f:
                config_path = f.read().strip()
            print(f"✅ Raccourci résolu: {shortcut_path} -> {config_path}")

            # Récupérer l'état de la case à cocher CUDA
            use_cuda = self.cuda_checkbox.isChecked()"""
    
    # Effectuer le remplacement pour start_detection
    content = re.sub(pattern, replacement, content)
    
    # Motif pour extract_selected_regions
    pattern2 = r"""            # Récupérer la configuration des régions
            # Utiliser uniquement le raccourci dans le dossier de détection des régions
            config_path = 'C:\\\\Users\\\\<USER>\\\\PokerAdvisor\\\\Détection des regions\\\\config\\\\poker_advisor_config - Raccourci'
            print\(f"✅ Utilisation du raccourci de configuration: {config_path}"\)

            with open\(config_path, 'r'\) as f:
                config = json\.load\(f\)"""
    
    # Remplacement pour extract_selected_regions
    replacement2 = r"""            # Récupérer la configuration des régions
            # Utiliser uniquement le raccourci dans le dossier de détection des régions
            shortcut_path = 'C:\\Users\\<USER>\\PokerAdvisor\\Détection des regions\\config\\poker_advisor_config - Raccourci'
            print(f"✅ Utilisation du raccourci de configuration: {shortcut_path}")
            
            # Lire le contenu du fichier de raccourci pour obtenir le chemin réel
            with open(shortcut_path, 'r') as f:
                config_path = f.read().strip()
            print(f"✅ Raccourci résolu: {shortcut_path} -> {config_path}")
            
            # Charger la configuration à partir du chemin réel
            with open(config_path, 'r') as f:
                config = json.load(f)"""
    
    # Effectuer le remplacement pour extract_selected_regions
    content = re.sub(pattern2, replacement2, content)
    
    # Écrire le contenu modifié dans le fichier
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Gestion des raccourcis corrigée dans {file_path}")

def main():
    """Fonction principale"""
    # Fichier à corriger
    file_path = os.path.join(os.getcwd(), "detector_gui.py")
    
    if os.path.exists(file_path):
        fix_shortcut_handling(file_path)
    else:
        print(f"❌ Le fichier {file_path} n'existe pas")
    
    print("\nAppuyez sur Entrée pour quitter...")
    input()

if __name__ == "__main__":
    main()
