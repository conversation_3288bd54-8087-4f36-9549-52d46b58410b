#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 INTERFACE CONSEILLER POKER TEMPS RÉEL
========================================

Interface graphique qui intègre la détection automatique des joueurs
avec des conseils en temps réel basés sur leurs actions.
"""

import sys
import time
import threading
from typing import Dict, List, Any
import tkinter as tk
from tkinter import ttk, messagebox
from auto_player_detection import AutoPlayerDetector

class RealtimeAdvisorGUI:
    """Interface graphique pour le conseiller temps réel"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Conseiller Poker Temps Réel")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # Style sombre
        self.setup_styles()
        
        # Détecteur automatique
        self.detector = None
        self.monitoring = False
        
        # Variables d'interface
        self.players_var = tk.StringVar()
        self.pot_var = tk.StringVar()
        self.chips_var = tk.StringVar()
        self.cards_var = tk.StringVar()
        self.board_var = tk.StringVar()
        
        # Setup de l'interface
        self.setup_ui()
        
        # Démarrer la mise à jour automatique
        self.start_ui_updates()
    
    def setup_styles(self):
        """Configure les styles sombres"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Couleurs sombres
        style.configure('TLabel', background='#1e1e1e', foreground='white')
        style.configure('TButton', background='#404040', foreground='white')
        style.configure('TFrame', background='#1e1e1e')
        style.configure('TLabelFrame', background='#1e1e1e', foreground='white')
        style.configure('TCheckbutton', background='#1e1e1e', foreground='white')
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        
        # Titre principal
        title_label = tk.Label(
            self.root,
            text="🎯 Conseiller Poker Temps Réel",
            font=("Arial", 18, "bold"),
            bg='#1e1e1e',
            fg='#00ff00'
        )
        title_label.pack(pady=10)
        
        # Frame principal
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Frame gauche - Contrôles
        left_frame = tk.Frame(main_frame, bg='#1e1e1e', width=300)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # Frame droite - Conseils
        right_frame = tk.Frame(main_frame, bg='#1e1e1e')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Setup des sections
        self.setup_control_section(left_frame)
        self.setup_advice_section(right_frame)
    
    def setup_control_section(self, parent):
        """Configure la section de contrôle"""
        
        # Section Status
        status_frame = tk.LabelFrame(
            parent, 
            text="📊 Status", 
            bg='#1e1e1e', 
            fg='white',
            font=("Arial", 10, "bold")
        )
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = tk.Label(
            status_frame,
            text="⏹️ Arrêté",
            bg='#1e1e1e',
            fg='red',
            font=("Arial", 10, "bold")
        )
        self.status_label.pack(pady=5)
        
        # Boutons de contrôle
        control_frame = tk.Frame(status_frame, bg='#1e1e1e')
        control_frame.pack(fill=tk.X, pady=5)
        
        self.start_btn = tk.Button(
            control_frame,
            text="🚀 Démarrer",
            command=self.start_monitoring,
            bg='#00aa00',
            fg='white',
            font=("Arial", 9, "bold"),
            width=12
        )
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = tk.Button(
            control_frame,
            text="⏹️ Arrêter",
            command=self.stop_monitoring,
            bg='#aa0000',
            fg='white',
            font=("Arial", 9, "bold"),
            width=12,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT)
        
        # Section État de la table
        table_frame = tk.LabelFrame(
            parent,
            text="🎲 État de la table",
            bg='#1e1e1e',
            fg='white',
            font=("Arial", 10, "bold")
        )
        table_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Joueurs détectés
        tk.Label(table_frame, text="👥 Joueurs:", bg='#1e1e1e', fg='white').pack(anchor=tk.W)
        self.players_label = tk.Label(
            table_frame,
            textvariable=self.players_var,
            bg='#1e1e1e',
            fg='yellow',
            font=("Arial", 8),
            wraplength=280,
            justify=tk.LEFT
        )
        self.players_label.pack(anchor=tk.W, padx=10)
        
        # Pot et jetons
        tk.Label(table_frame, text="💰 Pot:", bg='#1e1e1e', fg='white').pack(anchor=tk.W, pady=(5,0))
        self.pot_label = tk.Label(
            table_frame,
            textvariable=self.pot_var,
            bg='#1e1e1e',
            fg='#00ff00',
            font=("Arial", 9, "bold")
        )
        self.pot_label.pack(anchor=tk.W, padx=10)
        
        tk.Label(table_frame, text="🎯 Mes jetons:", bg='#1e1e1e', fg='white').pack(anchor=tk.W, pady=(5,0))
        self.chips_label = tk.Label(
            table_frame,
            textvariable=self.chips_var,
            bg='#1e1e1e',
            fg='#00ff00',
            font=("Arial", 9, "bold")
        )
        self.chips_label.pack(anchor=tk.W, padx=10)
        
        # Cartes
        tk.Label(table_frame, text="🃏 Mes cartes:", bg='#1e1e1e', fg='white').pack(anchor=tk.W, pady=(5,0))
        self.cards_label = tk.Label(
            table_frame,
            textvariable=self.cards_var,
            bg='#1e1e1e',
            fg='#ffff00',
            font=("Arial", 9, "bold")
        )
        self.cards_label.pack(anchor=tk.W, padx=10)
        
        tk.Label(table_frame, text="🎴 Board:", bg='#1e1e1e', fg='white').pack(anchor=tk.W, pady=(5,0))
        self.board_label = tk.Label(
            table_frame,
            textvariable=self.board_var,
            bg='#1e1e1e',
            fg='#ffff00',
            font=("Arial", 9, "bold")
        )
        self.board_label.pack(anchor=tk.W, padx=10)
        
        # Boutons d'action manuelle
        manual_frame = tk.LabelFrame(
            parent,
            text="🔧 Actions manuelles",
            bg='#1e1e1e',
            fg='white',
            font=("Arial", 10, "bold")
        )
        manual_frame.pack(fill=tk.X, pady=(0, 10))
        
        tk.Button(
            manual_frame,
            text="🔄 Scanner joueurs",
            command=self.force_player_scan,
            bg='#404040',
            fg='white',
            font=("Arial", 8),
            width=20
        ).pack(pady=2)
        
        tk.Button(
            manual_frame,
            text="📊 Mettre à jour table",
            command=self.force_table_update,
            bg='#404040',
            fg='white',
            font=("Arial", 8),
            width=20
        ).pack(pady=2)
        
        tk.Button(
            manual_frame,
            text="💡 Conseil actuel",
            command=self.get_current_advice,
            bg='#0066cc',
            fg='white',
            font=("Arial", 8, "bold"),
            width=20
        ).pack(pady=2)
    
    def setup_advice_section(self, parent):
        """Configure la section des conseils"""
        
        # Titre de la section
        advice_title = tk.Label(
            parent,
            text="💡 Conseils Temps Réel",
            font=("Arial", 14, "bold"),
            bg='#1e1e1e',
            fg='#00ff00'
        )
        advice_title.pack(pady=(0, 10))
        
        # Zone de texte pour les conseils
        text_frame = tk.Frame(parent, bg='#1e1e1e')
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Zone de texte
        self.advice_text = tk.Text(
            text_frame,
            bg='#0d1117',
            fg='white',
            font=("Consolas", 10),
            wrap=tk.WORD,
            yscrollcommand=scrollbar.set,
            state=tk.DISABLED
        )
        self.advice_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.advice_text.yview)
        
        # Boutons de contrôle des conseils
        advice_control_frame = tk.Frame(parent, bg='#1e1e1e')
        advice_control_frame.pack(fill=tk.X, pady=(10, 0))
        
        tk.Button(
            advice_control_frame,
            text="🗑️ Effacer",
            command=self.clear_advice,
            bg='#aa4400',
            fg='white',
            font=("Arial", 9)
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        tk.Button(
            advice_control_frame,
            text="📄 Sauvegarder",
            command=self.save_advice,
            bg='#404040',
            fg='white',
            font=("Arial", 9)
        ).pack(side=tk.LEFT)
    
    def start_monitoring(self):
        """Démarre la surveillance automatique"""
        try:
            if not self.detector:
                self.detector = AutoPlayerDetector()
            
            self.detector.start_monitoring()
            self.monitoring = True
            
            # Mettre à jour l'interface
            self.status_label.config(text="🔄 En cours", fg='#00ff00')
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            
            self.log_advice("🚀 Surveillance automatique démarrée")
            self.log_advice("🔄 Détection des joueurs et actions en cours...")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible de démarrer la surveillance: {e}")
    
    def stop_monitoring(self):
        """Arrête la surveillance"""
        try:
            if self.detector:
                self.detector.stop_monitoring()
            
            self.monitoring = False
            
            # Mettre à jour l'interface
            self.status_label.config(text="⏹️ Arrêté", fg='red')
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            
            self.log_advice("⏹️ Surveillance arrêtée")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'arrêt: {e}")
    
    def force_player_scan(self):
        """Force un scan des joueurs"""
        if self.detector:
            self.detector.force_player_scan()
            self.log_advice("🔄 Scan forcé des joueurs...")
        else:
            messagebox.showwarning("Attention", "Démarrez d'abord la surveillance")
    
    def force_table_update(self):
        """Force une mise à jour de la table"""
        if self.detector:
            self.detector.force_table_update()
            self.log_advice("📊 Mise à jour forcée de la table...")
        else:
            messagebox.showwarning("Attention", "Démarrez d'abord la surveillance")
    
    def get_current_advice(self):
        """Obtient et affiche le conseil actuel"""
        if self.detector:
            try:
                advice = self.detector.get_current_advice()
                
                self.log_advice("\n💡 CONSEIL ACTUEL")
                self.log_advice("=" * 40)
                
                if "error" in advice:
                    self.log_advice(f"❌ {advice['error']}")
                else:
                    self.log_advice(f"🎯 Action recommandée: {advice.get('final_recommendation', 'N/A')}")
                    
                    if "table_state" in advice:
                        state = advice["table_state"]
                        self.log_advice(f"👥 Joueurs: {', '.join(state.get('players', []))}")
                        self.log_advice(f"💰 Pot: {state.get('pot', 0)}")
                        self.log_advice(f"🎯 Mes jetons: {state.get('my_chips', 0)}")
                        
                        if state.get('recent_actions'):
                            self.log_advice("📋 Actions récentes:")
                            for action in state['recent_actions']:
                                self.log_advice(f"  • {action}")
                    
                    if advice.get('tracker_adjustments'):
                        self.log_advice("🔧 Ajustements:")
                        for adj in advice['tracker_adjustments']:
                            self.log_advice(f"  • {adj}")
                
                self.log_advice("-" * 40)
                
            except Exception as e:
                self.log_advice(f"❌ Erreur obtention conseil: {e}")
        else:
            messagebox.showwarning("Attention", "Démarrez d'abord la surveillance")
    
    def start_ui_updates(self):
        """Démarre les mises à jour automatiques de l'interface"""
        def update_loop():
            while True:
                try:
                    if self.detector and self.monitoring:
                        summary = self.detector.get_table_summary()
                        
                        # Mettre à jour les variables d'affichage
                        self.players_var.set(", ".join(summary.get('active_players', [])))
                        self.pot_var.set(f"{summary.get('pot', 0):,}")
                        self.chips_var.set(f"{summary.get('my_chips', 0):,}")
                        self.cards_var.set(" ".join(summary.get('my_cards', [])))
                        self.board_var.set(" ".join(summary.get('board_cards', [])))
                    
                    time.sleep(2)  # Mise à jour toutes les 2 secondes
                    
                except Exception as e:
                    print(f"⚠️ Erreur mise à jour UI: {e}")
                    time.sleep(5)
        
        # Démarrer le thread de mise à jour
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def log_advice(self, message: str):
        """Ajoute un message aux conseils"""
        try:
            self.advice_text.config(state=tk.NORMAL)
            
            # Ajouter timestamp
            timestamp = time.strftime("%H:%M:%S")
            full_message = f"[{timestamp}] {message}\n"
            
            self.advice_text.insert(tk.END, full_message)
            self.advice_text.see(tk.END)
            self.advice_text.config(state=tk.DISABLED)
            
            # Forcer la mise à jour
            self.root.update_idletasks()
            
        except Exception as e:
            print(f"❌ Erreur log: {e}")
    
    def clear_advice(self):
        """Efface les conseils"""
        self.advice_text.config(state=tk.NORMAL)
        self.advice_text.delete(1.0, tk.END)
        self.advice_text.config(state=tk.DISABLED)
    
    def save_advice(self):
        """Sauvegarde les conseils"""
        try:
            content = self.advice_text.get(1.0, tk.END)
            filename = f"conseils_poker_{int(time.time())}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            messagebox.showinfo("Succès", f"Conseils sauvegardés: {filename}")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur sauvegarde: {e}")
    
    def on_closing(self):
        """Gestionnaire de fermeture"""
        if self.monitoring:
            self.stop_monitoring()
        self.root.destroy()
    
    def run(self):
        """Lance l'interface"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Message de bienvenue
        self.log_advice("🎯 Conseiller Poker Temps Réel initialisé")
        self.log_advice("💡 Cliquez sur 'Démarrer' pour commencer la surveillance")
        self.log_advice("📋 Les conseils apparaîtront automatiquement ici")
        
        self.root.mainloop()

def main():
    """Fonction principale"""
    print("🎯 Lancement du Conseiller Poker Temps Réel")
    
    try:
        app = RealtimeAdvisorGUI()
        app.run()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
