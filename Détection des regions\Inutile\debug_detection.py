#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de debug pour diagnostiquer les problèmes de détection
"""

import os
import sys
import time
import cv2
import numpy as np

def test_original_detector():
    """Test du détecteur original sans optimisations"""
    print("🔍 Test du détecteur original...")
    
    try:
        # Importer EasyOCR directement
        import easyocr
        
        # Créer une image de test avec du texte clair
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        cv2.putText(test_image, "K", (150, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        # Sauvegarder l'image de test
        cv2.imwrite("test_image_debug.jpg", test_image)
        print("✅ Image de test créée: test_image_debug.jpg")
        
        # Test EasyOCR direct
        print("🔄 Test EasyOCR direct...")
        reader = easyocr.Reader(['en'], gpu=True, verbose=False)
        
        start_time = time.time()
        results = reader.readtext(test_image)
        detection_time = time.time() - start_time
        
        print(f"✅ EasyOCR direct: {detection_time:.3f}s")
        print(f"✅ Résultats bruts: {results}")
        
        # Extraire le texte
        detected_texts = []
        for (bbox, text, confidence) in results:
            if confidence > 0.5:  # Seuil de confiance
                detected_texts.append(text.strip().upper())
                print(f"  - Texte: '{text}' (confiance: {confidence:.2f})")
        
        print(f"✅ Textes détectés: {detected_texts}")
        
        return detected_texts
        
    except Exception as e:
        print(f"❌ Erreur EasyOCR direct: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_cuda_detector():
    """Test du détecteur CUDA optimisé"""
    print("\n🔍 Test du détecteur CUDA optimisé...")
    
    try:
        from detector_cuda_optimized import CudaOptimizedDetector
        
        # Créer le détecteur
        detector = CudaOptimizedDetector()
        
        # Créer une image de test
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        # Test de détection
        start_time = time.time()
        result = detector.detect_text_fast(test_image)
        detection_time = time.time() - start_time
        
        print(f"✅ Détecteur CUDA: {detection_time:.3f}s")
        print(f"✅ Résultat: '{result}'")
        
        return result
        
    except Exception as e:
        print(f"❌ Erreur détecteur CUDA: {e}")
        import traceback
        traceback.print_exc()
        return ""

def test_main_detector():
    """Test du détecteur principal"""
    print("\n🔍 Test du détecteur principal...")
    
    try:
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector(use_cuda=True)
        
        # Créer une image de test
        test_image = np.ones((200, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "A", (100, 120), cv2.FONT_HERSHEY_SIMPLEX, 4, (0, 0, 0), 6)
        
        # Test de détection rapide
        start_time = time.time()
        result = detector.detect_text_fast(test_image)
        detection_time = time.time() - start_time
        
        print(f"✅ Détecteur principal: {detection_time:.3f}s")
        print(f"✅ Résultat: '{result}'")
        
        # Test de la méthode simple
        print("🔄 Test méthode simple...")
        start_time = time.time()
        result_simple = detector.detect_text_simple(test_image)
        detection_time_simple = time.time() - start_time
        
        print(f"✅ Méthode simple: {detection_time_simple:.3f}s")
        print(f"✅ Résultat simple: '{result_simple}'")
        
        return result, result_simple
        
    except Exception as e:
        print(f"❌ Erreur détecteur principal: {e}")
        import traceback
        traceback.print_exc()
        return "", ""

def test_with_real_card_image():
    """Test avec une vraie image de carte si disponible"""
    print("\n🔍 Test avec image réelle...")
    
    # Chercher des images de test
    test_files = ["test_card.jpg", "test_card.png", "card_test.jpg", "screenshot.jpg"]
    
    for filename in test_files:
        if os.path.exists(filename):
            print(f"📁 Image trouvée: {filename}")
            
            try:
                # Charger l'image
                image = cv2.imread(filename)
                if image is None:
                    continue
                
                print(f"✅ Image chargée: {image.shape}")
                
                # Test avec EasyOCR direct
                import easyocr
                reader = easyocr.Reader(['en'], gpu=True, verbose=False)
                
                results = reader.readtext(image)
                print(f"✅ Résultats sur image réelle: {results}")
                
                for (bbox, text, confidence) in results:
                    if confidence > 0.3:
                        print(f"  - Texte: '{text}' (confiance: {confidence:.2f})")
                
                return True
                
            except Exception as e:
                print(f"❌ Erreur avec {filename}: {e}")
    
    print("⚠️ Aucune image de test trouvée")
    return False

def main():
    print("🔥 DIAGNOSTIC DE DÉTECTION")
    print("=" * 50)
    
    # Test 1: EasyOCR direct
    original_results = test_original_detector()
    
    # Test 2: Détecteur CUDA optimisé
    cuda_result = test_cuda_detector()
    
    # Test 3: Détecteur principal
    main_result, simple_result = test_main_detector()
    
    # Test 4: Image réelle
    test_with_real_card_image()
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    print(f"EasyOCR direct: {original_results}")
    print(f"Détecteur CUDA: '{cuda_result}'")
    print(f"Détecteur principal: '{main_result}'")
    print(f"Méthode simple: '{simple_result}'")
    
    # Diagnostic
    if not any([original_results, cuda_result, main_result, simple_result]):
        print("\n❌ PROBLÈME: Aucune détection ne fonctionne")
        print("💡 Solutions possibles:")
        print("  1. Vérifier la qualité des images")
        print("  2. Ajuster les paramètres de prétraitement")
        print("  3. Vérifier la configuration EasyOCR")
    elif original_results and not cuda_result:
        print("\n⚠️ PROBLÈME: EasyOCR fonctionne mais pas le détecteur CUDA")
        print("💡 Le problème est dans le détecteur optimisé")
    elif cuda_result and not main_result:
        print("\n⚠️ PROBLÈME: Détecteur CUDA fonctionne mais pas le principal")
        print("💡 Le problème est dans l'intégration")
    else:
        print("\n✅ Certaines détections fonctionnent")

if __name__ == "__main__":
    main()
