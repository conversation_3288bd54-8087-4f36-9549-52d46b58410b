#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple d'Ollama + LLaVA pour la détection de montants et pseudos
====================================================================

Script de test rapide pour vérifier qu'Ollama fonctionne correctement
avant d'utiliser le module complet.

Usage:
    python test_ollama_simple.py
"""

import requests
import json
import os
import sys

def test_ollama_connection():
    """Test de base de la connexion Ollama"""
    print("🔗 Test de connexion à Ollama...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama connecté ! Modèles disponibles:")
            for model in models:
                print(f"   - {model['name']}")
            return True
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Impossible de se connecter à Ollama: {e}")
        return False

def test_simple_text():
    """Test simple avec du texte uniquement"""
    print("\n📝 Test simple avec texte...")
    
    try:
        payload = {
            "model": "llava:latest",
            "prompt": "Réponds simplement par 'OK' si tu me comprends.",
            "stream": False
        }
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get("response", "")
            print(f"✅ Réponse LLaVA: {response_text}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test texte: {e}")
        return False

def check_requirements():
    """Vérifie les dépendances Python"""
    print("\n📦 Vérification des dépendances...")
    
    required_packages = ['requests', 'opencv-python', 'numpy', 'pillow']
    missing = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
                print(f"✅ OpenCV: {cv2.__version__}")
            elif package == 'requests':
                import requests
                print(f"✅ Requests: {requests.__version__}")
            elif package == 'numpy':
                import numpy
                print(f"✅ NumPy: {numpy.__version__}")
            elif package == 'pillow':
                import PIL
                print(f"✅ Pillow: {PIL.__version__}")
        except ImportError:
            missing.append(package)
            print(f"❌ {package}: Non installé")
    
    if missing:
        print(f"\n⚠️ Packages manquants: {', '.join(missing)}")
        print("💡 Installez avec: pip install " + " ".join(missing))
        return False
    
    return True

def show_installation_guide():
    """Affiche le guide d'installation"""
    print("\n" + "=" * 60)
    print("📋 GUIDE D'INSTALLATION OLLAMA + LLAVA")
    print("=" * 60)
    print()
    print("1️⃣ INSTALLER OLLAMA:")
    print("   Windows: Télécharger depuis https://ollama.ai/")
    print("   Linux: curl -fsSL https://ollama.ai/install.sh | sh")
    print()
    print("2️⃣ DÉMARRER OLLAMA:")
    print("   Ouvrir un terminal et taper: ollama serve")
    print("   (Laisser ce terminal ouvert)")
    print()
    print("3️⃣ TÉLÉCHARGER LLAVA:")
    print("   Dans un autre terminal: ollama pull llava")
    print("   (Téléchargement ~4GB, patience...)")
    print()
    print("4️⃣ TESTER:")
    print("   ollama run llava")
    print("   Puis taper: /bye pour quitter")
    print()
    print("5️⃣ RELANCER CE SCRIPT:")
    print("   python test_ollama_simple.py")
    print()
    print("=" * 60)

def main():
    """Fonction principale"""
    print("🚀 TEST SIMPLE OLLAMA + LLAVA")
    print("=" * 40)
    
    # Vérifier les dépendances Python
    if not check_requirements():
        return False
    
    # Tester la connexion Ollama
    if not test_ollama_connection():
        show_installation_guide()
        return False
    
    # Test simple avec texte
    if not test_simple_text():
        print("❌ Le modèle LLaVA ne répond pas correctement")
        return False
    
    print("\n🎉 TOUS LES TESTS RÉUSSIS !")
    print("✅ Ollama fonctionne correctement")
    print("✅ LLaVA est opérationnel")
    print("✅ Vous pouvez maintenant tester le module complet")
    print()
    print("💡 Prochaine étape:")
    print("   python ollama_vision_detector.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Des problèmes ont été détectés.")
        print("💡 Suivez le guide d'installation ci-dessus.")
    
    input("\nAppuyez sur Entrée pour fermer...")
