#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Examinateur d'images de debug pour les mises
Aide à identifier visuellement les problèmes de calibration
"""

import sys
import os
import cv2
import numpy as np
from pathlib import Path

def examiner_images_debug():
    """Examine les images de debug pour identifier les problèmes"""
    print("🔍 EXAMEN DES IMAGES DE DEBUG - MISES")
    print("=" * 50)
    
    # Chercher les images de debug
    debug_images = []
    for i in range(1, 8):
        image_path = f"debug_jaune_mise_joueur{i}.jpg"
        mask_path = f"debug_jaune_mise_joueur{i}_mask.jpg"
        
        if os.path.exists(image_path):
            debug_images.append({
                'region': f'mise_joueur{i}',
                'image_path': image_path,
                'mask_path': mask_path if os.path.exists(mask_path) else None
            })
    
    if not debug_images:
        print("❌ Aucune image de debug trouvée")
        print("   Lancez d'abord: python diagnostic_mises_jaunes.py")
        return False
    
    print(f"✅ {len(debug_images)} images de debug trouvées")
    
    # Analyser chaque image
    for debug_info in debug_images:
        analyser_image_debug(debug_info)
    
    # Créer un résumé visuel
    creer_resume_visuel(debug_images)
    
    return True

def analyser_image_debug(debug_info):
    """Analyse une image de debug spécifique"""
    region = debug_info['region']
    image_path = debug_info['image_path']
    
    print(f"\n🎯 ANALYSE - {region.upper()}")
    print("-" * 30)
    
    try:
        # Charger l'image
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Impossible de charger {image_path}")
            return
        
        h, w = image.shape[:2]
        print(f"📏 Taille: {w}x{h} pixels")
        
        # Analyser le contenu
        analyser_contenu_image(image, region)
        
        # Analyser les couleurs
        analyser_couleurs_image(image, region)
        
        # Suggestions d'amélioration
        suggerer_ameliorations(image, region)
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse de {region}: {e}")

def analyser_contenu_image(image, region):
    """Analyse le contenu visuel de l'image"""
    print("🔍 Contenu visuel:")
    
    # Convertir en niveaux de gris
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Calculer les statistiques
    luminosite_moyenne = np.mean(gray)
    contraste = np.std(gray)
    
    print(f"   💡 Luminosité moyenne: {luminosite_moyenne:.1f}")
    print(f"   📊 Contraste: {contraste:.1f}")
    
    # Évaluer la qualité
    if luminosite_moyenne < 30:
        print("   ⚠️ Image très sombre - Peut affecter l'OCR")
    elif luminosite_moyenne > 200:
        print("   ⚠️ Image très claire - Peut affecter l'OCR")
    else:
        print("   ✅ Luminosité correcte")
    
    if contraste < 20:
        print("   ⚠️ Contraste faible - Texte peu visible")
    else:
        print("   ✅ Contraste suffisant")
    
    # Détecter les zones de texte potentielles
    detecter_zones_texte(gray, region)

def detecter_zones_texte(gray, region):
    """Détecte les zones potentielles de texte"""
    print("   📝 Zones de texte potentielles:")
    
    # Appliquer un seuil pour détecter le texte
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # Trouver les contours
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    zones_texte = 0
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > 50:  # Zone suffisamment grande
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            # Critères pour du texte (ratio largeur/hauteur raisonnable)
            if 0.2 <= aspect_ratio <= 5.0 and w > 10 and h > 5:
                zones_texte += 1
                print(f"      Zone {zones_texte}: ({x}, {y}) {w}x{h} (ratio: {aspect_ratio:.2f})")
    
    if zones_texte == 0:
        print("      ❌ Aucune zone de texte détectée")
        print("      💡 La région pourrait être mal positionnée")
    else:
        print(f"      ✅ {zones_texte} zone(s) de texte détectée(s)")

def analyser_couleurs_image(image, region):
    """Analyse les couleurs dans l'image"""
    print("🎨 Analyse des couleurs:")
    
    # Convertir en HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Définir les plages de couleurs
    plages_couleurs = {
        'Jaune': ([20, 100, 100], [30, 255, 255]),
        'Orange': ([10, 100, 100], [20, 255, 255]),
        'Jaune/Orange large': ([5, 80, 80], [35, 255, 255])
    }
    
    total_pixels = image.shape[0] * image.shape[1]
    
    for nom_couleur, (lower, upper) in plages_couleurs.items():
        lower_np = np.array(lower)
        upper_np = np.array(upper)
        mask = cv2.inRange(hsv, lower_np, upper_np)
        pixels_detectes = np.sum(mask > 0)
        pourcentage = (pixels_detectes / total_pixels) * 100
        
        if pourcentage > 1.0:
            print(f"   ✅ {nom_couleur}: {pourcentage:.2f}%")
        else:
            print(f"   ❌ {nom_couleur}: {pourcentage:.2f}%")

def suggerer_ameliorations(image, region):
    """Suggère des améliorations pour la région"""
    print("💡 Suggestions d'amélioration:")
    
    h, w = image.shape[:2]
    
    # Analyser la taille
    if w < 100 or h < 30:
        print("   📏 Région trop petite - Agrandir la zone de capture")
    elif w > 400 or h > 100:
        print("   📏 Région très grande - Réduire pour plus de précision")
    else:
        print("   ✅ Taille de région appropriée")
    
    # Analyser le contenu
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    luminosite = np.mean(gray)
    
    if luminosite < 50:
        print("   💡 Région sombre - Vérifier qu'elle capture bien les mises jaunes")
    
    # Vérifier la présence de couleurs jaunes/oranges
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    mask_jaune_orange = cv2.inRange(hsv, np.array([5, 80, 80]), np.array([35, 255, 255]))
    pourcentage_jaune_orange = (np.sum(mask_jaune_orange > 0) / (h * w)) * 100
    
    if pourcentage_jaune_orange < 2:
        print("   ⚠️ Très peu de jaune/orange - Vérifier le positionnement")
        print("   💡 Déplacer la région vers les mises visibles")
    else:
        print("   ✅ Couleurs jaunes/oranges détectées")

def creer_resume_visuel(debug_images):
    """Crée un résumé visuel de toutes les régions"""
    print(f"\n📊 CRÉATION DU RÉSUMÉ VISUEL")
    print("-" * 50)
    
    try:
        # Créer une grille d'images
        images_par_ligne = 4
        nb_lignes = (len(debug_images) + images_par_ligne - 1) // images_par_ligne
        
        # Taille cible pour chaque image
        target_width = 200
        target_height = 100
        
        # Créer l'image de résumé
        resume_width = images_par_ligne * target_width
        resume_height = nb_lignes * (target_height + 30)  # +30 pour le texte
        resume_image = np.zeros((resume_height, resume_width, 3), dtype=np.uint8)
        
        for i, debug_info in enumerate(debug_images):
            # Charger l'image
            image = cv2.imread(debug_info['image_path'])
            if image is None:
                continue
            
            # Redimensionner
            image_resized = cv2.resize(image, (target_width, target_height))
            
            # Calculer la position
            ligne = i // images_par_ligne
            colonne = i % images_par_ligne
            
            y_start = ligne * (target_height + 30)
            y_end = y_start + target_height
            x_start = colonne * target_width
            x_end = x_start + target_width
            
            # Placer l'image
            resume_image[y_start:y_end, x_start:x_end] = image_resized
            
            # Ajouter le nom de la région
            cv2.putText(resume_image, debug_info['region'], 
                       (x_start + 5, y_end + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Sauvegarder le résumé
        cv2.imwrite("resume_debug_mises.jpg", resume_image)
        print("✅ Résumé visuel créé: resume_debug_mises.jpg")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du résumé: {e}")

def generer_rapport_calibration():
    """Génère un rapport de calibration"""
    print(f"\n📋 RAPPORT DE CALIBRATION")
    print("=" * 50)
    
    print("🔍 PROBLÈMES IDENTIFIÉS :")
    print("1. mise_joueur1 : Détecte couleurs mais pas les chiffres")
    print("2. mise_joueur2-7 : Ne détectent aucune couleur jaune/orange")
    print()
    
    print("🔧 SOLUTIONS RECOMMANDÉES :")
    print("1. **Pour mise_joueur1 :**")
    print("   - Améliorer le prétraitement OCR")
    print("   - Ajuster la région pour capturer les chiffres")
    print("   - Tester différents seuils de binarisation")
    print()
    
    print("2. **Pour mise_joueur2-7 :**")
    print("   - Recalibrer les régions sur les mises visibles")
    print("   - Vérifier qu'il y a des mises sur ces positions")
    print("   - Ajuster la taille des régions")
    print()
    
    print("🚀 ÉTAPES SUIVANTES :")
    print("1. Ouvrez resume_debug_mises.jpg pour voir toutes les régions")
    print("2. Identifiez visuellement les régions mal positionnées")
    print("3. Utilisez l'outil de calibration pour ajuster")
    print("4. Retestez avec python diagnostic_mises_jaunes.py")

def main():
    """Fonction principale"""
    print("🔍 EXAMINATEUR D'IMAGES DEBUG - MISES PARTIELLES")
    print("=" * 60)
    print("Analyse visuelle des problèmes de détection des mises")
    print()
    
    # Examiner les images
    success = examiner_images_debug()
    
    if success:
        # Générer le rapport
        generer_rapport_calibration()
        
        print("\n" + "=" * 60)
        print("📊 EXAMEN TERMINÉ")
        print("-" * 60)
        print("✅ Images analysées")
        print("📁 Résumé visuel créé: resume_debug_mises.jpg")
        print("📋 Rapport de calibration généré")
        
        print("\n🎯 ACTIONS RECOMMANDÉES :")
        print("1. Ouvrez resume_debug_mises.jpg")
        print("2. Identifiez les régions problématiques")
        print("3. Recalibrez les régions mal positionnées")
        print("4. Retestez la détection")
    else:
        print("❌ Impossible d'examiner les images")
        print("   Lancez d'abord: python diagnostic_mises_jaunes.py")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
