#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic avancé pour les mises partiellement détectées
Analyse comparative entre les mises qui fonctionnent et celles qui échouent
"""

import sys
import os
import json
import cv2
import numpy as np

def analyser_mises_comparatives():
    """Analyse comparative des mises qui fonctionnent vs celles qui échouent"""
    print("🔍 DIAGNOSTIC COMPARATIF - MISES PARTIELLES")
    print("=" * 60)
    
    try:
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector()
        print("✅ Détecteur créé")
        
        # Capture d'écran
        import mss
        with mss.mss() as sct:
            screenshot = sct.grab(sct.monitors[1])
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_BGRA2BGR)
        
        print(f"✅ Capture réussie: {screenshot_bgr.shape}")
        
        # Charger la configuration
        config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        all_regions = config.get('all_regions', {})
        
        print("\n🔍 ANALYSE COMPARATIVE DES MISES")
        print("-" * 60)
        
        mises_fonctionnelles = []
        mises_defaillantes = []
        
        # Analyser chaque région mise_joueur
        for i in range(1, 8):
            region_name = f"mise_joueur{i}"
            
            if region_name in all_regions:
                print(f"\n🎯 {region_name.upper()}")
                print("-" * 30)
                
                region_data = all_regions[region_name]
                x, y, w, h = region_data['x'], region_data['y'], region_data['width'], region_data['height']
                
                # Extraire la région
                region_img = screenshot_bgr[y:y+h, x:x+w]
                
                print(f"📍 Position: ({x}, {y}) - Taille: {w}x{h}")
                
                # Analyser en détail
                analyse = analyser_region_detaillee(region_img, region_name, detector)
                
                # Classer la région
                if analyse['texte_detecte'] and analyse['texte_detecte'].strip():
                    mises_fonctionnelles.append({
                        'region': region_name,
                        'analyse': analyse,
                        'position': (x, y, w, h)
                    })
                    print(f"✅ FONCTIONNE: '{analyse['texte_detecte']}'")
                else:
                    mises_defaillantes.append({
                        'region': region_name,
                        'analyse': analyse,
                        'position': (x, y, w, h)
                    })
                    print(f"❌ ÉCHOUE: Aucun texte détecté")
        
        # Analyser les différences
        analyser_differences(mises_fonctionnelles, mises_defaillantes)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyser_region_detaillee(image, region_name, detector):
    """Analyse détaillée d'une région"""
    analyse = {
        'region_name': region_name,
        'texte_detecte': '',
        'couleurs': [],
        'luminosite': 0,
        'contraste': 0,
        'taille': image.shape[:2],
        'qualite_image': '',
        'problemes_identifies': []
    }
    
    # Analyser les couleurs
    try:
        couleurs = detector.detect_colors_fast(image)
        analyse['couleurs'] = couleurs
    except:
        analyse['couleurs'] = ['erreur']
    
    # Analyser la luminosité et le contraste
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    analyse['luminosite'] = np.mean(gray)
    analyse['contraste'] = np.std(gray)
    
    # Détecter le texte
    try:
        texte = detector.detect_amount_text(image)
        analyse['texte_detecte'] = texte if texte else ''
    except Exception as e:
        analyse['texte_detecte'] = ''
        analyse['problemes_identifies'].append(f"Erreur OCR: {e}")
    
    # Évaluer la qualité de l'image
    h, w = image.shape[:2]
    if w < 50 or h < 20:
        analyse['qualite_image'] = 'Trop petite'
        analyse['problemes_identifies'].append('Région trop petite pour OCR')
    elif analyse['contraste'] < 10:
        analyse['qualite_image'] = 'Contraste faible'
        analyse['problemes_identifies'].append('Contraste insuffisant')
    elif analyse['luminosite'] < 30:
        analyse['qualite_image'] = 'Trop sombre'
        analyse['problemes_identifies'].append('Image trop sombre')
    elif analyse['luminosite'] > 200:
        analyse['qualite_image'] = 'Trop claire'
        analyse['problemes_identifies'].append('Image trop claire')
    else:
        analyse['qualite_image'] = 'Correcte'
    
    # Analyser les couleurs spécifiquement
    if 'orange' not in analyse['couleurs'] and 'yellow' not in analyse['couleurs']:
        analyse['problemes_identifies'].append('Aucune couleur jaune/orange détectée')
    
    # Sauvegarder l'image pour inspection
    cv2.imwrite(f"debug_comparative_{region_name}.jpg", image)
    
    return analyse

def analyser_differences(mises_fonctionnelles, mises_defaillantes):
    """Analyse les différences entre mises fonctionnelles et défaillantes"""
    print(f"\n📊 ANALYSE COMPARATIVE")
    print("=" * 60)
    
    print(f"✅ Mises fonctionnelles: {len(mises_fonctionnelles)}")
    print(f"❌ Mises défaillantes: {len(mises_defaillantes)}")
    
    if mises_fonctionnelles:
        print(f"\n🎯 CARACTÉRISTIQUES DES MISES QUI FONCTIONNENT:")
        print("-" * 50)
        
        for mise in mises_fonctionnelles:
            analyse = mise['analyse']
            x, y, w, h = mise['position']
            print(f"\n   {mise['region']}:")
            print(f"      Texte: '{analyse['texte_detecte']}'")
            print(f"      Position: ({x}, {y}) - Taille: {w}x{h}")
            print(f"      Couleurs: {analyse['couleurs']}")
            print(f"      Luminosité: {analyse['luminosite']:.1f}")
            print(f"      Contraste: {analyse['contraste']:.1f}")
            print(f"      Qualité: {analyse['qualite_image']}")
        
        # Calculer les moyennes des mises fonctionnelles
        avg_luminosite_ok = np.mean([m['analyse']['luminosite'] for m in mises_fonctionnelles])
        avg_contraste_ok = np.mean([m['analyse']['contraste'] for m in mises_fonctionnelles])
        avg_width_ok = np.mean([m['position'][2] for m in mises_fonctionnelles])
        avg_height_ok = np.mean([m['position'][3] for m in mises_fonctionnelles])
        
        print(f"\n📈 MOYENNES DES MISES FONCTIONNELLES:")
        print(f"      Luminosité moyenne: {avg_luminosite_ok:.1f}")
        print(f"      Contraste moyen: {avg_contraste_ok:.1f}")
        print(f"      Taille moyenne: {avg_width_ok:.0f}x{avg_height_ok:.0f}")
    
    if mises_defaillantes:
        print(f"\n❌ CARACTÉRISTIQUES DES MISES QUI ÉCHOUENT:")
        print("-" * 50)
        
        for mise in mises_defaillantes:
            analyse = mise['analyse']
            x, y, w, h = mise['position']
            print(f"\n   {mise['region']}:")
            print(f"      Position: ({x}, {y}) - Taille: {w}x{h}")
            print(f"      Couleurs: {analyse['couleurs']}")
            print(f"      Luminosité: {analyse['luminosite']:.1f}")
            print(f"      Contraste: {analyse['contraste']:.1f}")
            print(f"      Qualité: {analyse['qualite_image']}")
            if analyse['problemes_identifies']:
                print(f"      Problèmes: {', '.join(analyse['problemes_identifies'])}")
        
        # Calculer les moyennes des mises défaillantes
        avg_luminosite_ko = np.mean([m['analyse']['luminosite'] for m in mises_defaillantes])
        avg_contraste_ko = np.mean([m['analyse']['contraste'] for m in mises_defaillantes])
        avg_width_ko = np.mean([m['position'][2] for m in mises_defaillantes])
        avg_height_ko = np.mean([m['position'][3] for m in mises_defaillantes])
        
        print(f"\n📉 MOYENNES DES MISES DÉFAILLANTES:")
        print(f"      Luminosité moyenne: {avg_luminosite_ko:.1f}")
        print(f"      Contraste moyen: {avg_contraste_ko:.1f}")
        print(f"      Taille moyenne: {avg_width_ko:.0f}x{avg_height_ko:.0f}")
    
    # Identifier les patterns
    identifier_patterns(mises_fonctionnelles, mises_defaillantes)

def identifier_patterns(mises_fonctionnelles, mises_defaillantes):
    """Identifie les patterns entre mises fonctionnelles et défaillantes"""
    print(f"\n🔍 PATTERNS IDENTIFIÉS")
    print("=" * 60)
    
    if mises_fonctionnelles and mises_defaillantes:
        # Comparer les caractéristiques
        luminosite_ok = [m['analyse']['luminosite'] for m in mises_fonctionnelles]
        luminosite_ko = [m['analyse']['luminosite'] for m in mises_defaillantes]
        
        contraste_ok = [m['analyse']['contraste'] for m in mises_fonctionnelles]
        contraste_ko = [m['analyse']['contraste'] for m in mises_defaillantes]
        
        taille_ok = [(m['position'][2], m['position'][3]) for m in mises_fonctionnelles]
        taille_ko = [(m['position'][2], m['position'][3]) for m in mises_defaillantes]
        
        print("🔍 DIFFÉRENCES PRINCIPALES :")
        
        # Luminosité
        if np.mean(luminosite_ok) > np.mean(luminosite_ko) + 20:
            print("   • Les mises fonctionnelles sont plus claires")
        elif np.mean(luminosite_ko) > np.mean(luminosite_ok) + 20:
            print("   • Les mises défaillantes sont plus claires")
        
        # Contraste
        if np.mean(contraste_ok) > np.mean(contraste_ko) + 10:
            print("   • Les mises fonctionnelles ont plus de contraste")
        elif np.mean(contraste_ko) > np.mean(contraste_ok) + 10:
            print("   • Les mises défaillantes ont plus de contraste")
        
        # Taille
        avg_area_ok = np.mean([w*h for w, h in taille_ok])
        avg_area_ko = np.mean([w*h for w, h in taille_ko])
        
        if avg_area_ok > avg_area_ko * 1.5:
            print("   • Les mises fonctionnelles sont plus grandes")
        elif avg_area_ko > avg_area_ok * 1.5:
            print("   • Les mises défaillantes sont plus grandes")
        
        # Couleurs
        couleurs_ok = set()
        couleurs_ko = set()
        
        for mise in mises_fonctionnelles:
            couleurs_ok.update(mise['analyse']['couleurs'])
        
        for mise in mises_defaillantes:
            couleurs_ko.update(mise['analyse']['couleurs'])
        
        couleurs_uniquement_ok = couleurs_ok - couleurs_ko
        couleurs_uniquement_ko = couleurs_ko - couleurs_ok
        
        if couleurs_uniquement_ok:
            print(f"   • Couleurs présentes seulement dans les mises fonctionnelles: {couleurs_uniquement_ok}")
        
        if couleurs_uniquement_ko:
            print(f"   • Couleurs présentes seulement dans les mises défaillantes: {couleurs_uniquement_ko}")

def generer_recommandations():
    """Génère des recommandations basées sur l'analyse"""
    print(f"\n🔧 RECOMMANDATIONS POUR CORRIGER LES MISES DÉFAILLANTES")
    print("=" * 60)
    
    print("1. **Examinez les images de debug :**")
    print("   - Ouvrez debug_comparative_mise_joueur*.jpg")
    print("   - Comparez visuellement les mises qui fonctionnent vs celles qui échouent")
    print("   - Identifiez les différences visuelles")
    
    print("\n2. **Ajustez la calibration :**")
    print("   - Utilisez les mises fonctionnelles comme modèle")
    print("   - Repositionnez les régions défaillantes")
    print("   - Ajustez la taille selon les mises qui fonctionnent")
    
    print("\n3. **Vérifiez les conditions :**")
    print("   - Assurez-vous que toutes les régions capturent des mises réelles")
    print("   - Vérifiez l'éclairage et la qualité d'affichage")
    print("   - Testez à différents moments")
    
    print("\n4. **Optimisez les paramètres :**")
    print("   - Ajustez les seuils de détection de couleurs")
    print("   - Améliorez le prétraitement OCR")
    print("   - Testez différentes méthodes de détection")

def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC AVANCÉ - MISES PARTIELLEMENT DÉTECTÉES")
    print("=" * 70)
    print("Analyse comparative entre mises fonctionnelles et défaillantes")
    print()
    
    # Effectuer l'analyse
    success = analyser_mises_comparatives()
    
    # Générer les recommandations
    generer_recommandations()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DU DIAGNOSTIC")
    print("-" * 70)
    
    if success:
        print("✅ Analyse comparative terminée")
        print("📁 Images de debug créées pour comparaison")
        print("🔍 Patterns identifiés entre mises fonctionnelles/défaillantes")
        print("💡 Recommandations générées")
        
        print("\n🚀 ÉTAPES SUIVANTES :")
        print("1. Examinez les images debug_comparative_*.jpg")
        print("2. Identifiez visuellement les différences")
        print("3. Utilisez les mises fonctionnelles comme modèle")
        print("4. Ajustez la calibration des régions défaillantes")
        print("5. Retestez après ajustements")
    else:
        print("❌ Erreur lors de l'analyse")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
