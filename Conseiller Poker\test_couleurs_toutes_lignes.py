#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la coloration automatique des cartes dans toutes les lignes
=================================================================

Ce script teste la nouvelle fonctionnalité qui détecte et colore
automatiquement toutes les cartes dans n'importe quelle ligne du conseiller.

Auteur: Augment Agent
Date: 2024
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QTextEdit, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QTextCharFormat, QColor

# Couleurs des cartes (identiques au conseiller)
CARD_COLORS = {
    "Cœur": QColor("#FF4444"),      # Rouge pour cœur
    "Pique": QColor("#CCCCCC"),     # Gris clair pour pique
    "Trèfle": QColor("#44FF44"),    # Vert pour trèfle
    "Carreau": QColor("#4444FF"),   # Bleu pour carreau
    "Valeur": QColor("#FFFFFF")     # Blanc pour les valeurs
}

class TestCouleursLignes(QMainWindow):
    """Fenêtre de test pour la coloration automatique des cartes"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Coloration Automatique des Cartes")
        self.setGeometry(100, 100, 900, 700)
        
        # Style sombre comme dans le conseiller
        self.setStyleSheet("""
            QMainWindow {
                background-color: #000000;
                color: white;
            }
            QTextEdit {
                background-color: #1A1A1A;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 10px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
            QLabel {
                color: white;
                font-family: 'Arial', sans-serif;
                font-size: 14px;
                padding: 10px;
            }
            QPushButton {
                background-color: #0066CC;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #0077DD;
            }
            QPushButton:pressed {
                background-color: #0055AA;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titre
        title_label = QLabel("🎨 Test de la Coloration Automatique des Cartes")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Instructions
        instructions = QLabel("""
Ce test simule l'affichage du conseiller poker avec la nouvelle fonctionnalité
de coloration automatique des cartes dans TOUTES les lignes.

Cliquez sur "Tester" pour voir un exemple d'analyse avec cartes colorées.
        """)
        layout.addWidget(instructions)
        
        # Bouton de test
        self.test_button = QPushButton("Tester la Coloration Automatique")
        self.test_button.clicked.connect(self.test_coloration)
        layout.addWidget(self.test_button)
        
        # Zone d'affichage
        self.text_display = QTextEdit()
        self.text_display.setReadOnly(True)
        layout.addWidget(self.text_display)
    
    def format_line_with_cards(self, cursor, line, default_format):
        """
        Copie de la fonction du conseiller pour formater les cartes
        """
        import re
        
        # Pattern pour détecter les cartes avec symboles Unicode
        card_pattern = r'\b(A|K|Q|J|10|[2-9])([♥♠♣♦])\b'
        
        # Trouver toutes les cartes dans la ligne
        matches = list(re.finditer(card_pattern, line))
        
        if not matches:
            # Aucune carte trouvée, afficher la ligne normalement
            cursor.insertText(line + "\n", default_format)
            return
        
        # Il y a des cartes à colorer
        last_end = 0
        
        for match in matches:
            start, end = match.span()
            
            # Insérer le texte avant la carte avec le format par défaut
            if start > last_end:
                cursor.insertText(line[last_end:start], default_format)
            
            # Extraire la carte et la colorer
            card = match.group(0)  # La carte complète (ex: "A♥")
            suit = card[-1]  # Le symbole de couleur
            
            # Créer le format pour cette carte
            card_format = QTextCharFormat()
            
            # Appliquer la couleur selon la couleur de la carte
            if suit == "♥":  # Cœur - ROUGE
                card_format.setForeground(CARD_COLORS["Cœur"])
            elif suit == "♠":  # Pique - GRIS CLAIR
                card_format.setForeground(CARD_COLORS["Pique"])
            elif suit == "♣":  # Trèfle - VERT
                card_format.setForeground(CARD_COLORS["Trèfle"])
            elif suit == "♦":  # Carreau - BLEU
                card_format.setForeground(CARD_COLORS["Carreau"])
            else:
                card_format.setForeground(CARD_COLORS["Valeur"])
            
            # Insérer la carte avec sa couleur
            cursor.insertText(card, card_format)
            
            last_end = end
        
        # Insérer le reste de la ligne après la dernière carte
        if last_end < len(line):
            cursor.insertText(line[last_end:], default_format)
        
        # Ajouter le saut de ligne
        cursor.insertText("\n")
    
    def test_coloration(self):
        """Teste la coloration automatique avec un exemple d'analyse"""
        # Effacer le contenu
        self.text_display.clear()
        
        # Créer un curseur
        cursor = self.text_display.textCursor()
        
        # Format par défaut
        default_format = QTextCharFormat()
        default_format.setForeground(QColor("#FFFFFF"))
        
        # Format pour les titres
        title_format = QTextCharFormat()
        title_format.setForeground(QColor("#FFFFFF"))
        title_format.setFontWeight(700)
        
        # Exemple d'analyse avec cartes dans différentes lignes
        analysis_lines = [
            "### Situation (14 régions détectées)",
            "Board : ● Non détectées",
            "Main : ● K♦ 2♦",
            "🎯 Pot total : 2 BB",
            "Tapis moyen : ● 26.0 (moyenne) BB (Tapis moyen)",
            "Mes jetons : 38 BB",
            "● Adversaires : J1: 10, J3: 6, J5: 31",
            "Mises : Non détectées",
            "🔥 All-in : J1: 0",
            "💡 Tapis moyen (moyenne: 26.0 BB)",
            "📊 Infos jeu: 14 régions",
            "",
            "# 🚨 **ACTION RECOMMANDÉE** 🚨",
            "### ♦ ♦ raise 2.5x (49% confiance) | 1/3 analyses** ⭐",
            "### *Main solide, relance pour isoler et construire le pot.*",
            "",
            "📊 **Analyse détaillée** :",
            "• Main actuelle : petite carte assortie (K♦2♦)",
            "• Équité estimée vs range (45-60) : ~52.5%",
            "• Pot odds : 17.9%   Cote implicite : 1.3:1",
            "",
            "Exemple avec toutes les couleurs : A♥ K♠ Q♣ J♦ 10♥ 9♠ 8♣ 7♦",
            "Paire d'As : A♥ A♠",
            "Couleur : 5♥ 7♥ 9♥ J♥ K♥",
            "Quinte : 10♠ J♣ Q♦ K♥ A♠"
        ]
        
        # Traiter chaque ligne
        for line in analysis_lines:
            if line.startswith("###"):
                cursor.insertText(line + "\n", title_format)
            elif line.startswith("#"):
                cursor.insertText(line + "\n", title_format)
            else:
                self.format_line_with_cards(cursor, line, default_format)
        
        print("🎨 Test de coloration automatique terminé")
        print("Vérifiez que les cartes sont colorées dans toutes les lignes !")

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Créer et afficher la fenêtre de test
    window = TestCouleursLignes()
    window.show()
    
    print("🎨 FENÊTRE DE TEST OUVERTE")
    print("=" * 50)
    print("Cliquez sur 'Tester' pour voir la coloration automatique")
    print("des cartes dans toutes les lignes du conseiller.")
    print()
    print("Couleurs attendues :")
    print("• Cœur (♥) : Rouge")
    print("• Pique (♠) : Gris clair")
    print("• Trèfle (♣) : Vert")
    print("• Carreau (♦) : Bleu")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
