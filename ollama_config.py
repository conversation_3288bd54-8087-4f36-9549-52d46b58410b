#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration et utilitaires pour Ollama
Optimisé pour votre RTX 3060 Ti (6Go VRAM)
"""

import subprocess
import json
import os
import psutil

class OllamaConfig:
    def __init__(self):
        self.gpu_memory = 6  # RTX 3060 Ti = 6Go VRAM
        self.system_ram = 32  # Votre RAM système
        
    def get_recommended_models(self):
        """Modèles recommandés selon votre config"""
        return {
            "léger": {
                "name": "mistral:7b",
                "size": "4.1GB",
                "vram_usage": "~4GB",
                "description": "Rapide et efficace, bon pour usage général"
            },
            "code": {
                "name": "codellama:7b", 
                "size": "3.8GB",
                "vram_usage": "~4GB",
                "description": "Spécialisé programmation, excellent pour Python"
            },
            "avancé": {
                "name": "deepseek-coder:6.7b",
                "size": "3.7GB", 
                "vram_usage": "~4GB",
                "description": "Très bon pour le code complexe"
            },
            "puissant": {
                "name": "codellama:13b",
                "size": "7.3GB",
                "vram_usage": "~6GB", 
                "description": "Plus puissant mais utilise toute votre VRAM"
            }
        }
    
    def check_system_resources(self):
        """Vérifie les ressources système"""
        print("🖥️ VÉRIFICATION SYSTÈME")
        print("-" * 30)
        
        # RAM
        ram = psutil.virtual_memory()
        print(f"💾 RAM: {ram.total // (1024**3)}GB total, {ram.available // (1024**3)}GB disponible")
        
        # CPU
        cpu_count = psutil.cpu_count()
        cpu_usage = psutil.cpu_percent(interval=1)
        print(f"🔧 CPU: {cpu_count} cœurs, {cpu_usage}% utilisé")
        
        # Disque
        disk = psutil.disk_usage('/')
        print(f"💿 Disque: {disk.free // (1024**3)}GB libre")
        
        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        if ram.available // (1024**3) >= 8:
            print("✅ Assez de RAM pour tous les modèles")
        else:
            print("⚠️ RAM limitée, utilisez des modèles 7B maximum")
            
        if disk.free // (1024**3) >= 20:
            print("✅ Assez d'espace disque")
        else:
            print("⚠️ Espace disque limité")
    
    def install_recommended_models(self):
        """Installe les modèles recommandés"""
        models = self.get_recommended_models()
        
        print("📥 INSTALLATION DES MODÈLES RECOMMANDÉS")
        print("=" * 50)
        
        for category, model_info in models.items():
            if category == "puissant":
                response = input(f"⚠️ {model_info['name']} utilise {model_info['vram_usage']} (toute votre VRAM). Installer ? (o/N): ")
                if response.lower() not in ['o', 'oui']:
                    continue
            
            print(f"\n📦 Installation de {model_info['name']} ({category})...")
            print(f"   Taille: {model_info['size']}")
            print(f"   VRAM: {model_info['vram_usage']}")
            print(f"   Usage: {model_info['description']}")
            
            try:
                result = subprocess.run(['ollama', 'pull', model_info['name']], 
                                      capture_output=True, text=True, timeout=600)
                if result.returncode == 0:
                    print(f"   ✅ {model_info['name']} installé avec succès")
                else:
                    print(f"   ❌ Erreur: {result.stderr}")
            except subprocess.TimeoutExpired:
                print(f"   ⏱️ Timeout lors de l'installation de {model_info['name']}")
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
    
    def create_launch_scripts(self):
        """Crée des scripts de lancement optimisés"""
        
        # Script pour démarrer Ollama avec optimisations GPU
        ollama_start = """@echo off
echo 🚀 Démarrage Ollama optimisé pour RTX 3060 Ti
echo.

REM Définir les variables d'environnement pour CUDA
set CUDA_VISIBLE_DEVICES=0
set OLLAMA_NUM_PARALLEL=1
set OLLAMA_MAX_LOADED_MODELS=1

echo ⚙️ Configuration GPU activée
echo 🎯 Modèles recommandés: mistral:7b, codellama:7b, deepseek-coder:6.7b
echo.

ollama serve
pause
"""
        
        # Script pour lancer l'assistant poker
        assistant_start = """@echo off
echo 🤖 Lancement Assistant Ollama Poker
echo.

REM Vérifier si Ollama fonctionne
ollama list >nul 2>&1
if errorlevel 1 (
    echo ❌ Ollama n'est pas démarré
    echo 💡 Lancez d'abord ollama_start.bat
    pause
    exit /b 1
)

echo ✅ Ollama détecté
echo 🎯 Lancement de l'assistant...
echo.

python assistant_ollama_poker.py
pause
"""
        
        try:
            with open("ollama_start.bat", "w", encoding="utf-8") as f:
                f.write(ollama_start)
            
            with open("assistant_poker_start.bat", "w", encoding="utf-8") as f:
                f.write(assistant_start)
            
            print("✅ Scripts de lancement créés:")
            print("   📄 ollama_start.bat - Démarre Ollama optimisé")
            print("   📄 assistant_poker_start.bat - Lance l'assistant poker")
            
        except Exception as e:
            print(f"❌ Erreur création scripts: {e}")
    
    def test_models_performance(self):
        """Teste les performances des modèles installés"""
        print("🧪 TEST DE PERFORMANCE DES MODÈLES")
        print("=" * 50)
        
        test_prompt = "Écris une fonction Python pour détecter les couleurs dans une image avec OpenCV."
        
        # Lister les modèles installés
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Impossible de lister les modèles")
                return
            
            models = []
            for line in result.stdout.split('\n')[1:]:  # Skip header
                if line.strip():
                    model_name = line.split()[0]
                    if model_name != "NAME":
                        models.append(model_name)
            
            if not models:
                print("❌ Aucun modèle installé")
                return
            
            print(f"📋 Modèles trouvés: {', '.join(models)}")
            print()
            
            for model in models:
                print(f"🧪 Test de {model}...")
                
                import time
                start_time = time.time()
                
                try:
                    # Test simple avec timeout
                    result = subprocess.run(['ollama', 'run', model, test_prompt], 
                                          capture_output=True, text=True, timeout=30)
                    
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    if result.returncode == 0:
                        response_length = len(result.stdout)
                        print(f"   ✅ Réponse en {duration:.1f}s ({response_length} caractères)")
                        print(f"   📊 Vitesse: ~{response_length/duration:.0f} car/s")
                    else:
                        print(f"   ❌ Erreur: {result.stderr}")
                
                except subprocess.TimeoutExpired:
                    print(f"   ⏱️ Timeout (>30s)")
                except Exception as e:
                    print(f"   ❌ Erreur: {e}")
                
                print()
        
        except Exception as e:
            print(f"❌ Erreur test: {e}")

def main():
    """Menu principal de configuration"""
    config = OllamaConfig()
    
    print("⚙️ CONFIGURATION OLLAMA POUR POKER ADVISOR")
    print("=" * 60)
    print("Optimisé pour RTX 3060 Ti (6Go VRAM) + 32Go RAM")
    print()
    
    while True:
        print("📋 OPTIONS DISPONIBLES:")
        print("1. 🖥️  Vérifier les ressources système")
        print("2. 📥 Installer les modèles recommandés")
        print("3. 📄 Créer les scripts de lancement")
        print("4. 🧪 Tester les performances des modèles")
        print("5. 📊 Voir les modèles recommandés")
        print("6. 🚪 Quitter")
        print()
        
        choice = input("🎯 Votre choix (1-6): ").strip()
        
        if choice == "1":
            config.check_system_resources()
        elif choice == "2":
            config.install_recommended_models()
        elif choice == "3":
            config.create_launch_scripts()
        elif choice == "4":
            config.test_models_performance()
        elif choice == "5":
            models = config.get_recommended_models()
            print("\n🎯 MODÈLES RECOMMANDÉS:")
            print("-" * 40)
            for category, info in models.items():
                print(f"\n📦 {category.upper()}: {info['name']}")
                print(f"   Taille: {info['size']}")
                print(f"   VRAM: {info['vram_usage']}")
                print(f"   Usage: {info['description']}")
        elif choice == "6":
            print("👋 Au revoir !")
            break
        else:
            print("❌ Choix invalide")
        
        print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    main()
