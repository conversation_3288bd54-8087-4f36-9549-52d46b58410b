#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de formatage des cartes avec couleurs
==========================================

Ce module fournit des fonctions pour formater l'affichage des cartes
avec leurs couleurs respectives dans différents types d'interfaces.

Couleurs utilisées :
- Cœur (♥) : <PERSON>
- Pi<PERSON> (♠) : <PERSON><PERSON> clair (noir invisible sur fond noir)
- Trèfle (♣) : V<PERSON>
<PERSON> <PERSON><PERSON> (♦) : Bleu

Auteur: Augment Agent
Date: 2024
"""

def format_cards_html(cards_text):
    """
    Formate les cartes avec des couleurs HTML/CSS
    
    Args:
        cards_text (str): Texte contenant les cartes (ex: "A♥ K♠ Q♣ J♦")
        
    Returns:
        str: HTML formaté avec les couleurs
    """
    if not cards_text or cards_text == "Non détecté":
        return cards_text
    
    # Diviser le texte en cartes individuelles
    cards = cards_text.split()
    formatted_cards = []
    
    for card in cards:
        if len(card) >= 2:
            # Extraire le symbole de couleur (dernier caractère)
            suit = card[-1]
            
            # Appliquer la couleur HTML selon la couleur de la carte
            if suit == "♥":  # Cœur - ROUGE
                formatted_cards.append(f'<span style="color: #FF4444;">{card}</span>')
            elif suit == "♠":  # Pique - GRIS CLAIR
                formatted_cards.append(f'<span style="color: #CCCCCC;">{card}</span>')
            elif suit == "♣":  # Trèfle - VERT
                formatted_cards.append(f'<span style="color: #44FF44;">{card}</span>')
            elif suit == "♦":  # Carreau - BLEU
                formatted_cards.append(f'<span style="color: #4444FF;">{card}</span>')
            else:
                formatted_cards.append(f'<span style="color: #FFFFFF;">{card}</span>')
        else:
            formatted_cards.append(f'<span style="color: #FFFFFF;">{card}</span>')
    
    return " ".join(formatted_cards)

def format_cards_ansi(cards_text):
    """
    Formate les cartes avec des codes couleur ANSI pour terminal
    
    Args:
        cards_text (str): Texte contenant les cartes (ex: "A♥ K♠ Q♣ J♦")
        
    Returns:
        str: Texte formaté avec codes couleur ANSI
    """
    if not cards_text or cards_text == "Non détecté":
        return cards_text
    
    # Codes couleur ANSI
    RED = '\033[91m'
    GREEN = '\033[92m'
    BLUE = '\033[94m'
    GRAY = '\033[37m'
    WHITE = '\033[97m'
    RESET = '\033[0m'
    
    # Diviser le texte en cartes individuelles
    cards = cards_text.split()
    formatted_cards = []
    
    for card in cards:
        if len(card) >= 2:
            # Extraire le symbole de couleur (dernier caractère)
            suit = card[-1]
            
            # Appliquer la couleur ANSI selon la couleur de la carte
            if suit == "♥":  # Cœur - ROUGE
                formatted_cards.append(f'{RED}{card}{RESET}')
            elif suit == "♠":  # Pique - GRIS
                formatted_cards.append(f'{GRAY}{card}{RESET}')
            elif suit == "♣":  # Trèfle - VERT
                formatted_cards.append(f'{GREEN}{card}{RESET}')
            elif suit == "♦":  # Carreau - BLEU
                formatted_cards.append(f'{BLUE}{card}{RESET}')
            else:
                formatted_cards.append(f'{WHITE}{card}{RESET}')
        else:
            formatted_cards.append(f'{WHITE}{card}{RESET}')
    
    return " ".join(formatted_cards)

def format_cards_plain_with_colors(cards_text):
    """
    Formate les cartes avec indication textuelle des couleurs
    
    Args:
        cards_text (str): Texte contenant les cartes (ex: "A♥ K♠ Q♣ J♦")
        
    Returns:
        str: Texte avec indication des couleurs
    """
    if not cards_text or cards_text == "Non détecté":
        return cards_text
    
    # Diviser le texte en cartes individuelles
    cards = cards_text.split()
    formatted_cards = []
    
    for card in cards:
        if len(card) >= 2:
            # Extraire le symbole de couleur (dernier caractère)
            suit = card[-1]
            
            # Ajouter l'indication de couleur
            if suit == "♥":  # Cœur - ROUGE
                formatted_cards.append(f'{card}(R)')
            elif suit == "♠":  # Pique - NOIR
                formatted_cards.append(f'{card}(N)')
            elif suit == "♣":  # Trèfle - VERT
                formatted_cards.append(f'{card}(V)')
            elif suit == "♦":  # Carreau - BLEU
                formatted_cards.append(f'{card}(B)')
            else:
                formatted_cards.append(card)
        else:
            formatted_cards.append(card)
    
    return " ".join(formatted_cards)

def convert_detected_cards_to_symbols(hand_cards, board_cards):
    """
    Convertit les cartes détectées au format texte vers le format avec symboles
    
    Args:
        hand_cards (list): Liste des cartes en main (ex: ["A de Cœur", "K de Pique"])
        board_cards (list): Liste des cartes du board
        
    Returns:
        tuple: (hand_symbols, board_symbols) avec symboles Unicode
    """
    # Mapping des couleurs vers les symboles
    suit_symbols = {
        "Cœur": "♥",
        "Pique": "♠", 
        "Trèfle": "♣",
        "Carreau": "♦"
    }
    
    def convert_card_list(cards):
        """Convertit une liste de cartes vers le format symbole"""
        converted = []
        for card in cards:
            if " de " in card:
                parts = card.split(" de ")
                if len(parts) == 2:
                    value, suit = parts
                    symbol = suit_symbols.get(suit, "?")
                    converted.append(f"{value}{symbol}")
                else:
                    converted.append(card)
            else:
                converted.append(card)
        return converted
    
    hand_symbols = convert_card_list(hand_cards) if hand_cards else []
    board_symbols = convert_card_list(board_cards) if board_cards else []
    
    return hand_symbols, board_symbols

def format_status_bar_cards(hand_cards, board_cards, format_type="html"):
    """
    Formate les cartes pour affichage dans une barre de statut
    
    Args:
        hand_cards (list): Liste des cartes en main
        board_cards (list): Liste des cartes du board
        format_type (str): Type de formatage ("html", "ansi", "plain")
        
    Returns:
        str: Texte formaté pour la barre de statut
    """
    # Convertir vers le format symbole
    hand_symbols, board_symbols = convert_detected_cards_to_symbols(hand_cards, board_cards)
    
    # Formater selon le type demandé
    if format_type == "html":
        hand_text = format_cards_html(" ".join(hand_symbols)) if hand_symbols else "Non détectées"
        board_text = format_cards_html(" ".join(board_symbols)) if board_symbols else "Non détectées"
        return f'Board : {board_text} | Main : {hand_text}'
    
    elif format_type == "ansi":
        hand_text = format_cards_ansi(" ".join(hand_symbols)) if hand_symbols else "Non détectées"
        board_text = format_cards_ansi(" ".join(board_symbols)) if board_symbols else "Non détectées"
        return f'Board : {board_text} | Main : {hand_text}'
    
    else:  # plain
        hand_text = format_cards_plain_with_colors(" ".join(hand_symbols)) if hand_symbols else "Non détectées"
        board_text = format_cards_plain_with_colors(" ".join(board_symbols)) if board_symbols else "Non détectées"
        return f'Board : {board_text} | Main : {hand_text}'

def test_formatage():
    """Fonction de test pour vérifier le formatage"""
    print("🃏 TEST DU FORMATAGE DES CARTES AVEC COULEURS")
    print("=" * 60)
    
    # Cartes de test
    test_cards = "A♥ K♠ Q♣ J♦ 10♥"
    test_hand = ["A de Cœur", "K de Pique"]
    test_board = ["Q de Trèfle", "J de Carreau", "10 de Cœur"]
    
    print(f"Cartes originales : {test_cards}")
    print()
    
    print("1. Formatage HTML :")
    print(format_cards_html(test_cards))
    print()
    
    print("2. Formatage ANSI (terminal) :")
    print(format_cards_ansi(test_cards))
    print()
    
    print("3. Formatage texte avec couleurs :")
    print(format_cards_plain_with_colors(test_cards))
    print()
    
    print("4. Barre de statut HTML :")
    print(format_status_bar_cards(test_hand, test_board, "html"))
    print()
    
    print("5. Barre de statut ANSI :")
    print(format_status_bar_cards(test_hand, test_board, "ansi"))
    print()
    
    print("6. Barre de statut texte :")
    print(format_status_bar_cards(test_hand, test_board, "plain"))

if __name__ == "__main__":
    test_formatage()
