#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Diagnostic pour identifier le problème de fermeture de l'application
lors de l'utilisation de la nouvelle logique avancée.
"""

import sys
import os
import traceback

def test_imports():
    """Test des imports de la logique avancée"""
    print("🔍 TEST DES IMPORTS")
    print("=" * 40)
    
    try:
        print("1. Test import poker_logic_advanced...")
        from poker_logic_advanced import AdvancedPokerLogic
        print("   ✅ poker_logic_advanced importé avec succès")
        
        print("2. Test import poker_advisor_integration...")
        from poker_advisor_integration import poker_integration
        print("   ✅ poker_advisor_integration importé avec succès")
        
        print("3. Test création instance AdvancedPokerLogic...")
        logic = AdvancedPokerLogic()
        print("   ✅ Instance AdvancedPokerLogic créée avec succès")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur d'import: {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test des fonctionnalités de base"""
    print("\n🧪 TEST DES FONCTIONNALITÉS DE BASE")
    print("=" * 40)
    
    try:
        from poker_advisor_integration import poker_integration
        
        print("1. Test évaluation main simple...")
        hand_values = ["As", "Roi"]
        hand_suits = ["Cœur", "Pique"]
        board_values = []
        board_suits = []
        
        result = poker_integration.evaluate_hand_advanced(
            hand_values, hand_suits, board_values, board_suits
        )
        
        print(f"   ✅ Résultat: {result['hand_description']}, Équité: {result['equity']:.1f}%")
        
        print("2. Test avec board...")
        board_values = ["Dame", "Valet", "10"]
        board_suits = ["Trèfle", "Carreau", "Cœur"]
        
        result = poker_integration.evaluate_hand_advanced(
            hand_values, hand_suits, board_values, board_suits
        )
        
        print(f"   ✅ Résultat: {result['hand_description']}, Équité: {result['equity']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur fonctionnalité: {e}")
        traceback.print_exc()
        return False

def test_format_conversion():
    """Test de la conversion de format des cartes"""
    print("\n🔄 TEST DE CONVERSION DE FORMAT")
    print("=" * 40)
    
    try:
        from poker_advisor_integration import poker_integration
        
        # Test différents formats de cartes
        test_cases = [
            ("As", "Cœur"),
            ("Roi", "Pique"),
            ("Dame", "Trèfle"),
            ("Valet", "Carreau"),
            ("10", "Cœur"),
            ("9", "Pique"),
            ("A", "Cœur"),  # Format anglais
            ("K", "Pique"),
        ]
        
        for value, suit in test_cases:
            try:
                converted = poker_integration.convert_card_format(value, suit)
                print(f"   ✅ {value} de {suit} → {converted}")
            except Exception as e:
                print(f"   ❌ Erreur conversion {value} de {suit}: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur conversion: {e}")
        traceback.print_exc()
        return False

def test_detector_gui_integration():
    """Test de l'intégration avec detector_gui (sans interface)"""
    print("\n🔗 TEST D'INTÉGRATION DETECTOR_GUI")
    print("=" * 40)
    
    try:
        # Simuler l'appel de la fonction calculate_hand_strength_advanced
        print("1. Test simulation calculate_hand_strength_advanced...")
        
        # Importer la fonction directement
        sys.path.append('.')
        
        # Simuler les données d'entrée
        hand_cards = ["As de Cœur", "Roi de Pique"]
        board_cards = ["Dame de Trèfle", "Valet de Carreau", "10 de Cœur"]
        
        print(f"   Main: {hand_cards}")
        print(f"   Board: {board_cards}")
        
        # Test de la logique de conversion dans calculate_hand_strength_advanced
        hand_values = []
        hand_suits = []
        
        for card in hand_cards:
            if " de " in card:
                value, suit = card.split(" de ")
                hand_values.append(value)
                hand_suits.append(suit)
        
        board_values = []
        board_suits = []
        
        for card in board_cards:
            if " de " in card:
                value, suit = card.split(" de ")
                board_values.append(value)
                board_suits.append(suit)
        
        print(f"   Hand values: {hand_values}, suits: {hand_suits}")
        print(f"   Board values: {board_values}, suits: {board_suits}")
        
        # Test de l'évaluation
        from poker_advisor_integration import poker_integration
        
        result = poker_integration.evaluate_hand_advanced(
            hand_values, hand_suits, board_values, board_suits
        )
        
        print(f"   ✅ Évaluation réussie: {result['hand_description']}")
        print(f"   ✅ Équité: {result['equity']:.1f}%")
        print(f"   ✅ Action: {result['recommendations']['action']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur intégration: {e}")
        traceback.print_exc()
        return False

def test_memory_usage():
    """Test de l'utilisation mémoire"""
    print("\n💾 TEST D'UTILISATION MÉMOIRE")
    print("=" * 40)
    
    try:
        import psutil
        import gc
        
        # Mémoire avant
        process = psutil.Process()
        mem_before = process.memory_info().rss / 1024 / 1024  # MB
        print(f"   Mémoire avant: {mem_before:.1f} MB")
        
        # Créer plusieurs instances pour tester les fuites mémoire
        from poker_advisor_integration import poker_integration
        
        for i in range(10):
            result = poker_integration.evaluate_hand_advanced(
                ["As", "Roi"], ["Cœur", "Pique"], 
                ["Dame", "Valet"], ["Trèfle", "Carreau"]
            )
        
        # Forcer le garbage collection
        gc.collect()
        
        # Mémoire après
        mem_after = process.memory_info().rss / 1024 / 1024  # MB
        print(f"   Mémoire après: {mem_after:.1f} MB")
        print(f"   Différence: {mem_after - mem_before:.1f} MB")
        
        if mem_after - mem_before > 100:  # Plus de 100MB de différence
            print("   ⚠️ Possible fuite mémoire détectée")
        else:
            print("   ✅ Utilisation mémoire normale")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur test mémoire: {e}")
        return False

def main():
    """Fonction principale de diagnostic"""
    print("🚀 DIAGNOSTIC DE CRASH DE LA LOGIQUE AVANCÉE")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: Imports
    if not test_imports():
        all_tests_passed = False
        print("\n❌ ARRÊT: Problème d'import détecté")
        return
    
    # Test 2: Fonctionnalités de base
    if not test_basic_functionality():
        all_tests_passed = False
        print("\n❌ ARRÊT: Problème de fonctionnalité détecté")
        return
    
    # Test 3: Conversion de format
    if not test_format_conversion():
        all_tests_passed = False
        print("\n⚠️ Problème de conversion détecté")
    
    # Test 4: Intégration detector_gui
    if not test_detector_gui_integration():
        all_tests_passed = False
        print("\n❌ ARRÊT: Problème d'intégration détecté")
        return
    
    # Test 5: Mémoire
    if not test_memory_usage():
        print("\n⚠️ Problème de mémoire détecté")
    
    if all_tests_passed:
        print("\n✅ TOUS LES TESTS SONT RÉUSSIS!")
        print("La logique avancée fonctionne correctement.")
        print("\nLe problème de fermeture pourrait venir d'ailleurs:")
        print("   - Problème d'interface PyQt5")
        print("   - Conflit avec d'autres modules")
        print("   - Problème de threading")
        print("   - Erreur dans la gestion des événements")
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Le problème vient probablement de la logique avancée.")
    
    print("\n📋 RECOMMANDATIONS:")
    print("1. Vérifier les logs de debug_crash.log")
    print("2. Tester l'application sans la logique avancée")
    print("3. Ajouter plus de try/catch dans calculate_hand_strength_advanced")
    print("4. Tester avec des données simplifiées")

if __name__ == "__main__":
    main()
