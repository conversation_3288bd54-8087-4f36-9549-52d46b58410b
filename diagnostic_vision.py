#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 DIAGNOSTIC VISION - DÉTECTION DU PROBLÈME
Test simple pour identifier pourquoi LLaVA ne lit pas l'image
"""

import subprocess
import base64
import os
import requests
import json

def test_image_basique():
    """Test basique de lecture d'image"""
    print("🔧 DIAGNOSTIC VISION OLLAMA")
    print("=" * 40)
    
    image_path = "capture_poker.png"
    
    # 1. Vérifier l'image
    print("1️⃣ VÉRIFICATION IMAGE")
    if not os.path.exists(image_path):
        print(f"❌ {image_path} non trouvé")
        return False
    
    size = os.path.getsize(image_path)
    print(f"✅ Image trouvée: {size / 1024:.1f} KB")
    
    # 2. Test encodage base64
    print("\n2️⃣ TEST ENCODAGE BASE64")
    try:
        with open(image_path, "rb") as f:
            image_b64 = base64.b64encode(f.read()).decode('utf-8')
        print(f"✅ Encodage OK: {len(image_b64)} caractères")
    except Exception as e:
        print(f"❌ Erreur encodage: {e}")
        return False
    
    # 3. Test simple avec prompt minimal
    print("\n3️⃣ TEST PROMPT MINIMAL")
    
    prompt_simple = "Décris cette image en une phrase."
    
    payload = {
        "model": "llava:13b",
        "prompt": prompt_simple,
        "images": [image_b64],
        "stream": False
    }
    
    try:
        print("🔄 Envoi requête...")
        response = requests.post(
            "http://localhost:11434/api/generate", 
            json=payload, 
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            reponse = result.get("response", "")
            
            print("✅ Réponse reçue:")
            print("-" * 30)
            print(reponse)
            print("-" * 30)
            
            # Analyser la réponse
            if len(reponse) < 50:
                print("⚠️ Réponse très courte - possible problème")
            elif prompt_simple.lower() in reponse.lower():
                print("❌ LLaVA recopie le prompt - ne lit pas l'image")
            elif any(word in reponse.lower() for word in ['poker', 'carte', 'table', 'jeu']):
                print("✅ LLaVA voit l'image poker !")
            else:
                print("🤔 Réponse inattendue")
                
            return True
            
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ Erreur requête: {e}")
        return False

def test_autres_modeles():
    """Test avec d'autres modèles de vision"""
    print("\n4️⃣ TEST AUTRES MODÈLES")
    
    modeles = ["llava:7b", "moondream:latest", "bakllava:latest"]
    
    # Vérifier quels modèles sont installés
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        modeles_installes = result.stdout
        
        for modele in modeles:
            if modele in modeles_installes:
                print(f"✅ {modele} disponible")
                # Test rapide
                test_modele_rapide(modele)
            else:
                print(f"❌ {modele} non installé")
                
    except Exception as e:
        print(f"❌ Erreur vérification modèles: {e}")

def test_modele_rapide(modele):
    """Test rapide d'un modèle"""
    try:
        with open("capture_poker.png", "rb") as f:
            image_b64 = base64.b64encode(f.read()).decode('utf-8')
        
        payload = {
            "model": modele,
            "prompt": "Que vois-tu dans cette image ?",
            "images": [image_b64],
            "stream": False
        }
        
        response = requests.post(
            "http://localhost:11434/api/generate", 
            json=payload, 
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            reponse = result.get("response", "")[:100] + "..."
            print(f"   📝 {modele}: {reponse}")
        else:
            print(f"   ❌ {modele}: Erreur {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ {modele}: {e}")

def solutions_recommandees():
    """Propose des solutions"""
    print("\n💡 SOLUTIONS RECOMMANDÉES")
    print("=" * 40)
    
    print("🔧 Si LLaVA ne lit pas l'image :")
    print("1. Réinstaller LLaVA: ollama pull llava:13b")
    print("2. Redémarrer Ollama: ollama serve")
    print("3. Tester avec une autre image")
    print("4. Vérifier les logs Ollama")
    
    print("\n🎯 Alternatives :")
    print("1. Utiliser votre détection OCR actuelle")
    print("2. Tester GPT-4 Vision (API OpenAI)")
    print("3. Combiner OCR + IA pour l'analyse")
    print("4. Optimiser votre système existant")

def main():
    """Fonction principale"""
    print("🔧 DIAGNOSTIC COMPLET - VISION IA")
    print("Identification du problème LLaVA")
    print("=" * 50)
    
    # Test basique
    if test_image_basique():
        print("\n✅ Test basique réussi")
    else:
        print("\n❌ Test basique échoué")
        solutions_recommandees()
        return
    
    # Test autres modèles
    test_autres_modeles()
    
    # Recommandations
    solutions_recommandees()
    
    print("\n🎉 Diagnostic terminé !")

if __name__ == "__main__":
    main()
