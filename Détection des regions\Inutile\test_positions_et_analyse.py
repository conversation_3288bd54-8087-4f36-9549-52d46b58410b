#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des positions du bouton dealer et de l'analyse détaillée
Vérifie que le joueur 1 = moi et que l'analyse est bien utilisée
"""

def test_calcul_positions():
    """Test du calcul des positions avec le joueur 1 = moi"""
    print("🔘 TEST DU CALCUL DES POSITIONS")
    print("=" * 50)
    print("Configuration: Joueur 1 = MOI, Table de 6 joueurs")
    print()
    
    # Simuler le calcul de position pour chaque position du bouton
    positions = ["UTG", "MP", "CO", "BTN", "SB", "BB"]
    max_players = 6
    my_seat = 1  # Je suis le joueur 1
    
    print("🎯 Test de toutes les positions du bouton :")
    print("-" * 40)
    
    for dealer_position in range(1, max_players + 1):
        # Calculer ma position relative au bouton
        distance_to_button = (my_seat - dealer_position) % max_players
        position_index = (3 + distance_to_button) % 6  # UTG est à distance 3 du bouton
        my_position = positions[position_index]
        
        print(f"Bouton sur Joueur {dealer_position} → Je suis en position {my_position}")
        
        # Afficher toutes les positions
        position_details = []
        for player in range(1, max_players + 1):
            player_distance = (player - dealer_position) % max_players
            player_pos_index = (3 + player_distance) % 6
            player_position = positions[player_pos_index]
            
            if player == dealer_position:
                position_details.append(f"J{player}: BTN 🔘")
            elif player == my_seat:
                position_details.append(f"J{player}: {player_position} (MOI)")
            else:
                position_details.append(f"J{player}: {player_position}")
        
        print(f"   Détail: {' | '.join(position_details)}")
        print()

def test_analyse_detaillee():
    """Test de l'extraction d'actions depuis l'analyse détaillée"""
    print("📊 TEST DE L'ANALYSE DÉTAILLÉE")
    print("=" * 50)
    
    # Exemples d'analyses détaillées typiques
    analyses_test = [
        {
            "nom": "FOLD avec montant",
            "contenu": """
### 🚨 **ACTION RECOMMANDÉE** 🚨
## ⭐ **FOLD** ⭐
### *Main trop faible face à une grosse mise*

📊 **Analyse détaillée** :
• Main actuelle : Paire faible
• Équité estimée vs range (15-25%) : ~20.0%
• Pot odds : 33.3%   Cote implicite : Défavorable
• Montant recommandé : 0 BB (se coucher)
            """,
            "attendu": "🚫 FOLD"
        },
        {
            "nom": "CALL avec montant",
            "contenu": """
### 🚨 **ACTION RECOMMANDÉE** 🚨
## ⭐ **CALL 25 BB** ⭐
### *Pot odds favorables avec main moyenne*

📊 **Analyse détaillée** :
• Main actuelle : Paire moyenne
• Équité estimée vs range (35-45%) : ~40.0%
• Pot odds : 25.0%   Cote implicite : Favorable
• Montant recommandé : 25.0 BB (suivre)
            """,
            "attendu": "📞 CALL 25BB"
        },
        {
            "nom": "RAISE avec montant",
            "contenu": """
### 🚨 **ACTION RECOMMANDÉE** 🚨
## ⭐ **RAISE 60 BB** ⭐
### *Main forte, position favorable*

📊 **Analyse détaillée** :
• Main actuelle : Paire forte
• Équité estimée vs range (70-85%) : ~77.5%
• Pot odds : 15.0%   Cote implicite : Très favorable
• Montant recommandé : 60.0 BB (relance)
            """,
            "attendu": "⬆️ RAISE 60BB"
        },
        {
            "nom": "ALL-IN",
            "contenu": """
### 🚨 **ACTION RECOMMANDÉE** 🚨
## ⭐ **ALL-IN 80 BB** ⭐
### *Main monstre, maximiser la valeur*

📊 **Analyse détaillée** :
• Main actuelle : Brelan
• Équité estimée vs range (85-95%) : ~90.0%
• Pot odds : 10.0%   Cote implicite : Excellente
• Montant recommandé : 80.0 BB (tapis)
            """,
            "attendu": "💰 ALL-IN 80BB"
        }
    ]
    
    print("🎮 Test d'extraction d'actions :")
    print("-" * 40)
    
    for test in analyses_test:
        print(f"\n📋 Test: {test['nom']}")
        print(f"   Attendu: {test['attendu']}")
        
        # Simuler l'extraction d'action
        action_extracted = extraire_action_de_analyse(test['contenu'])
        
        if action_extracted == test['attendu']:
            print(f"   ✅ SUCCÈS: {action_extracted}")
        else:
            print(f"   ❌ ÉCHEC: Obtenu '{action_extracted}', attendu '{test['attendu']}'")

def extraire_action_de_analyse(formatted_analysis):
    """Simule l'extraction d'action comme dans detector_gui.py"""
    import re
    
    # Patterns d'action (comme dans detector_gui.py)
    action_patterns = [
        r"## ⭐ \*\*(.*?)\*\* ⭐",  # Format principal
        r"\*\*Action optimale\*\* : (.*)",
        r"Action recommandée\s*:\s*(.*)",
        r"Recommandation\s*:\s*(.*)"
    ]
    
    action_found = False
    action = ""
    
    # Chercher l'action dans l'analyse détaillée
    for pattern in action_patterns:
        match = re.search(pattern, formatted_analysis, re.IGNORECASE | re.MULTILINE | re.DOTALL)
        if match:
            action = match.group(1).strip()
            # Nettoyer l'action (enlever les caractères spéciaux)
            action = re.sub(r'[*#⭐🚨]', '', action).strip()
            action_found = True
            break
    
    # Extraire les montants si disponibles
    if action_found and action:
        # Chercher des montants dans l'action
        amount_match = re.search(r'(\d+(?:\.\d+)?)\s*BB', action, re.IGNORECASE)
        if amount_match:
            amount = amount_match.group(1)
            # Nettoyer l'action de base
            base_action = re.sub(r'\d+(?:\.\d+)?\s*BB', '', action).strip()
            action = f"{base_action} {amount}BB"
    
    # Simplifier l'action (comme dans simplify_action)
    if action_found and action:
        action_lower = action.lower()
        
        if "fold" in action_lower:
            return "🚫 FOLD"
        elif "all-in" in action_lower:
            # Extraire le montant
            amount_match = re.search(r'(\d+(?:\.\d+)?)\s*bb', action_lower)
            if amount_match:
                return f"💰 ALL-IN {amount_match.group(1)}BB"
            return "💰 ALL-IN"
        elif "call" in action_lower:
            amount_match = re.search(r'(\d+(?:\.\d+)?)\s*bb', action_lower)
            if amount_match:
                return f"📞 CALL {amount_match.group(1)}BB"
            return "📞 CALL"
        elif "raise" in action_lower:
            amount_match = re.search(r'(\d+(?:\.\d+)?)\s*bb', action_lower)
            if amount_match:
                return f"⬆️ RAISE {amount_match.group(1)}BB"
            return "⬆️ RAISE"
        elif "check" in action_lower:
            return "✋ CHECK"
    
    return "⏳ ATTENDRE"

def main():
    """Fonction principale de test"""
    print("🎯 TEST COMPLET DES POSITIONS ET ANALYSE")
    print("=" * 60)
    print("Vérification que :")
    print("✅ Le joueur 1 = MOI")
    print("✅ Les positions sont calculées correctement")
    print("✅ L'analyse détaillée est bien utilisée")
    print("✅ Les montants sont extraits et affichés")
    print()
    
    # Test des positions
    test_calcul_positions()
    
    # Test de l'analyse
    test_analyse_detaillee()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES CORRECTIONS")
    print("-" * 60)
    print("✅ Position du joueur 1 = MOI corrigée")
    print("✅ Calcul des positions relatives au bouton corrigé")
    print("✅ Extraction d'actions depuis l'analyse détaillée")
    print("✅ Affichage des montants avec les actions")
    print("✅ Gestion des cas 'cartes en main non détectées'")
    print()
    print("🚀 PRÊT À TESTER :")
    print("   1. Lancez 'lancer_detector_cuda_advisor.bat'")
    print("   2. Activez le conseiller poker")
    print("   3. Testez les boutons de déplacement du dealer")
    print("   4. Vérifiez que votre position change correctement")
    print("   5. Observez les recommandations avec montants")

if __name__ == "__main__":
    main()
