#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Poker-Coach Pro v5 - Application PyQt pour analyser les situations de poker en temps réel
et fournir des recommandations stratégiques optimales.
"""

import sys
import os
import time
import re
import random
import hashlib
import json
from collections import OrderedDict
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTextEdit, QVBoxLayout,
                            QWidget, QLabel, QPushButton, QHBoxLayout, QStatusBar,
                            QGridLayout, QFrame, QSplitter, QScrollArea, QMenu, QAction,
                            QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QPoint
from PyQt5.QtGui import QFont, QColor, QTextCharFormat, QPalette, QIcon

# Chemin vers le fichier à surveiller
FILE_PATH = r"C:\Users\<USER>\PokerAdvisor\Détection des regions\export\realtime_results"

class PokerDataCache:
    """Classe pour gérer le cache des données et des analyses de poker."""

    def __init__(self, max_size=50):
        """
        Initialise le cache avec une taille maximale.

        Args:
            max_size (int): Nombre maximum d'entrées dans le cache
        """
        self.max_size = max_size
        self.cache = OrderedDict()  # Utiliser OrderedDict pour conserver l'ordre d'insertion
        self.hits = 0  # Nombre de fois où le cache a été utilisé
        self.misses = 0  # Nombre de fois où le cache n'a pas été utilisé
        self.total_requests = 0  # Nombre total de requêtes

    def get_hash(self, data):
        """
        Calcule un hash unique pour les données.

        Args:
            data (dict): Données de poker

        Returns:
            str: Hash des données
        """
        # Extraire les éléments importants pour le hash
        # Nous ne prenons que les éléments qui affectent l'analyse
        hash_data = {
            "hand_cards_text": data.get("hand_cards_text", ""),
            "board_cards_text": data.get("board_cards_text", ""),
            "pot": data.get("pot", 0),
            "pot_total": data.get("pot_total", 0),
            "effective_stack": data.get("effective_stack", 0),
            "missing_cards": data.get("missing_cards", [])
        }

        # Convertir en chaîne JSON triée pour garantir la cohérence
        json_str = json.dumps(hash_data, sort_keys=True)

        # Calculer le hash
        return hashlib.md5(json_str.encode()).hexdigest()

    def get(self, data):
        """
        Récupère les résultats d'analyse du cache si disponibles.

        Args:
            data (dict): Données de poker

        Returns:
            tuple: (analysis, formatted_analysis, from_cache) ou (None, None, False) si non trouvé
        """
        self.total_requests += 1

        # Calculer le hash des données
        data_hash = self.get_hash(data)

        # Vérifier si le hash est dans le cache
        if data_hash in self.cache:
            # Déplacer l'entrée à la fin (la plus récemment utilisée)
            entry = self.cache.pop(data_hash)
            self.cache[data_hash] = entry

            self.hits += 1
            return entry["analysis"], entry["formatted_analysis"], True

        self.misses += 1
        return None, None, False

    def put(self, data, analysis, formatted_analysis):
        """
        Ajoute une entrée au cache.

        Args:
            data (dict): Données de poker
            analysis (dict): Résultats de l'analyse
            formatted_analysis (str): Analyse formatée
        """
        # Calculer le hash des données
        data_hash = self.get_hash(data)

        # Ajouter au cache
        self.cache[data_hash] = {
            "analysis": analysis,
            "formatted_analysis": formatted_analysis,
            "timestamp": time.time()
        }

        # Si le cache dépasse la taille maximale, supprimer l'entrée la plus ancienne
        if len(self.cache) > self.max_size:
            self.cache.popitem(last=False)  # Supprimer le premier élément (le plus ancien)

    def clear(self):
        """Vide le cache."""
        self.cache.clear()
        self.hits = 0
        self.misses = 0
        self.total_requests = 0

    def get_stats(self):
        """
        Retourne les statistiques d'utilisation du cache.

        Returns:
            dict: Statistiques du cache
        """
        hit_rate = (self.hits / self.total_requests * 100) if self.total_requests > 0 else 0
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hits": self.hits,
            "misses": self.misses,
            "total_requests": self.total_requests,
            "hit_rate": hit_rate
        }

# Constantes pour les couleurs des cartes
CARD_COLORS = {
    "Cœur": QColor("#FF5555"),    # Rouge
    "Pique": QColor("#FFFFFF"),   # Noir (blanc pour visibilité)
    "Trèfle": QColor("#55FF55"),  # Vert
    "Carreau": QColor("#5555FF"),  # Bleu
    "Valeur": QColor("#FFFFFF")   # Blanc pour les lettres et chiffres des cartes
}

# Symboles Unicode pour les couleurs
SUIT_SYMBOLS = {
    "Cœur": "♥",
    "Pique": "♠",
    "Trèfle": "♣",
    "Carreau": "♦"
}

# Conversion des valeurs de cartes
CARD_VALUES = {
    "As": "A",
    "Roi": "K",
    "Dame": "Q",
    "Valet": "J",
    "10": "T",
    "9": "9",
    "8": "8",
    "7": "7",
    "6": "6",
    "5": "5",
    "4": "4",
    "3": "3",
    "2": "2"
}

# Fonctions d'analyse de poker
def parse_cards(card_text):
    """
    Analyse le texte des cartes et retourne une liste de tuples (valeur, couleur)

    Args:
        card_text (str): Texte contenant les cartes (ex: "As de Cœur, Roi de Pique")

    Returns:
        list: Liste de tuples (valeur, couleur)
    """
    if not card_text:
        return []

    cards = []
    # Diviser le texte en cartes individuelles
    card_parts = card_text.split(", ")

    for part in card_parts:
        # Rechercher la valeur et la couleur avec un pattern plus flexible
        match = re.search(r"(As|Roi|Dame|Valet|10|[2-9]) de (Cœur|Pique|Trèfle|Carreau)", part)
        if match:
            value, suit = match.groups()
            cards.append((value, suit))
            print(f"Carte détectée: {value} de {suit}")
        else:
            print(f"Carte non reconnue: {part}")

    # Afficher un résumé des cartes détectées
    if cards:
        print(f"Total des cartes détectées: {len(cards)}")
    else:
        print(f"Aucune carte détectée dans: '{card_text}'")

    return cards

def cards_to_texas_format(cards):
    """
    Convertit une liste de cartes au format Texas Hold'em

    Args:
        cards (list): Liste de tuples (valeur, couleur)

    Returns:
        list: Liste de cartes au format Texas Hold'em (ex: ["A♥", "K♠"])
    """
    texas_cards = []

    for value, suit in cards:
        # Convertir la valeur
        texas_value = CARD_VALUES.get(value, value)
        # Convertir la couleur
        texas_suit = SUIT_SYMBOLS.get(suit, "")

        # Vérifier que la conversion a réussi
        if not texas_suit:
            print(f"⚠️ Couleur non reconnue: {suit}")

        # Créer la carte au format Texas Hold'em
        texas_card = f"{texas_value}{texas_suit}"
        texas_cards.append(texas_card)
        print(f"Carte convertie: {value} de {suit} -> {texas_card}")

    return texas_cards

def evaluate_hand_strength(board_cards, hand_cards):
    """
    Évalue la force de la main

    Args:
        board_cards (list): Liste des cartes du board au format Texas Hold'em
        hand_cards (list): Liste des cartes en main au format Texas Hold'em

    Returns:
        str: Description de la force de la main
    """
    # Simuler différentes forces de main pour démonstration
    all_cards = board_cards + hand_cards

    # Vérifier les paires
    values = [card[0] for card in all_cards]
    value_counts = {value: values.count(value) for value in set(values)}

    # Vérifier les couleurs
    suits = [card[1] for card in all_cards]
    suit_counts = {suit: suits.count(suit) for suit in set(suits)}

    # Déterminer la force de la main
    if any(count >= 4 for count in value_counts.values()):
        return "Carré"
    elif any(count >= 3 for count in value_counts.values()) and any(count >= 2 for count in value_counts.values()):
        return "Full House"
    elif any(count >= 5 for count in suit_counts.values()):
        return "Couleur"
    elif len(value_counts) >= 5:  # Potentiel pour une suite
        return "Quinte possible"
    elif any(count >= 3 for count in value_counts.values()):
        return "Brelan"
    elif sum(1 for count in value_counts.values() if count >= 2) >= 2:
        return "Deux paires"
    elif any(count >= 2 for count in value_counts.values()):
        return "Paire"
    else:
        return "Hauteur"

def calculate_pot_odds(pot_size, bet_to_call):
    """
    Calcule les pot odds

    Args:
        pot_size (float): Taille du pot
        bet_to_call (float): Mise à suivre

    Returns:
        float: Pot odds en pourcentage
    """
    if bet_to_call == 0:
        return 0

    return (pot_size / (pot_size + bet_to_call)) * 100

def estimate_equity(board_cards, hand_cards):
    """
    Estime l'équité de la main contre une range raisonnable

    Args:
        board_cards (list): Liste des cartes du board au format Texas Hold'em
        hand_cards (list): Liste des cartes en main au format Texas Hold'em

    Returns:
        tuple: (min_equity, max_equity) en pourcentage
    """
    # Simuler différentes équités en fonction de la force de la main
    hand_strength = evaluate_hand_strength(board_cards, hand_cards)

    if hand_strength == "Carré":
        return (85, 95)
    elif hand_strength == "Full House":
        return (80, 90)
    elif hand_strength == "Couleur":
        return (75, 85)
    elif hand_strength == "Quinte possible":
        return (60, 75)
    elif hand_strength == "Brelan":
        return (65, 80)
    elif hand_strength == "Deux paires":
        return (55, 70)
    elif hand_strength == "Paire":
        return (40, 60)
    else:
        return (20, 40)

def recommend_action(equity, pot_odds, stack_to_pot_ratio):
    """
    Recommande une action optimale

    Args:
        equity (tuple): (min_equity, max_equity) en pourcentage
        pot_odds (float): Pot odds en pourcentage
        stack_to_pot_ratio (float): Rapport stack-to-pot

    Returns:
        tuple: (action, raison)
    """
    min_equity, max_equity = equity
    avg_equity = (min_equity + max_equity) / 2

    # Décider de l'action en fonction de l'équité et des pot odds
    if avg_equity < 25:
        return ("fold", "Main faible avec peu d'équité contre la range adverse.")

    if pot_odds > 0 and avg_equity > pot_odds:
        if avg_equity > 75:
            if stack_to_pot_ratio < 3:
                return ("all-in", "Main très forte, maximiser la valeur avec un tapis court.")
            else:
                return ("value-bet 3/4 pot", "Main très forte, extraire de la valeur.")
        elif avg_equity > 60:
            return ("value-bet 1/2 pot", "Bonne main avec équité favorable contre la range adverse.")
        else:
            return ("call", "Équité suffisante par rapport aux cotes du pot.")
    elif pot_odds == 0:
        if avg_equity > 60:
            return ("value-bet 2/3 pot", "Bonne main, prendre l'initiative.")
        elif avg_equity > 40:
            return ("check", "Main moyenne, contrôler le pot.")
        else:
            return ("check", "Main faible, voir le prochain tour gratuitement.")
    else:
        if avg_equity > 50 and stack_to_pot_ratio < 5:
            return ("call", "Équité marginale mais cote implicite favorable.")
        else:
            return ("fold", "Équité insuffisante par rapport aux cotes du pot.")

def extract_poker_data(content):
    """
    Extrait les données de poker du contenu du fichier

    Args:
        content (str): Contenu du fichier

    Returns:
        dict: Dictionnaire contenant les données de poker
    """
    data = {
        "hand_cards_text": "",
        "board_cards_text": "",
        "probability": 0,
        "action": "",
        "pot": 10,  # Valeurs par défaut
        "pot_total": 10,
        "effective_stack": 100,
        "my_stack": 100,
        "player_stacks": {},
        "player_bets": {},
        "missing_cards": []  # Liste pour suivre les cartes manquantes
    }

    # Extraire les cartes en main
    hand_match = re.search(r"Cartes en main: (.*)", content)
    if hand_match:
        data["hand_cards_text"] = hand_match.group(1).strip()

        # Vérifier si les cartes en main sont complètes (normalement 2 cartes)
        hand_cards = data["hand_cards_text"].split()
        if len(hand_cards) < 2:
            data["missing_cards"].append("main_incomplete")
            print(f"Cartes en main incomplètes: {data['hand_cards_text']}")
    else:
        data["missing_cards"].append("main")
        print("Cartes en main non détectées")

    # Extraire les cartes du board
    board_match = re.search(r"Cartes sur le board: (.*)", content)
    if board_match:
        data["board_cards_text"] = board_match.group(1).strip()

        # Vérifier si les cartes du board sont complètes (jusqu'à 5 cartes)
        board_cards = data["board_cards_text"].split()
        if len(board_cards) > 0 and len(board_cards) < 3:
            data["missing_cards"].append("board_incomplete")
            print(f"Cartes du board incomplètes: {data['board_cards_text']}")
    else:
        data["missing_cards"].append("board")
        print("Cartes du board non détectées")

    # Vérifier si les cartes détectées sont valides
    valid_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
    valid_suits = ['♥', '♠', '♣', '♦']

    # Vérifier les cartes en main
    if data["hand_cards_text"]:
        hand_cards = data["hand_cards_text"].split()
        for i, card in enumerate(hand_cards):
            if len(card) < 2 or card[-1] not in valid_suits or not any(card.startswith(v) for v in valid_values):
                print(f"Carte en main invalide détectée: {card}")
                data["missing_cards"].append(f"main_card_{i+1}_invalid")

        # Afficher un résumé des cartes en main
        print(f"Cartes en main détectées: {data['hand_cards_text']}")

    # Vérifier les cartes du board
    if data["board_cards_text"]:
        board_cards = data["board_cards_text"].split()
        for i, card in enumerate(board_cards):
            if len(card) < 2 or card[-1] not in valid_suits or not any(card.startswith(v) for v in valid_values):
                print(f"Carte du board invalide détectée: {card}")
                data["missing_cards"].append(f"board_card_{i+1}_invalid")

        # Afficher un résumé des cartes du board
        print(f"Cartes du board détectées: {data['board_cards_text']}")

    # Extraire la probabilité
    prob_match = re.search(r"Probabilité de gagner: ([0-9.]+)%", content)
    if prob_match:
        data["probability"] = float(prob_match.group(1))

    # Extraire l'action recommandée
    action_match = re.search(r"Action recommandée: (.*)", content)
    if action_match:
        data["action"] = action_match.group(1)

    # Extraire les informations sur le pot et les tapis (si disponibles)
    pot_match = re.search(r"Pot: ([0-9.]+)", content)
    if pot_match:
        data["pot"] = float(pot_match.group(1))

    pot_total_match = re.search(r"Pot total: ([0-9.]+)", content)
    if pot_total_match:
        data["pot_total"] = float(pot_total_match.group(1))

    stack_match = re.search(r"Tapis effectif: ([0-9.]+)", content)
    if stack_match:
        data["effective_stack"] = float(stack_match.group(1))

    my_stack_match = re.search(r"Mes jetons: ([0-9.]+)", content)
    if my_stack_match:
        data["my_stack"] = float(my_stack_match.group(1))

    # Extraire les tapis des joueurs
    for i in range(1, 8):
        player_stack_match = re.search(rf"Jetons joueur{i}: ([0-9.]+)", content)
        if player_stack_match:
            data["player_stacks"][f"joueur{i}"] = float(player_stack_match.group(1))

    # Extraire les mises des joueurs
    for i in range(1, 8):
        player_bet_match = re.search(rf"Mise joueur{i}: ([0-9.]+)", content)
        if player_bet_match:
            data["player_bets"][f"joueur{i}"] = float(player_bet_match.group(1))

    return data

def analyze_poker_situation(data):
    """
    Analyse la situation de poker

    Args:
        data (dict): Données de poker extraites

    Returns:
        dict: Résultats de l'analyse
    """
    results = {
        "hand_cards": [],
        "board_cards": [],
        "hand_strength": "",
        "equity": (0, 0),
        "pot_odds": 0,
        "implied_odds": "N/A",
        "recommended_action": "",
        "action_reason": "",
        "notes": []
    }

    # Analyser les cartes
    hand_cards = parse_cards(data["hand_cards_text"])
    board_cards = parse_cards(data["board_cards_text"])

    # Convertir au format Texas Hold'em
    results["hand_cards"] = cards_to_texas_format(hand_cards)
    results["board_cards"] = cards_to_texas_format(board_cards)

    # Ajouter des notes sur les cartes manquantes ou invalides
    if "missing_cards" in data:
        for issue in data["missing_cards"]:
            if issue == "main":
                results["notes"].append("⚠️ Cartes en main non détectées")
            elif issue == "main_incomplete":
                results["notes"].append(f"⚠️ Cartes en main incomplètes: {data['hand_cards_text']}")
            elif issue == "board":
                results["notes"].append("⚠️ Board non détecté")
            elif issue == "board_incomplete":
                results["notes"].append(f"⚠️ Board incomplet: {data['board_cards_text']}")
            elif issue.startswith("main_card_") and issue.endswith("_invalid"):
                card_index = issue.split("_")[2]
                results["notes"].append(f"⚠️ Carte en main {card_index} invalide")
            elif issue.startswith("board_card_") and issue.endswith("_invalid"):
                card_index = issue.split("_")[2]
                results["notes"].append(f"⚠️ Carte du board {card_index} invalide")

    # Évaluer la force de la main seulement si nous avons des cartes valides
    if results["hand_cards"] or results["board_cards"]:
        results["hand_strength"] = evaluate_hand_strength(results["board_cards"], results["hand_cards"])

        # Estimer l'équité
        results["equity"] = estimate_equity(results["board_cards"], results["hand_cards"])
    else:
        results["hand_strength"] = "Non détecté"
        results["equity"] = (0, 0)
        results["notes"].append("⚠️ Impossible d'évaluer la main: aucune carte valide détectée")

    # Calculer les pot odds (simulé)
    bet_to_call = 5  # Valeur par défaut
    results["pot_odds"] = calculate_pot_odds(data["pot"], bet_to_call)

    # Déterminer la cote implicite
    if data["effective_stack"] > data["pot"] * 3:
        results["implied_odds"] = "Favorable"
    elif data["effective_stack"] > data["pot"]:
        results["implied_odds"] = "Neutre"
    else:
        results["implied_odds"] = "Défavorable"

    # Calculer le rapport stack-to-pot
    stack_to_pot_ratio = data["effective_stack"] / data["pot"] if data["pot"] > 0 else 10

    # Recommander une action seulement si nous avons suffisamment d'informations
    if results["hand_cards"] and (results["board_cards"] or len(data["missing_cards"]) == 0):
        action, reason = recommend_action(results["equity"], results["pot_odds"], stack_to_pot_ratio)
        results["recommended_action"] = action
        results["action_reason"] = reason
    else:
        # Si nous n'avons pas assez d'informations, recommander d'attendre
        results["recommended_action"] = "attendre"
        results["action_reason"] = "Données insuffisantes pour une recommandation fiable"
        results["notes"].append("⚠️ Recommandation limitée en raison de données manquantes")

    return results

def format_poker_analysis(data, analysis):
    """
    Formate l'analyse de poker selon le template demandé

    Args:
        data (dict): Données de poker extraites
        analysis (dict): Résultats de l'analyse

    Returns:
        str: Analyse formatée
    """
    # Formater les cartes
    board_str = " ".join(analysis["board_cards"]) if analysis["board_cards"] else "Non détecté"
    hand_str = " ".join(analysis["hand_cards"]) if analysis["hand_cards"] else "Non détecté"

    # Formater l'équité
    min_equity, max_equity = analysis["equity"]

    # Éviter la division par zéro si l'équité est (0, 0)
    if min_equity == 0 and max_equity == 0:
        equity_str = "0"
        avg_equity = 0
    else:
        equity_str = f"{min_equity}–{max_equity}" if min_equity != max_equity else f"{min_equity}"
        avg_equity = (min_equity + max_equity) / 2

    # Formater les pot odds
    pot_odds_str = f"{analysis['pot_odds']:.1f}" if analysis["pot_odds"] > 0 else "N/A"

    # Formater l'action recommandée
    if analysis["recommended_action"] == "attendre":
        action_str = "ATTENDRE PLUS DE DONNÉES"
    else:
        action_str = analysis["recommended_action"].upper() if analysis["recommended_action"] else "ATTENTE DE DONNÉES"

    # Ajouter des indicateurs visuels pour les cartes manquantes
    board_indicator = "⚠️ " if "board" in data.get("missing_cards", []) or "board_incomplete" in data.get("missing_cards", []) else ""
    hand_indicator = "⚠️ " if "main" in data.get("missing_cards", []) or "main_incomplete" in data.get("missing_cards", []) else ""

    # Vérifier si des cartes sont invalides
    for issue in data.get("missing_cards", []):
        if issue.startswith("board_card_") and issue.endswith("_invalid"):
            board_indicator = "⚠️ "
        if issue.startswith("main_card_") and issue.endswith("_invalid"):
            hand_indicator = "⚠️ "

    # Construire le template
    template = f"""### Situation
Board : {board_indicator}{board_str}
Main : {hand_indicator}{hand_str}
Pot : {data['pot']} BB   Pot total : {data['pot_total']} BB
Tapis effectif : {data['effective_stack']} BB

### Analyse rapide
- Force : {analysis['hand_strength']}
- Équité estimée vs range ({equity_str}) : ~{avg_equity:.1f}%
- Pot odds : {pot_odds_str}%   Cote implicite : {analysis['implied_odds']}

### Recommandation
**Action optimale** : {action_str}
*Raison* : {analysis['action_reason']}

### Notes supplémentaires
"""

    # Ajouter les notes
    if analysis["notes"]:
        for note in analysis["notes"]:
            template += f"{note}\n"
    else:
        template += "Aucune hypothèse nécessaire, toutes les données sont disponibles."

    # Ajouter un horodatage
    template += f"\nDernière mise à jour: {time.strftime('%H:%M:%S')}"

    return template

class FileWatcher(QThread):
    """Thread pour surveiller les modifications du fichier."""
    file_changed = pyqtSignal(str)
    file_error = pyqtSignal(str)

    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.signal_file_path = os.path.join(os.path.dirname(file_path), "update_signal.txt")
        self.running = True
        self.last_modified = 0
        self.last_signal_modified = 0
        self.last_content = ""

    def run(self):
        """Surveille le fichier pour détecter les modifications."""
        while self.running:
            try:
                # Vérifier d'abord le fichier de signal s'il existe
                if os.path.exists(self.signal_file_path):
                    signal_modified = os.path.getmtime(self.signal_file_path)

                    # Si le fichier de signal a été modifié, vérifier le fichier principal
                    if signal_modified != self.last_signal_modified:
                        self.last_signal_modified = signal_modified

                        # Vérifier si le fichier principal existe
                        if os.path.exists(self.file_path):
                            # Attendre un court instant pour s'assurer que l'écriture est terminée
                            time.sleep(0.05)

                            try:
                                with open(self.file_path, 'r', encoding='utf-8') as file:
                                    content = file.read()

                                # Émettre le signal seulement si le contenu a changé
                                if content != self.last_content:
                                    self.file_changed.emit(content)
                                    self.last_content = content
                                    self.last_modified = os.path.getmtime(self.file_path)
                            except Exception as e:
                                # Si erreur de lecture, attendre un peu et réessayer
                                time.sleep(0.1)
                                try:
                                    with open(self.file_path, 'r', encoding='utf-8') as file:
                                        content = file.read()

                                    if content != self.last_content:
                                        self.file_changed.emit(content)
                                        self.last_content = content
                                        self.last_modified = os.path.getmtime(self.file_path)
                                except Exception as e2:
                                    self.file_error.emit(f"Erreur lors de la lecture du fichier après réessai: {str(e2)}")

                # Vérification traditionnelle du fichier principal
                elif os.path.exists(self.file_path):
                    current_modified = os.path.getmtime(self.file_path)

                    # Si le fichier a été modifié depuis la dernière vérification
                    if current_modified != self.last_modified:
                        try:
                            with open(self.file_path, 'r', encoding='utf-8') as file:
                                content = file.read()

                            # Émettre le signal seulement si le contenu a changé
                            if content != self.last_content:
                                self.file_changed.emit(content)
                                self.last_content = content

                            self.last_modified = current_modified
                        except Exception as e:
                            # Si erreur de lecture, attendre un peu et réessayer
                            time.sleep(0.1)
                            try:
                                with open(self.file_path, 'r', encoding='utf-8') as file:
                                    content = file.read()

                                if content != self.last_content:
                                    self.file_changed.emit(content)
                                    self.last_content = content
                                    self.last_modified = current_modified
                            except Exception as e2:
                                self.file_error.emit(f"Erreur lors de la lecture du fichier après réessai: {str(e2)}")
                else:
                    # Si le fichier n'existe pas, on attend qu'il soit créé
                    self.file_error.emit(f"Le fichier {self.file_path} n'existe pas. En attente de création...")
                    time.sleep(1)

                # Attendre un court instant avant de vérifier à nouveau
                time.sleep(0.05)

            except Exception as e:
                self.file_error.emit(f"Erreur lors de la surveillance du fichier: {str(e)}")
                time.sleep(1)

    def stop(self):
        """Arrête le thread."""
        self.running = False
        self.wait()


class PokerAdvisorApp(QMainWindow):
    """Application principale pour le conseiller poker."""

    def __init__(self):
        super().__init__()
        self.update_count = 0
        self.current_data = None
        self.current_analysis = None
        self.previous_analyses = []  # Historique des analyses pour le context window
        self.continuous_detection = False  # État de la détection continue
        self.file_check_timer = QTimer()  # Timer pour vérifier les modifications du fichier
        self.file_check_timer.timeout.connect(self.check_file_changes)
        self.last_modified_time = 0  # Horodatage de la dernière modification du fichier

        # Initialiser le cache
        self.data_cache = PokerDataCache(max_size=100)  # Cache pour 100 analyses différentes
        self.cache_enabled = True  # Activer/désactiver le cache
        self.cache_hits = 0  # Nombre de fois où le cache a été utilisé

        self.init_ui()
        self.start_file_watcher()

    def init_ui(self):
        """Initialise l'interface utilisateur."""
        # Configuration de la fenêtre principale
        self.setWindowTitle("Poker-Coach Pro v5")
        self.setGeometry(100, 100, 900, 700)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #000000;
                color: white;
            }
            QLabel {
                color: white;
            }
            QTextEdit {
                background-color: #1A1A1A;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', monospace;
            }
            QScrollBar:vertical {
                background: #1A1A1A;
                width: 12px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #555555;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # En-tête
        header_label = QLabel("Poker-Coach Pro v5 - Analyse en temps réel")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("color: #FFFFFF; margin: 10px; font-weight: bold;")
        main_layout.addWidget(header_label)

        # Zone de texte pour afficher l'analyse
        self.analysis_display = QTextEdit()
        self.analysis_display.setReadOnly(True)
        self.analysis_display.setFont(QFont("Consolas", 12))
        self.analysis_display.setStyleSheet("""
            background-color: #1A1A1A;
            color: #FFFFFF;
            border: 1px solid #333333;
            padding: 10px;
            line-height: 1.5;
        """)
        main_layout.addWidget(self.analysis_display)

        # Zone pour l'historique des analyses
        self.history_display = QTextEdit()
        self.history_display.setReadOnly(True)
        self.history_display.setFont(QFont("Consolas", 10))
        self.history_display.setStyleSheet("""
            background-color: #1A1A1A;
            color: #999999;
            border: 1px solid #333333;
            padding: 5px;
            max-height: 150px;
        """)
        self.history_display.setMaximumHeight(150)
        self.history_display.setPlaceholderText("Historique des analyses précédentes...")
        main_layout.addWidget(self.history_display)

        # Barre d'état
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("background-color: #333333; color: white;")
        self.setStatusBar(self.status_bar)

        # Créer des labels pour les informations supplémentaires
        self.file_status_label = QLabel("Statut: En attente de données")
        self.file_status_label.setStyleSheet("color: white; padding: 0 10px;")

        self.update_count_label = QLabel("Mises à jour: 0")
        self.update_count_label.setStyleSheet("color: white; padding: 0 10px;")

        self.last_update_label = QLabel("Dernière mise à jour: -")
        self.last_update_label.setStyleSheet("color: white; padding: 0 10px;")

        # Ajouter les labels à la barre d'état
        self.status_bar.addPermanentWidget(self.file_status_label)
        self.status_bar.addPermanentWidget(self.update_count_label)
        self.status_bar.addPermanentWidget(self.last_update_label)

        self.status_bar.showMessage("Poker-Coach Pro v5 prêt à analyser")



        # Boutons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.clear_button = QPushButton("Effacer l'historique")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #333333;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #444444;
            }
            QPushButton:pressed {
                background-color: #222222;
            }
        """)
        self.clear_button.clicked.connect(self.clear_history)

        self.refresh_button = QPushButton("Rafraîchir l'analyse")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #0066CC;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0077DD;
            }
            QPushButton:pressed {
                background-color: #0055AA;
            }
        """)
        self.refresh_button.clicked.connect(self.manual_refresh)

        # Bouton pour démarrer/arrêter la détection continue
        self.start_detection_button = QPushButton("Démarrer la détection continue")
        self.start_detection_button.setStyleSheet("""
            QPushButton {
                background-color: #55AA55;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #77CC77;
            }
            QPushButton:pressed {
                background-color: #338833;
            }
        """)
        self.start_detection_button.clicked.connect(self.toggle_continuous_detection)

        # Bouton pour activer/désactiver le cache
        self.toggle_cache_button = QPushButton("Cache: Activé")
        self.toggle_cache_button.setStyleSheet("""
            QPushButton {
                background-color: #AA55AA;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #CC77CC;
            }
            QPushButton:pressed {
                background-color: #883388;
            }
        """)
        self.toggle_cache_button.clicked.connect(self.toggle_cache)

        button_layout.addWidget(self.clear_button)
        button_layout.addWidget(self.refresh_button)
        button_layout.addWidget(self.start_detection_button)
        button_layout.addWidget(self.toggle_cache_button)
        main_layout.addLayout(button_layout)

        # Ajouter un label pour les statistiques du cache avec menu contextuel
        self.cache_stats_label = QLabel("Cache: 0/0 (0%)")
        self.cache_stats_label.setStyleSheet("color: #AAAAAA; font-size: 10px; margin-top: 5px;")
        self.cache_stats_label.setAlignment(Qt.AlignRight)
        self.cache_stats_label.setContextMenuPolicy(Qt.CustomContextMenu)
        self.cache_stats_label.customContextMenuRequested.connect(self.show_cache_context_menu)
        main_layout.addWidget(self.cache_stats_label)

    def start_file_watcher(self):
        """Démarre le thread de surveillance du fichier."""
        self.file_watcher = FileWatcher(FILE_PATH)
        self.file_watcher.file_changed.connect(self.process_file_content)
        self.file_watcher.file_error.connect(self.show_error)
        self.file_watcher.start()

    def process_file_content(self, content):
        """
        Traite le contenu du fichier et met à jour l'affichage

        Args:
            content (str): Contenu du fichier
        """
        try:
            # Extraire les données de poker
            data = extract_poker_data(content)

            # Vérifier si les données sont dans le cache
            cache_used = False
            if self.cache_enabled:
                analysis, formatted_analysis, from_cache = self.data_cache.get(data)
                if from_cache:
                    cache_used = True
                    self.cache_hits += 1

                    # Mettre à jour l'horodatage dans l'analyse formatée
                    formatted_analysis = self.update_timestamp_in_analysis(formatted_analysis)

            # Si les données ne sont pas dans le cache, les analyser
            if not self.cache_enabled or not cache_used:
                # Analyser la situation
                analysis = analyze_poker_situation(data)

                # Formater l'analyse
                formatted_analysis = format_poker_analysis(data, analysis)

                # Ajouter au cache si activé
                if self.cache_enabled:
                    self.data_cache.put(data, analysis, formatted_analysis)

            # Stocker les données et l'analyse courantes
            self.current_data = data
            self.current_analysis = analysis

            # Mettre à jour l'affichage
            self.update_display(formatted_analysis)

            # Ajouter l'analyse à l'historique
            self.add_to_history(formatted_analysis)

            # Mettre à jour les informations de statut
            self.update_count += 1
            self.update_count_label.setText(f"Mises à jour: {self.update_count}")

            # Mettre à jour l'horodatage
            current_time = time.strftime('%H:%M:%S')
            self.last_update_label.setText(f"Dernière mise à jour: {current_time}")

            # Mettre à jour le statut avec l'information du cache
            if cache_used:
                self.file_status_label.setText("Statut: Données en cache")
                self.status_bar.showMessage(f"Analyse depuis le cache: {current_time} (Hits: {self.cache_hits})")
            else:
                self.file_status_label.setText("Statut: Données analysées")
                self.status_bar.showMessage(f"Analyse complétée: {current_time}")

        except Exception as e:
            self.show_error(f"Erreur lors du traitement des données: {str(e)}")

    def update_timestamp_in_analysis(self, formatted_analysis):
        """
        Met à jour l'horodatage dans l'analyse formatée.

        Args:
            formatted_analysis (str): Analyse formatée

        Returns:
            str: Analyse formatée avec horodatage mis à jour
        """
        # Remplacer l'horodatage existant par l'horodatage actuel
        current_time = time.strftime('%H:%M:%S')

        # Utiliser une expression régulière pour remplacer la ligne d'horodatage
        updated_analysis = re.sub(
            r"Dernière mise à jour: \d{2}:\d{2}:\d{2}",
            f"Dernière mise à jour: {current_time}",
            formatted_analysis
        )

        return updated_analysis

    def update_display(self, formatted_analysis):
        """
        Met à jour l'affichage avec l'analyse formatée

        Args:
            formatted_analysis (str): Analyse formatée
        """
        # Effacer le contenu actuel
        self.analysis_display.clear()

        # Créer un curseur pour formater le texte
        cursor = self.analysis_display.textCursor()

        # Formats pour les différentes sections
        header_format = QTextCharFormat()
        header_format.setForeground(QColor("#FFFFFF"))
        header_format.setFontWeight(QFont.Bold)

        board_format = QTextCharFormat()
        board_format.setForeground(QColor("#FFFFFF"))

        hand_format = QTextCharFormat()
        hand_format.setForeground(QColor("#FFFFFF"))

        stats_format = QTextCharFormat()
        stats_format.setForeground(QColor("#AAAAAA"))

        action_format = QTextCharFormat()
        action_format.setForeground(QColor("#FFFF55"))
        action_format.setFontWeight(QFont.Bold)

        reason_format = QTextCharFormat()
        reason_format.setForeground(QColor("#AAAAAA"))
        reason_format.setFontItalic(True)

        notes_format = QTextCharFormat()
        notes_format.setForeground(QColor("#999999"))

        # Traiter chaque ligne de l'analyse
        lines = formatted_analysis.split('\n')
        for i, line in enumerate(lines):
            # Appliquer le format approprié selon le contenu de la ligne
            if line.startswith("###"):
                # En-tête de section
                cursor.insertText(line + "\n", header_format)
            elif line.startswith("Board :"):
                # Ligne du board
                prefix = "Board : "
                cursor.insertText(prefix, board_format)

                # Formater les cartes du board avec leurs couleurs respectives
                cards_text = line[len(prefix):].strip()
                self.format_cards(cursor, cards_text)
                cursor.insertText("\n")
            elif line.startswith("Main :"):
                # Ligne de la main
                prefix = "Main : "
                cursor.insertText(prefix, hand_format)

                # Formater les cartes de la main avec leurs couleurs respectives
                cards_text = line[len(prefix):].strip()
                self.format_cards(cursor, cards_text)
                cursor.insertText("\n")
            elif line.startswith("**Action optimale**"):
                # Ligne de l'action recommandée
                cursor.insertText(line + "\n", action_format)
            elif line.startswith("*Raison*"):
                # Ligne de la raison
                cursor.insertText(line + "\n", reason_format)
            elif line.startswith("- "):
                # Ligne de statistique
                cursor.insertText(line + "\n", stats_format)
            elif line.startswith("Hypothèse :"):
                # Ligne d'hypothèse
                cursor.insertText(line + "\n", notes_format)
            else:
                # Autres lignes
                if i < len(lines) - 1 or line.strip():  # Éviter d'ajouter une ligne vide à la fin
                    cursor.insertText(line + "\n", board_format)

    def format_cards(self, cursor, cards_text):
        """
        Formate les cartes avec leurs couleurs respectives

        Args:
            cursor (QTextCursor): Curseur pour insérer le texte formaté
            cards_text (str): Texte contenant les cartes
        """
        if not cards_text or cards_text == "Non détecté":
            cursor.insertText(cards_text)
            return

        # Diviser le texte en cartes individuelles
        cards = cards_text.split()

        for i, card in enumerate(cards):
            if len(card) >= 2:
                # Extraire la valeur (tous les caractères sauf le dernier) et la couleur (le dernier caractère)
                # La valeur n'est pas utilisée directement car nous affichons la carte complète
                # mais nous l'extrayons pour la documentation
                _ = card[:-1]  # Lettres et chiffres (1,2,3,4,5) en blanc
                suit = card[-1]    # Symbole de la couleur

                # Créer le format pour cette carte
                card_format = QTextCharFormat()

                # Appliquer la couleur appropriée pour le symbole de la couleur
                if suit == "♥":  # Cœur
                    card_format.setForeground(CARD_COLORS["Cœur"])
                elif suit == "♠":  # Pique
                    card_format.setForeground(CARD_COLORS["Pique"])
                elif suit == "♣":  # Trèfle
                    card_format.setForeground(CARD_COLORS["Trèfle"])
                elif suit == "♦":  # Carreau
                    card_format.setForeground(CARD_COLORS["Carreau"])
                else:
                    card_format.setForeground(CARD_COLORS["Valeur"])  # Blanc pour les valeurs non reconnues

                # Insérer la carte avec le format approprié
                cursor.insertText(card, card_format)

                # Ajouter un espace entre les cartes
                if i < len(cards) - 1:
                    cursor.insertText(" ")
            else:
                # Si le format n'est pas reconnu, afficher tel quel en blanc
                value_format = QTextCharFormat()
                value_format.setForeground(CARD_COLORS["Valeur"])
                cursor.insertText(card, value_format)
                if i < len(cards) - 1:
                    cursor.insertText(" ")

        # Ajouter un log pour le débogage
        print(f"Cartes formatées: {cards_text}")

    def add_to_history(self, formatted_analysis):
        """
        Ajoute l'analyse courante à l'historique

        Args:
            formatted_analysis (str): Analyse formatée
        """
        # Limiter la taille de l'historique
        if len(self.previous_analyses) >= 5:
            self.previous_analyses.pop(0)

        # Ajouter l'analyse courante à l'historique
        timestamp = time.strftime("%H:%M:%S")
        summary = f"[{timestamp}] "

        # Extraire les informations importantes
        board_match = re.search(r"Board : (.*)", formatted_analysis)
        hand_match = re.search(r"Main : (.*)", formatted_analysis)
        action_match = re.search(r"\*\*Action optimale\*\* : (.*)", formatted_analysis)

        if board_match:
            summary += f"Board: {board_match.group(1).strip()} | "

        if hand_match:
            summary += f"Main: {hand_match.group(1).strip()} | "

        if action_match:
            summary += f"Action: {action_match.group(1).strip()}"

        self.previous_analyses.append(summary)

        # Mettre à jour l'affichage de l'historique
        self.history_display.clear()
        for i, analysis in enumerate(reversed(self.previous_analyses)):
            self.history_display.append(analysis)
            if i < len(self.previous_analyses) - 1:
                self.history_display.append("---")

    def show_error(self, error_message):
        """Affiche un message d'erreur dans la barre d'état."""
        self.status_bar.showMessage(error_message)
        self.file_status_label.setText("Statut: Erreur")

    def clear_history(self):
        """Efface l'historique des analyses."""
        self.previous_analyses = []
        self.history_display.clear()
        self.status_bar.showMessage("Historique effacé")

    def manual_refresh(self):
        """Rafraîchit manuellement le contenu."""
        try:
            if os.path.exists(FILE_PATH):
                with open(FILE_PATH, 'r', encoding='utf-8') as file:
                    content = file.read()

                # Traiter le contenu sans incrémenter le compteur de mises à jour
                old_count = self.update_count

                # Forcer une nouvelle analyse en désactivant temporairement le cache
                if self.cache_enabled:
                    old_cache_state = self.cache_enabled
                    self.cache_enabled = False
                    self.process_file_content(content)
                    self.cache_enabled = old_cache_state
                else:
                    self.process_file_content(content)

                # Restaurer le compteur
                self.update_count = old_count
                self.update_count_label.setText(f"Mises à jour: {self.update_count}")

                # Mettre à jour les statistiques du cache
                self.update_cache_stats()

                self.status_bar.showMessage(f"Rafraîchi manuellement: {time.strftime('%H:%M:%S')}")
            else:
                self.status_bar.showMessage(f"Le fichier {FILE_PATH} n'existe pas")
                self.file_status_label.setText("Statut: Fichier non trouvé")
        except Exception as e:
            self.status_bar.showMessage(f"Erreur lors du rafraîchissement: {str(e)}")

    def check_file_changes(self):
        """Vérifie si le fichier a été modifié et le traite si c'est le cas."""
        try:
            if os.path.exists(FILE_PATH):
                current_modified = os.path.getmtime(FILE_PATH)

                # Si le fichier a été modifié depuis la dernière vérification
                if current_modified != self.last_modified_time:
                    with open(FILE_PATH, 'r', encoding='utf-8') as file:
                        content = file.read()

                    # Traiter le contenu
                    self.process_file_content(content)

                    # Mettre à jour l'horodatage
                    self.last_modified_time = current_modified

                    # Mettre à jour le statut
                    self.file_status_label.setText("Statut: Mise à jour automatique")

                    # Mettre à jour les statistiques du cache
                    self.update_cache_stats()
            else:
                self.file_status_label.setText("Statut: Fichier non trouvé")
        except Exception as e:
            self.status_bar.showMessage(f"Erreur lors de la vérification du fichier: {str(e)}")

    def start_continuous_detection(self):
        """Démarre la détection continue."""
        if not self.continuous_detection:
            self.continuous_detection = True
            self.file_check_timer.start(1000)  # Vérifier toutes les secondes
            self.status_bar.showMessage("Détection continue démarrée")
            self.start_detection_button.setText("Arrêter la détection continue")
            self.start_detection_button.setStyleSheet("""
                QPushButton {
                    background-color: #FF5555;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #FF7777;
                }
                QPushButton:pressed {
                    background-color: #DD3333;
                }
            """)

    def stop_continuous_detection(self):
        """Arrête la détection continue."""
        if self.continuous_detection:
            self.continuous_detection = False
            self.file_check_timer.stop()
            self.status_bar.showMessage("Détection continue arrêtée")
            self.start_detection_button.setText("Démarrer la détection continue")
            self.start_detection_button.setStyleSheet("""
                QPushButton {
                    background-color: #55AA55;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #77CC77;
                }
                QPushButton:pressed {
                    background-color: #338833;
                }
            """)

    def toggle_continuous_detection(self):
        """Bascule entre démarrage et arrêt de la détection continue."""
        if self.continuous_detection:
            self.stop_continuous_detection()
        else:
            self.start_continuous_detection()

    def toggle_cache(self):
        """Active ou désactive le cache."""
        self.cache_enabled = not self.cache_enabled

        if self.cache_enabled:
            self.toggle_cache_button.setText("Cache: Activé")
            self.toggle_cache_button.setStyleSheet("""
                QPushButton {
                    background-color: #AA55AA;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #CC77CC;
                }
                QPushButton:pressed {
                    background-color: #883388;
                }
            """)
            self.status_bar.showMessage("Cache activé")
        else:
            self.toggle_cache_button.setText("Cache: Désactivé")
            self.toggle_cache_button.setStyleSheet("""
                QPushButton {
                    background-color: #AA5555;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #CC7777;
                }
                QPushButton:pressed {
                    background-color: #883333;
                }
            """)
            self.status_bar.showMessage("Cache désactivé")

        # Mettre à jour les statistiques du cache
        self.update_cache_stats()

    def update_cache_stats(self):
        """Met à jour les statistiques du cache."""
        if self.cache_enabled:
            stats = self.data_cache.get_stats()
            hit_rate = stats["hit_rate"]
            self.cache_stats_label.setText(
                f"Cache: {stats['hits']}/{stats['total_requests']} ({hit_rate:.1f}%) - Taille: {stats['size']}/{stats['max_size']}"
            )
        else:
            self.cache_stats_label.setText("Cache: Désactivé")

    def clear_cache(self):
        """Vide le cache."""
        self.data_cache.clear()
        self.cache_hits = 0
        self.update_cache_stats()
        self.status_bar.showMessage("Cache vidé")

    def show_cache_context_menu(self, position):
        """Affiche un menu contextuel pour le cache."""
        menu = QMenu()

        # Action pour vider le cache
        clear_action = QAction("Vider le cache", self)
        clear_action.triggered.connect(self.clear_cache)
        menu.addAction(clear_action)

        # Action pour activer/désactiver le cache
        toggle_action = QAction("Désactiver le cache" if self.cache_enabled else "Activer le cache", self)
        toggle_action.triggered.connect(self.toggle_cache)
        menu.addAction(toggle_action)

        # Action pour afficher les statistiques détaillées
        stats_action = QAction("Afficher les statistiques", self)
        stats_action.triggered.connect(self.show_cache_stats_dialog)
        menu.addAction(stats_action)

        # Afficher le menu
        menu.exec_(self.cache_stats_label.mapToGlobal(position))

    def show_cache_stats_dialog(self):
        """Affiche une boîte de dialogue avec les statistiques détaillées du cache."""
        stats = self.data_cache.get_stats()

        message = f"""Statistiques du cache:

- Taille actuelle: {stats['size']} entrées
- Taille maximale: {stats['max_size']} entrées
- Utilisation: {stats['size'] / stats['max_size'] * 100:.1f}%

- Requêtes totales: {stats['total_requests']}
- Hits (cache utilisé): {stats['hits']}
- Misses (cache non utilisé): {stats['misses']}
- Taux de hit: {stats['hit_rate']:.1f}%

Le cache est actuellement {"activé" if self.cache_enabled else "désactivé"}.
"""

        QMessageBox.information(self, "Statistiques du cache", message)

    def closeEvent(self, event):
        """Gère l'événement de fermeture de la fenêtre."""
        # Arrêter le thread de surveillance
        if hasattr(self, 'file_watcher'):
            self.file_watcher.stop()

        # Arrêter le timer de détection continue
        if hasattr(self, 'file_check_timer') and self.file_check_timer.isActive():
            self.file_check_timer.stop()

        event.accept()


def main():
    """Fonction principale pour démarrer l'application."""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Style moderne

    window = PokerAdvisorApp()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
