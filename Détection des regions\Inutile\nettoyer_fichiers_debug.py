#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nettoyeur des fichiers de debug et test restants
Déplace les fichiers de debug, test et diagnostic vers Inutile
"""

import os
import shutil
import sys
from pathlib import Path

def deplacer_fichiers_debug_test():
    """Déplace les fichiers de debug et test vers Inutile"""
    print("🧹 NETTOYAGE FICHIERS DEBUG ET TEST")
    print("=" * 50)
    
    dossier_inutile = Path("Inutile")
    if not dossier_inutile.exists():
        dossier_inutile.mkdir()
    
    # Fichiers à déplacer (debug, test, diagnostic)
    fichiers_a_deplacer = [
        # Scripts de test
        'test_ameliorations_finales.py',
        'test_ameliorations_rapide.py',
        'test_boutons_pseudos_ameliores.py',
        'test_conseiller_complet.py',
        'test_correctif_tapis_court.py',
        'test_corrections_finales.py',
        'test_correction_rouge_noir.py',
        'test_couleurs_cartes.py',
        'test_crash_simulation.py',
        'test_detection_boutons_final.py',
        'test_detection_bouton_orange.py',
        'test_html_fix.py',
        'test_integration.py',
        'test_interface_agrandie.py',
        'test_interface_simplifiee_detector.py',
        'test_mises_debug.py',
        'test_mises_fonctionnement.py',
        'test_mises_simple.py',
        'test_positions_et_analyse.py',
        'test_rapide_mises.py',
        'test_region_specifique.py',
        'test_tracker_intelligent.py',
        
        # Scripts de diagnostic
        'diagnostic_calibration_vs_detection.py',
        'diagnostic_crash_detection.py',
        'diagnostic_crash_logique.py',
        'diagnostic_mises_jaunes.py',
        'diagnostic_mises_joueurs.py',
        'diagnostic_mises_partielles.py',
        'diagnostic_surveillance.py',
        
        # Scripts d'analyse
        'analyse_ameliorations.py',
        'analyse_simple.py',
        
        # Scripts de correction/installation
        'correctif_tapis_court.py',
        'install_easyocr.py',
        'install_paddle_cuda.py',
        'install_tesseract.py',
        'install_tesseract_simple.py',
        'integration_detection_existante.py',
        'verification_integration_complete.py',
        
        # Scripts de lancement alternatifs
        'lancer_conseiller_avec_tracker.py',
        'lancer_conseiller_temps_reel.py',
        'lancer_cuda_force.py',
        'lancer_detector_safe.py',
        'lancer_detector_securise.py',
        'lancer_mode_securise.py',
        
        # Images de debug
        'debug_button_bouton_joueur1.jpg',
        'debug_button_bouton_joueur2.jpg',
        'debug_button_bouton_joueur3.jpg',
        'debug_button_bouton_joueur4.jpg',
        'debug_button_bouton_joueur5.jpg',
        'debug_button_bouton_joueur6.jpg',
        'debug_button_bouton_joueur7.jpg',
        'debug_button_bouton_moi.jpg',
        
        # Images de calibration debug
        'debug_calibration_mise_joueur1.jpg',
        'debug_calibration_mise_joueur1_hsv.jpg',
        'debug_calibration_mise_joueur2.jpg',
        'debug_calibration_mise_joueur2_hsv.jpg',
        'debug_calibration_mise_joueur3.jpg',
        'debug_calibration_mise_joueur3_hsv.jpg',
        'debug_calibration_mise_joueur4.jpg',
        'debug_calibration_mise_joueur4_hsv.jpg',
        'debug_calibration_mise_joueur5.jpg',
        'debug_calibration_mise_joueur5_hsv.jpg',
        'debug_calibration_mise_joueur6.jpg',
        'debug_calibration_mise_joueur6_hsv.jpg',
        'debug_calibration_mise_joueur7.jpg',
        'debug_calibration_mise_joueur7_hsv.jpg',
        
        # Images de debug jaune
        'debug_jaune_mise_joueur1.jpg',
        'debug_jaune_mise_joueur1_mask.jpg',
        'debug_jaune_mise_joueur2.jpg',
        'debug_jaune_mise_joueur2_mask.jpg',
        'debug_jaune_mise_joueur3.jpg',
        'debug_jaune_mise_joueur3_mask.jpg',
        'debug_jaune_mise_joueur4.jpg',
        'debug_jaune_mise_joueur4_mask.jpg',
        'debug_jaune_mise_joueur5.jpg',
        'debug_jaune_mise_joueur5_mask.jpg',
        'debug_jaune_mise_joueur6.jpg',
        'debug_jaune_mise_joueur6_mask.jpg',
        'debug_jaune_mise_joueur7.jpg',
        'debug_jaune_mise_joueur7_mask.jpg',
        
        # Images de debug mise
        'debug_mise_joueur1.jpg',
        'debug_mise_joueur1_preprocessed.jpg',
        'debug_mise_joueur2.jpg',
        'debug_mise_joueur2_preprocessed.jpg',
        'debug_mise_joueur3.jpg',
        'debug_mise_joueur3_preprocessed.jpg',
        'debug_mise_joueur4.jpg',
        'debug_mise_joueur4_preprocessed.jpg',
        'debug_mise_joueur5.jpg',
        'debug_mise_joueur5_preprocessed.jpg',
        'debug_mise_joueur6.jpg',
        'debug_mise_joueur6_preprocessed.jpg',
        'debug_mise_joueur7.jpg',
        'debug_mise_joueur7_preprocessed.jpg',
        
        # Autres images de debug/test
        'resume_debug_mises.jpg',
        'test_image_debug.jpg',
        'test_preview_display.jpg',
        'test_preview_resized.jpg',
        'test_simple.jpg',
        
        # Logs et fichiers temporaires
        'debug_crash.log',
        'poker_players.db',
        
        # GUI alternative
        'detector_gui_all_regions.py'
    ]
    
    fichiers_deplaces = 0
    fichiers_non_trouves = 0
    erreurs = 0
    
    print(f"🔍 Recherche de {len(fichiers_a_deplacer)} fichiers à déplacer...")
    
    for fichier in fichiers_a_deplacer:
        fichier_path = Path(fichier)
        
        if fichier_path.exists():
            try:
                destination = dossier_inutile / fichier
                
                # Si le fichier existe déjà dans Inutile, ajouter un suffixe
                if destination.exists():
                    base_name = fichier_path.stem
                    extension = fichier_path.suffix
                    counter = 1
                    while destination.exists():
                        destination = dossier_inutile / f"{base_name}_{counter}{extension}"
                        counter += 1
                
                shutil.move(str(fichier_path), str(destination))
                print(f"📦 DÉPLACÉ: {fichier}")
                fichiers_deplaces += 1
                
            except Exception as e:
                print(f"❌ ERREUR: {fichier} - {e}")
                erreurs += 1
        else:
            fichiers_non_trouves += 1
    
    return fichiers_deplaces, fichiers_non_trouves, erreurs

def nettoyer_dossier_installation():
    """Déplace le dossier Installation vers Inutile"""
    print(f"\n📁 NETTOYAGE DOSSIER INSTALLATION")
    print("-" * 40)
    
    installation_path = Path("Installation")
    if installation_path.exists():
        try:
            destination = Path("Inutile") / "Installation"
            if destination.exists():
                destination = Path("Inutile") / "Installation_backup"
            
            shutil.move(str(installation_path), str(destination))
            print(f"📁 DÉPLACÉ: Installation/ → Inutile/")
            return True
        except Exception as e:
            print(f"❌ ERREUR: Installation/ - {e}")
            return False
    else:
        print("ℹ️ Dossier Installation non trouvé")
        return True

def lister_fichiers_restants():
    """Liste les fichiers restants dans le dossier"""
    print(f"\n📋 FICHIERS RESTANTS DANS LE DOSSIER")
    print("-" * 50)
    
    dossier_courant = Path(".")
    fichiers_restants = []
    
    for item in dossier_courant.iterdir():
        if item.name not in ["Inutile", "__pycache__"]:
            fichiers_restants.append(item.name)
    
    fichiers_restants.sort()
    
    print(f"📊 Total: {len(fichiers_restants)} fichiers/dossiers")
    print()
    
    # Séparer par catégories
    scripts_essentiels = []
    autres_fichiers = []
    
    for fichier in fichiers_restants:
        if any(essential in fichier for essential in [
            'detector.py', 'poker_advisor', 'monte_carlo', 'gto_solver',
            'range_analyzer', 'variance_calculator', 'session_analyzer',
            'learning_system', 'monitor_app', 'realtime_advisor',
            'lancer_detector_cuda_advisor.bat'
        ]):
            scripts_essentiels.append(fichier)
        else:
            autres_fichiers.append(fichier)
    
    print("✅ SCRIPTS ESSENTIELS:")
    for script in scripts_essentiels:
        print(f"   📄 {script}")
    
    if autres_fichiers:
        print(f"\n📁 AUTRES FICHIERS ({len(autres_fichiers)}):")
        for fichier in autres_fichiers[:10]:  # Limiter l'affichage
            print(f"   📄 {fichier}")
        if len(autres_fichiers) > 10:
            print(f"   ... et {len(autres_fichiers) - 10} autres")

def main():
    """Fonction principale"""
    print("🧹 NETTOYEUR FICHIERS DEBUG ET TEST")
    print("=" * 60)
    print("Déplace les fichiers de debug, test et diagnostic vers Inutile")
    print()
    
    # Vérifier qu'on est dans le bon dossier
    if not Path("detector.py").exists():
        print("❌ ERREUR: Ce script doit être exécuté dans le dossier 'Détection des regions'")
        return False
    
    # Déplacer les fichiers debug/test
    deplaces, non_trouves, erreurs = deplacer_fichiers_debug_test()
    
    # Nettoyer le dossier Installation
    installation_ok = nettoyer_dossier_installation()
    
    # Lister les fichiers restants
    lister_fichiers_restants()
    
    # Résumé final
    print(f"\n" + "=" * 60)
    print("📊 RÉSUMÉ DU NETTOYAGE DEBUG/TEST")
    print("-" * 60)
    print(f"📦 Fichiers déplacés: {deplaces}")
    print(f"ℹ️ Fichiers non trouvés: {non_trouves}")
    print(f"❌ Erreurs: {erreurs}")
    print(f"📁 Dossier Installation: {'✅ Déplacé' if installation_ok else '❌ Erreur'}")
    
    if erreurs == 0:
        print("\n🎉 NETTOYAGE DEBUG/TEST TERMINÉ AVEC SUCCÈS!")
        print("   Le dossier est maintenant très propre")
        print("   Seuls les fichiers essentiels restent")
    else:
        print(f"\n⚠️ NETTOYAGE TERMINÉ AVEC {erreurs} ERREUR(S)")
    
    print(f"\n💡 CONSEIL:")
    print("   Vous pouvez maintenant utiliser uniquement:")
    print("   • lancer_detector_cuda_advisor.bat")
    print("   • Les scripts essentiels listés ci-dessus")
    
    return erreurs == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
