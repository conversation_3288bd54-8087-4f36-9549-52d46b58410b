#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exemple d'intégration des couleurs de cartes dans différentes interfaces
======================================================================

Ce fichier montre comment intégrer l'affichage coloré des cartes
dans différents types d'interfaces utilisateur.

Auteur: Augment Agent
Date: 2024
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QStatusBar, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QTextCharFormat, QColor

# Importer le module de formatage
from format_cartes_couleurs import format_status_bar_cards, convert_detected_cards_to_symbols

# Couleurs pour PyQt5
CARD_COLORS = {
    "Cœur": QColor("#FF4444"),      # Rouge pour cœur
    "Pique": QColor("#CCCCCC"),     # Gris clair pour pique
    "Trèfle": QColor("#44FF44"),    # Vert pour trèfle
    "Carreau": QColor("#4444FF"),   # Bleu pour carreau
    "Valeur": QColor("#FFFFFF")     # Blanc pour les valeurs
}

class ExempleIntegrationCouleurs(QMainWindow):
    """Exemple d'intégration des couleurs dans une interface PyQt5"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Exemple Intégration Couleurs Cartes")
        self.setGeometry(100, 100, 800, 400)
        
        # Style sombre
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
            QStatusBar {
                background-color: #1e1e1e;
                color: white;
                border-top: 1px solid #555;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
            QLabel {
                color: white;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                padding: 10px;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Labels d'exemple
        self.info_label = QLabel("Exemple d'affichage des cartes avec couleurs dans différentes interfaces")
        layout.addWidget(self.info_label)
        
        # Barre de statut
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Tester l'affichage
        self.test_affichage_couleurs()
    
    def test_affichage_couleurs(self):
        """Teste l'affichage des couleurs dans la barre de statut"""
        # Cartes de test
        hand_cards = ["A de Cœur", "K de Pique"]
        board_cards = ["Q de Trèfle", "J de Carreau", "10 de Cœur"]
        
        # Convertir vers le format symbole
        hand_symbols, board_symbols = convert_detected_cards_to_symbols(hand_cards, board_cards)
        
        # Créer le texte de statut avec HTML
        status_html = format_status_bar_cards(hand_cards, board_cards, "html")
        
        # Afficher dans la barre de statut (PyQt5 supporte le HTML)
        self.status_bar.showMessage(status_html)
        
        # Afficher aussi dans le label principal
        self.info_label.setText(f"""
Exemple d'affichage des cartes avec couleurs :

1. Cartes détectées :
   Main : {hand_cards}
   Board : {board_cards}

2. Cartes avec symboles :
   Main : {' '.join(hand_symbols)}
   Board : {' '.join(board_symbols)}

3. Formatage HTML (dans la barre de statut) :
   {status_html}

4. Les couleurs devraient être visibles dans la barre de statut en bas.
        """)

def exemple_integration_detector_gui():
    """
    Exemple d'intégration dans detector_gui.py
    
    Ajoutez cette fonction dans votre detector_gui.py pour mettre à jour
    la barre de statut avec les couleurs des cartes.
    """
    
    code_exemple = '''
# Dans detector_gui.py, ajoutez cette méthode à la classe DetectorGUI :

def update_status_bar_with_colored_cards(self, results):
    """Met à jour la barre de statut avec les cartes colorées"""
    try:
        # Importer le module de formatage
        from format_cartes_couleurs import format_status_bar_cards
        
        # Extraire les cartes des résultats
        hand_cards = []
        board_cards = []
        
        # Traiter les cartes en main
        for name, data in results.items():
            if (name.startswith("carte_") or name.startswith("hand_card_")) and data.get("text"):
                card_text = data['text'].strip()
                card_color = self.get_suit_name(data.get('colors', []))
                card_str = f"{card_text} de {card_color}"
                hand_cards.append(card_str)
        
        # Traiter les cartes du board
        for name, data in results.items():
            if name.startswith("card_") and not name.startswith("card_hand_") and data.get("text"):
                card_text = data['text'].strip()
                card_color = self.get_suit_name(data.get('colors', []))
                card_str = f"{card_text} de {card_color}"
                board_cards.append(card_str)
        
        # Formater pour la barre de statut
        status_text = format_status_bar_cards(hand_cards, board_cards, "html")
        
        # Mettre à jour la barre de statut
        self.status_bar.showMessage(status_text)
        
    except Exception as e:
        print(f"Erreur lors de la mise à jour de la barre de statut: {e}")
        self.status_bar.showMessage("Erreur affichage cartes")

# Puis appelez cette méthode après la détection :
# self.update_status_bar_with_colored_cards(results)
    '''
    
    return code_exemple

def exemple_integration_conseiller():
    """
    Exemple d'intégration dans le conseiller poker
    """
    
    code_exemple = '''
# Dans poker_advisor_app.py, vous pouvez utiliser la fonction format_cards existante
# qui applique déjà les couleurs correctement.

# Pour une barre de statut supplémentaire, ajoutez :

def update_status_with_colored_cards(self, board_text, hand_text):
    """Met à jour une barre de statut avec les cartes colorées"""
    from format_cartes_couleurs import format_cards_html
    
    # Formater les cartes avec HTML
    board_html = format_cards_html(board_text) if board_text else "Non détectées"
    hand_html = format_cards_html(hand_text) if hand_text else "Non détectées"
    
    # Créer le texte de statut
    status_html = f"Board : {board_html} | Main : {hand_html}"
    
    # Afficher dans la barre de statut (si elle supporte HTML)
    self.status_bar.showMessage(status_html)
    '''
    
    return code_exemple

def main():
    """Fonction principale pour tester l'intégration"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Créer et afficher la fenêtre d'exemple
    window = ExempleIntegrationCouleurs()
    window.show()
    
    # Afficher les exemples de code dans la console
    print("🎨 EXEMPLES D'INTÉGRATION DES COULEURS")
    print("=" * 60)
    print()
    
    print("1. Code pour detector_gui.py :")
    print(exemple_integration_detector_gui())
    print()
    
    print("2. Code pour conseiller poker :")
    print(exemple_integration_conseiller())
    print()
    
    print("3. Fenêtre d'exemple ouverte - vérifiez la barre de statut !")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
