#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Poker Advisor - Module de détection Ollama + LLaVA COMPLET
==========================================================

Module complet utilisant Ollama + LLaVA pour détecter :
- Montants (pot_total blanc, pot jaune, jetons, mises)
- Pseudos (avec chiffres acceptés)
- Boutons dealer (blanc avec D)
- Statut all-in (rouge)

Optimisé pour CUDA et toutes les régions sauf cartes.

Auteur: Augment Agent
Date: 2025
"""

import os
import cv2
import numpy as np
import time
import json
import logging
from typing import Dict
import requests
import base64
from io import BytesIO
from PIL import Image

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaVisionDetectorComplet:
    """Détecteur complet utilisant Ollama + LLaVA avec CUDA"""
    
    def __init__(self, ollama_url: str = "http://localhost:11434", model: str = "llava:7b", use_cuda: bool = True):
        """
        Initialise le détecteur Ollama complet
        
        Args:
            ollama_url (str): URL du serveur Ollama local
            model (str): Modèle LLaVA à utiliser
            use_cuda (bool): Utiliser CUDA pour l'accélération GPU
        """
        self.ollama_url = ollama_url
        self.model = model
        self.use_cuda = use_cuda
        self.session = requests.Session()
        
        # Configuration CUDA pour Ollama
        if self.use_cuda:
            logger.info("🔥 Mode CUDA activé pour Ollama")
        
        # Vérifier la connexion Ollama
        self._check_ollama_connection()
        
        # Statistiques
        self.stats = {
            "total_detections": 0,
            "successful_detections": 0,
            "failed_detections": 0,
            "average_time": 0.0
        }
        
        logger.info(f"✅ OllamaVisionDetectorComplet initialisé avec {model}")
    
    def _check_ollama_connection(self):
        """Vérifie que Ollama est accessible"""
        try:
            response = self.session.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                available_models = [m["name"] for m in models]
                logger.info(f"🔗 Connexion Ollama OK. Modèles disponibles: {available_models}")
                
                if self.model not in available_models:
                    logger.warning(f"⚠️ Modèle {self.model} non trouvé. Modèles disponibles: {available_models}")
                    if available_models:
                        self.model = available_models[0]
                        logger.info(f"🔄 Utilisation du modèle: {self.model}")
            else:
                raise Exception(f"Erreur HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ Impossible de se connecter à Ollama: {e}")
            logger.error("💡 Assurez-vous qu'Ollama est installé et lancé avec: ollama serve")
            raise
    
    def _image_to_base64(self, image: np.ndarray) -> str:
        """Convertit une image OpenCV en base64"""
        try:
            # Convertir BGR vers RGB
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image
            
            # Convertir en PIL Image
            pil_image = Image.fromarray(image_rgb)
            
            # Convertir en base64
            buffer = BytesIO()
            pil_image.save(buffer, format="PNG")
            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return img_base64
            
        except Exception as e:
            logger.error(f"❌ Erreur conversion image vers base64: {e}")
            raise
    
    def detect_amounts_enhanced(self, image: np.ndarray, region_name: str = "unknown") -> Dict:
        """
        Détection améliorée des montants avec gestion spécifique par région
        """
        start_time = time.time()
        
        try:
            img_base64 = self._image_to_base64(image)
            
            # Prompt adaptatif selon la région
            color_hint = ""
            decimal_hint = ""
            
            if "pot_total" in region_name.lower():
                color_hint = "ATTENTION: Le pot total est affiché en BLANC sur fond sombre."
                decimal_hint = "Les montants de pot sont souvent entiers ou avec décimales: 42, 42.8, 100.5"
            elif "pot" in region_name.lower() and "total" not in region_name.lower():
                color_hint = "ATTENTION: Le pot peut être affiché en JAUNE/ORANGE."
                decimal_hint = "Les montants de pot sont souvent entiers ou avec décimales: 42, 42.8, 100.5"
            elif "jetons" in region_name.lower():
                color_hint = "ATTENTION: Les jetons sont généralement en BLANC ou JAUNE/ORANGE."
                decimal_hint = "Les jetons sont souvent avec décimales: 0.5, 1.2, 42.8, 100.0"
            elif "mise" in region_name.lower():
                color_hint = "ATTENTION: Les mises peuvent être en JAUNE/ORANGE ou BLANC."
                decimal_hint = "Les mises sont souvent avec décimales: 0.5, 1.2, 2.8, 5.0"
            
            prompt = f"""Tu es un expert en reconnaissance de montants dans les jeux de poker en ligne.

ANALYSE CETTE IMAGE et détecte UNIQUEMENT les montants/chiffres visibles.

{color_hint}

RÈGLES STRICTES:
1. Cherche les chiffres qui représentent des montants (jetons, mises, pot)
2. {decimal_hint}
3. ATTENTION AUX DÉCIMALES: Si tu vois "7" seul, c'est probablement "0.7"
4. Si tu vois un seul chiffre comme "7", "8", "5", vérifie s'il y a un point ou virgule avant
5. Les montants de poker sont TRÈS souvent avec décimales: 0.5, 1.2, 2.8, etc.
6. Ignore tout texte qui n'est pas un montant (pseudos, cartes, boutons)
7. Sois très précis sur les chiffres ET les décimales
8. Si l'image est vide ou floue, retourne null

RÉPONSE ATTENDUE (format JSON):
{{
    "montant_detecte": "montant exact trouvé ou null",
    "confiance": "score de 0 à 1",
    "format": "type de format détecté (decimal, K, M, entier, etc.)",
    "couleur_detectee": "couleur du texte (blanc, jaune, orange, etc.)",
    "decimal_probable": "true si c'est probablement une décimale",
    "position": "description de où se trouve le montant"
}}

EXEMPLES SPÉCIFIQUES:
Si tu vois "0.7" → {{"montant_detecte": "0.7", "confiance": 0.95, "format": "decimal", "couleur_detectee": "blanc", "decimal_probable": false, "position": "centre"}}
Si tu vois "7" seul → {{"montant_detecte": "0.7", "confiance": 0.75, "format": "decimal_probable", "couleur_detectee": "blanc", "decimal_probable": true, "position": "centre"}}
Si tu vois "42.8" → {{"montant_detecte": "42.8", "confiance": 0.95, "format": "decimal", "couleur_detectee": "blanc", "position": "centre"}}
Si tu vois "1.5K" → {{"montant_detecte": "1.5K", "confiance": 0.90, "format": "K", "couleur_detectee": "jaune", "decimal_probable": false, "position": "coin"}}
Si aucun montant → {{"montant_detecte": null, "confiance": 0.0, "format": null, "couleur_detectee": null, "decimal_probable": false, "position": null}}"""

            # Appel à Ollama avec CUDA
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [img_base64],
                "stream": False,
                "options": {
                    "temperature": 0.05,  # Très peu de créativité
                    "top_p": 0.9,
                    "num_gpu": 1 if self.use_cuda else 0  # Utiliser GPU si CUDA activé
                }
            }
            
            response = self.session.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=45  # Plus de temps pour CUDA
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")
                
                detection_result = self._parse_amount_response(response_text, region_name)
                detection_time = time.time() - start_time
                detection_result["detection_time"] = detection_time
                detection_result["region_name"] = region_name
                
                self._update_stats(detection_time, True)
                logger.info(f"💰 MONTANT DÉTECTÉ [{region_name}]: {detection_result}")
                return detection_result
                
            else:
                logger.error(f"❌ Erreur Ollama: {response.status_code}")
                self._update_stats(time.time() - start_time, False)
                return self._empty_amount_result(region_name)
                
        except Exception as e:
            logger.error(f"❌ Erreur détection montant: {e}")
            self._update_stats(time.time() - start_time, False)
            return self._empty_amount_result(region_name)
    
    def detect_player_names_enhanced(self, image: np.ndarray, region_name: str = "unknown") -> Dict:
        """
        Détection améliorée des pseudos avec acceptation des chiffres
        """
        start_time = time.time()
        
        try:
            img_base64 = self._image_to_base64(image)
            
            prompt = """Tu es un expert en reconnaissance de pseudos de joueurs dans les jeux de poker en ligne.

ANALYSE CETTE IMAGE et détecte UNIQUEMENT les pseudos/noms de joueurs visibles.

RÈGLES STRICTES:
1. Cherche les noms d'utilisateurs/pseudos de joueurs
2. Les pseudos peuvent contenir: lettres, chiffres, tirets, underscores, points
3. Longueur typique: 3-20 caractères
4. ACCEPTER ABSOLUMENT LES PSEUDOS AVEC CHIFFRES: Player123, TomZ666, User42, ABC123, etc.
5. Les pseudos peuvent commencer par une lettre OU un chiffre
6. Exemples valides: "Player1", "123User", "Tom-666", "User_42", "ABC123", "XxX_666", "Pro2024"
7. Ignore les montants purs (comme "0.7", "100", "1.5K", "42.8")
8. Ignore les cartes isolées (comme "A", "K", "Q", "J" seuls)
9. Ignore les boutons d'action (FOLD, CALL, RAISE, CHECK)
10. Si l'image est vide ou floue, retourne null

RÉPONSE ATTENDUE (format JSON):
{{
    "pseudo_detecte": "pseudo exact trouvé ou null",
    "confiance": "score de 0 à 1",
    "longueur": "nombre de caractères",
    "contient_chiffres": "true/false",
    "type_pseudo": "description du type de pseudo",
    "position": "description de où se trouve le pseudo"
}}

EXEMPLES SPÉCIFIQUES:
Si tu vois "Player123" → {{"pseudo_detecte": "Player123", "confiance": 0.95, "longueur": 9, "contient_chiffres": true, "type_pseudo": "lettres_chiffres", "position": "centre"}}
Si tu vois "TomZ-666" → {{"pseudo_detecte": "TomZ-666", "confiance": 0.90, "longueur": 8, "contient_chiffres": true, "type_pseudo": "lettres_tiret_chiffres", "position": "gauche"}}
Si tu vois "User42" → {{"pseudo_detecte": "User42", "confiance": 0.90, "longueur": 6, "contient_chiffres": true, "type_pseudo": "lettres_chiffres", "position": "centre"}}
Si tu vois "ABC" → {{"pseudo_detecte": "ABC", "confiance": 0.85, "longueur": 3, "contient_chiffres": false, "type_pseudo": "lettres_seulement", "position": "centre"}}
Si tu vois "123Pro" → {{"pseudo_detecte": "123Pro", "confiance": 0.85, "longueur": 6, "contient_chiffres": true, "type_pseudo": "chiffres_lettres", "position": "centre"}}
Si tu vois juste "7" ou "0.7" → {{"pseudo_detecte": null, "confiance": 0.0, "longueur": 0, "contient_chiffres": false, "type_pseudo": null, "position": null}}
Si aucun pseudo → {{"pseudo_detecte": null, "confiance": 0.0, "longueur": 0, "contient_chiffres": false, "type_pseudo": null, "position": null}}"""

            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [img_base64],
                "stream": False,
                "options": {
                    "temperature": 0.05,
                    "top_p": 0.9,
                    "num_gpu": 1 if self.use_cuda else 0
                }
            }
            
            response = self.session.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")
                
                detection_result = self._parse_player_response(response_text, region_name)
                detection_time = time.time() - start_time
                detection_result["detection_time"] = detection_time
                detection_result["region_name"] = region_name
                
                self._update_stats(detection_time, True)
                logger.info(f"👤 PSEUDO DÉTECTÉ [{region_name}]: {detection_result}")
                return detection_result
                
            else:
                logger.error(f"❌ Erreur Ollama: {response.status_code}")
                self._update_stats(time.time() - start_time, False)
                return self._empty_player_result(region_name)
                
        except Exception as e:
            logger.error(f"❌ Erreur détection pseudo: {e}")
            self._update_stats(time.time() - start_time, False)
            return self._empty_player_result(region_name)

    def detect_button_dealer(self, image: np.ndarray, region_name: str = "unknown") -> Dict:
        """Détecte le bouton dealer (blanc avec lettre D)"""
        start_time = time.time()

        try:
            img_base64 = self._image_to_base64(image)

            prompt = """Tu es un expert en reconnaissance de boutons dealer dans les jeux de poker en ligne.

ANALYSE CETTE IMAGE et détecte le bouton dealer.

RÈGLES STRICTES:
1. Le bouton dealer est un cercle ou bouton BLANC
2. Il contient la lettre "D" (pour Dealer)
3. Le "D" peut être noir sur fond blanc ou blanc sur fond sombre
4. Ignore tous les autres éléments (cartes, montants, pseudos)
5. Si l'image est vide ou floue, retourne false

RÉPONSE ATTENDUE (format JSON):
{{
    "bouton_detecte": "true/false",
    "lettre_d_visible": "true/false",
    "confiance": "score de 0 à 1",
    "couleur_fond": "couleur du fond du bouton",
    "forme": "description de la forme (cercle, carré, etc.)",
    "position": "description de où se trouve le bouton"
}}

EXEMPLES:
Si tu vois un bouton blanc avec "D" → {{"bouton_detecte": true, "lettre_d_visible": true, "confiance": 0.95, "couleur_fond": "blanc", "forme": "cercle", "position": "centre"}}
Si tu vois juste un cercle blanc sans "D" → {{"bouton_detecte": true, "lettre_d_visible": false, "confiance": 0.70, "couleur_fond": "blanc", "forme": "cercle", "position": "centre"}}
Si aucun bouton → {{"bouton_detecte": false, "lettre_d_visible": false, "confiance": 0.0, "couleur_fond": null, "forme": null, "position": null}}"""

            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [img_base64],
                "stream": False,
                "options": {
                    "temperature": 0.05,
                    "top_p": 0.9,
                    "num_gpu": 1 if self.use_cuda else 0
                }
            }

            response = self.session.post(f"{self.ollama_url}/api/generate", json=payload, timeout=45)

            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")

                detection_result = self._parse_button_response(response_text, region_name)
                detection_time = time.time() - start_time
                detection_result["detection_time"] = detection_time
                detection_result["region_name"] = region_name

                self._update_stats(detection_time, True)
                logger.info(f"🔘 BOUTON DÉTECTÉ [{region_name}]: {detection_result}")
                return detection_result

            else:
                logger.error(f"❌ Erreur Ollama: {response.status_code}")
                self._update_stats(time.time() - start_time, False)
                return self._empty_button_result(region_name)

        except Exception as e:
            logger.error(f"❌ Erreur détection bouton: {e}")
            self._update_stats(time.time() - start_time, False)
            return self._empty_button_result(region_name)

    def detect_allin_status(self, image: np.ndarray, region_name: str = "unknown") -> Dict:
        """Détecte le statut all-in (texte rouge)"""
        start_time = time.time()

        try:
            img_base64 = self._image_to_base64(image)

            prompt = """Tu es un expert en reconnaissance de statut all-in dans les jeux de poker en ligne.

ANALYSE CETTE IMAGE et détecte si un joueur est en all-in.

RÈGLES STRICTES:
1. Le statut all-in apparaît en texte ROUGE
2. Peut être écrit "ALL-IN", "ALLIN", "All In", "ALL IN", etc.
3. Apparaît généralement sur ou près des jetons du joueur
4. Ignore les montants normaux en blanc/jaune
5. Sois très précis sur la couleur rouge
6. Si l'image est vide ou floue, retourne false

RÉPONSE ATTENDUE (format JSON):
{{
    "allin_detecte": "true/false",
    "texte_allin": "texte exact trouvé ou null",
    "confiance": "score de 0 à 1",
    "couleur_texte": "couleur du texte détecté",
    "style_texte": "description du style (gras, normal, etc.)",
    "position": "description de où se trouve le texte"
}}

EXEMPLES:
Si tu vois "ALL-IN" en rouge → {{"allin_detecte": true, "texte_allin": "ALL-IN", "confiance": 0.95, "couleur_texte": "rouge", "style_texte": "gras", "position": "centre"}}
Si tu vois "ALLIN" en rouge → {{"allin_detecte": true, "texte_allin": "ALLIN", "confiance": 0.90, "couleur_texte": "rouge", "style_texte": "normal", "position": "centre"}}
Si aucun all-in → {{"allin_detecte": false, "texte_allin": null, "confiance": 0.0, "couleur_texte": null, "style_texte": null, "position": null}}"""

            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [img_base64],
                "stream": False,
                "options": {
                    "temperature": 0.05,
                    "top_p": 0.9,
                    "num_gpu": 1 if self.use_cuda else 0
                }
            }

            response = self.session.post(f"{self.ollama_url}/api/generate", json=payload, timeout=45)

            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")

                detection_result = self._parse_allin_response(response_text, region_name)
                detection_time = time.time() - start_time
                detection_result["detection_time"] = detection_time
                detection_result["region_name"] = region_name

                self._update_stats(detection_time, True)
                logger.info(f"🔥 ALL-IN DÉTECTÉ [{region_name}]: {detection_result}")
                return detection_result

            else:
                logger.error(f"❌ Erreur Ollama: {response.status_code}")
                self._update_stats(time.time() - start_time, False)
                return self._empty_allin_result(region_name)

        except Exception as e:
            logger.error(f"❌ Erreur détection all-in: {e}")
            self._update_stats(time.time() - start_time, False)
            return self._empty_allin_result(region_name)
