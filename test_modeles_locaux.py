#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST RÉEL AVEC VOTRE IMAGE POKER
Analyse comparative des modèles locaux sur votre capture d'écran
"""

import subprocess
import time
import json
import base64
import os
from typing import Dict, List

class TestImagePoker:
    def __init__(self):
        self.modeles_testes = [
            "mistral:7b",
            "codellama:7b", 
            "deepseek-coder:6.7b",
            "llama3:8b",
            "qwen:7b"
        ]
        
        self.code_test_poker = '''
def detect_colors_fast(self, image):
    """Détecte les couleurs dans une région d'image pour poker"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Définir les plages de couleurs
    lower_orange = np.array([10, 100, 100])
    upper_orange = np.array([25, 255, 255])
    
    # <PERSON><PERSON><PERSON> le masque
    mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)
    
    # Calculer le pourcentage
    total_pixels = image.shape[0] * image.shape[1]
    orange_pixels = cv2.countNonZero(mask_orange)
    pourcentage = (orange_pixels / total_pixels) * 100
    
    if pourcentage > 5.0:  # Seuil peut-être trop élevé ?
        return "orange détecté"
    return "rien détecté"
'''
        
        self.prompt_analyse = f"""Tu es un expert en programmation Python et vision par ordinateur. 

Analyse ce code de détection de couleurs pour une application de poker et identifie :

1. 🐛 Problèmes potentiels
2. ⚡ Optimisations possibles  
3. 🎯 Améliorations recommandées
4. 💡 Bonnes pratiques

Code à analyser :
```python
{self.code_test_poker}
```

Réponds de manière concise et technique en français."""

    def tester_modele(self, modele: str) -> Dict:
        """Teste un modèle spécifique"""
        print(f"🧪 Test de {modele}...")
        
        try:
            start_time = time.time()
            
            # Exécuter Ollama avec le modèle
            result = subprocess.run([
                'ollama', 'run', modele, self.prompt_analyse
            ], capture_output=True, text=True, timeout=60)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                return {
                    "modele": modele,
                    "succes": True,
                    "reponse": response,
                    "duree": duration,
                    "longueur_reponse": len(response),
                    "vitesse": len(response) / duration if duration > 0 else 0
                }
            else:
                return {
                    "modele": modele,
                    "succes": False,
                    "erreur": result.stderr,
                    "duree": duration
                }
                
        except subprocess.TimeoutExpired:
            return {
                "modele": modele,
                "succes": False,
                "erreur": "Timeout (>60s)",
                "duree": 60
            }
        except Exception as e:
            return {
                "modele": modele,
                "succes": False,
                "erreur": str(e),
                "duree": 0
            }

    def verifier_modeles_installes(self) -> List[str]:
        """Vérifie quels modèles sont installés"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lignes = result.stdout.split('\n')[1:]  # Skip header
                modeles_installes = []
                for ligne in lignes:
                    if ligne.strip():
                        nom_modele = ligne.split()[0]
                        if nom_modele != "NAME":
                            modeles_installes.append(nom_modele)
                return modeles_installes
            return []
        except:
            return []

    def installer_modeles_manquants(self, modeles_installes: List[str]):
        """Propose d'installer les modèles manquants"""
        modeles_manquants = [m for m in self.modeles_testes if m not in modeles_installes]
        
        if modeles_manquants:
            print(f"\n📥 Modèles manquants: {', '.join(modeles_manquants)}")
            response = input("Voulez-vous les installer ? (o/N): ")
            
            if response.lower() in ['o', 'oui']:
                for modele in modeles_manquants:
                    print(f"📦 Installation de {modele}...")
                    try:
                        subprocess.run(['ollama', 'pull', modele], check=True)
                        print(f"✅ {modele} installé")
                    except:
                        print(f"❌ Erreur installation {modele}")

    def executer_tests(self) -> List[Dict]:
        """Exécute tous les tests"""
        print("🔍 VÉRIFICATION DES MODÈLES INSTALLÉS")
        print("-" * 50)
        
        modeles_installes = self.verifier_modeles_installes()
        print(f"✅ Modèles installés: {', '.join(modeles_installes)}")
        
        # Installer les modèles manquants si souhaité
        self.installer_modeles_manquants(modeles_installes)
        
        # Re-vérifier après installation
        modeles_installes = self.verifier_modeles_installes()
        modeles_a_tester = [m for m in self.modeles_testes if m in modeles_installes]
        
        if not modeles_a_tester:
            print("❌ Aucun modèle à tester disponible")
            return []
        
        print(f"\n🧪 TESTS EN COURS")
        print("-" * 50)
        print(f"Modèles à tester: {', '.join(modeles_a_tester)}")
        print()
        
        resultats = []
        for modele in modeles_a_tester:
            resultat = self.tester_modele(modele)
            resultats.append(resultat)
            
            if resultat["succes"]:
                print(f"✅ {modele}: {resultat['duree']:.1f}s, {resultat['longueur_reponse']} chars")
            else:
                print(f"❌ {modele}: {resultat['erreur']}")
        
        return resultats

    def generer_rapport(self, resultats: List[Dict]):
        """Génère un rapport comparatif"""
        print("\n📊 RAPPORT COMPARATIF")
        print("=" * 60)
        
        # Trier par succès puis par vitesse
        resultats_succes = [r for r in resultats if r["succes"]]
        resultats_succes.sort(key=lambda x: x["duree"])
        
        if not resultats_succes:
            print("❌ Aucun test réussi")
            return
        
        print("🏆 CLASSEMENT PAR VITESSE:")
        print("-" * 30)
        for i, r in enumerate(resultats_succes, 1):
            print(f"{i}. {r['modele']}")
            print(f"   ⏱️  Temps: {r['duree']:.1f}s")
            print(f"   📝 Réponse: {r['longueur_reponse']} caractères")
            print(f"   ⚡ Vitesse: {r['vitesse']:.0f} car/s")
            print()
        
        # Meilleur modèle
        meilleur = resultats_succes[0]
        print(f"🥇 MEILLEUR MODÈLE: {meilleur['modele']}")
        print(f"   Temps de réponse: {meilleur['duree']:.1f}s")
        print(f"   Qualité estimée: {'Excellente' if meilleur['longueur_reponse'] > 500 else 'Bonne'}")
        
        # Aperçu de la meilleure réponse
        print(f"\n📋 APERÇU DE LA RÉPONSE ({meilleur['modele']}):")
        print("-" * 50)
        apercu = meilleur['reponse'][:300] + "..." if len(meilleur['reponse']) > 300 else meilleur['reponse']
        print(apercu)

    def sauvegarder_resultats(self, resultats: List[Dict], filename: str = "test_modeles_resultats.json"):
        """Sauvegarde les résultats"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(resultats, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Résultats sauvés dans {filename}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")

def main():
    """Fonction principale"""
    print("🧪 TEST COMPARATIF MODÈLES LOCAUX")
    print("Analyse du code de détection poker")
    print("=" * 60)
    
    # Vérifier qu'Ollama fonctionne
    try:
        subprocess.run(['ollama', '--version'], capture_output=True, check=True)
    except:
        print("❌ Ollama n'est pas installé ou ne fonctionne pas")
        print("💡 Installez Ollama depuis ollama.ai")
        return
    
    tester = TestImagePoker()
    
    print("🎯 Ce test va analyser votre code de détection poker avec différents modèles")
    print("📊 Critères évalués: vitesse, qualité de réponse, pertinence")
    print()
    
    # Exécuter les tests
    resultats = tester.executer_tests()
    
    if resultats:
        # Générer le rapport
        tester.generer_rapport(resultats)
        
        # Sauvegarder
        tester.sauvegarder_resultats(resultats)
        
        print("\n💡 RECOMMANDATIONS:")
        print("-" * 30)
        print("✅ Utilisez le modèle le plus rapide pour le debug quotidien")
        print("🎯 Gardez Claude (moi) pour les analyses complexes")
        print("🔄 Testez régulièrement de nouveaux modèles")
    
    print("\n🎉 Test terminé !")

if __name__ == "__main__":
    main()
