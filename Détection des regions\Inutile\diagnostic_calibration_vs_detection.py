#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic avancé : Calibration OK mais détection échoue
Analyse pourquoi les régions bien calibrées ne détectent pas les mises jaunes
"""

import sys
import os
import json
import cv2
import numpy as np

def analyser_probleme_detection_vs_calibration():
    """Analyse le décalage entre calibration et détection"""
    print("🔍 DIAGNOSTIC AVANCÉ - CALIBRATION VS DÉTECTION")
    print("=" * 60)
    
    try:
        from detector import Detector
        
        # Créer le détecteur
        detector = Detector()
        print("✅ Détecteur créé")
        
        # Capture d'écran
        import mss
        with mss.mss() as sct:
            screenshot = sct.grab(sct.monitors[1])
            screenshot_np = np.array(screenshot)
            screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_BGRA2BGR)
        
        print(f"✅ Capture réussie: {screenshot_bgr.shape}")
        
        # Charger la configuration
        config_path = r"C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        all_regions = config.get('all_regions', {})
        
        print("\n🔍 ANALYSE DÉTAILLÉE PAR RÉGION")
        print("-" * 60)
        
        # Analyser chaque région mise_joueur
        for i in range(1, 8):
            region_name = f"mise_joueur{i}"
            
            if region_name in all_regions:
                print(f"\n🎯 {region_name.upper()}")
                print("-" * 30)
                
                region_data = all_regions[region_name]
                x, y, w, h = region_data['x'], region_data['y'], region_data['width'], region_data['height']
                
                # Extraire la région
                region_img = screenshot_bgr[y:y+h, x:x+w]
                
                print(f"📍 Position: ({x}, {y}) - Taille: {w}x{h}")
                
                # Analyser les couleurs pixel par pixel
                analyser_couleurs_detaillees(region_img, region_name)
                
                # Tester différents seuils de détection
                tester_seuils_couleurs(region_img, region_name)
                
                # Analyser la distribution des couleurs
                analyser_distribution_couleurs(region_img, region_name)
                
                # Sauvegarder pour inspection visuelle
                cv2.imwrite(f"debug_calibration_{region_name}.jpg", region_img)
                
                # Créer une version avec analyse HSV
                hsv_analysis = creer_analyse_hsv_visuelle(region_img)
                cv2.imwrite(f"debug_calibration_{region_name}_hsv.jpg", hsv_analysis)
                
                print(f"💾 Images sauvées: debug_calibration_{region_name}.jpg et debug_calibration_{region_name}_hsv.jpg")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyser_couleurs_detaillees(image, region_name):
    """Analyse détaillée des couleurs dans l'image"""
    print(f"🎨 ANALYSE COULEURS DÉTAILLÉE - {region_name}")
    
    # Convertir en différents espaces colorimétriques
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Analyser les statistiques de base
    h, w = image.shape[:2]
    total_pixels = h * w
    
    print(f"   📊 Pixels totaux: {total_pixels}")
    
    # Analyser les canaux HSV
    h_channel = hsv[:, :, 0]  # Teinte
    s_channel = hsv[:, :, 1]  # Saturation
    v_channel = hsv[:, :, 2]  # Luminosité
    
    h_mean = np.mean(h_channel)
    s_mean = np.mean(s_channel)
    v_mean = np.mean(v_channel)
    
    print(f"   🌈 HSV moyen: H={h_mean:.1f}, S={s_mean:.1f}, V={v_mean:.1f}")
    
    # Analyser les plages de couleurs spécifiques
    plages_test = {
        'Jaune strict': ([20, 100, 100], [30, 255, 255]),
        'Jaune large': ([15, 80, 80], [35, 255, 255]),
        'Orange strict': ([10, 100, 100], [20, 255, 255]),
        'Orange large': ([5, 80, 80], [25, 255, 255]),
        'Jaune/Orange combiné': ([5, 80, 80], [35, 255, 255])
    }
    
    for nom_plage, (lower, upper) in plages_test.items():
        lower_np = np.array(lower)
        upper_np = np.array(upper)
        mask = cv2.inRange(hsv, lower_np, upper_np)
        pixels_detectes = np.sum(mask > 0)
        pourcentage = (pixels_detectes / total_pixels) * 100
        
        print(f"   🔍 {nom_plage}: {pixels_detectes} pixels ({pourcentage:.2f}%)")
        
        if pourcentage > 1.0:
            print(f"      ✅ Couleur détectée avec {nom_plage}")

def tester_seuils_couleurs(image, region_name):
    """Teste différents seuils pour la détection de couleurs"""
    print(f"🎯 TEST SEUILS COULEURS - {region_name}")
    
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    total_pixels = image.shape[0] * image.shape[1]
    
    # Tester différents seuils pour orange/jaune
    seuils_test = [1.0, 2.0, 3.0, 5.0, 8.0, 10.0]
    
    # Plage jaune/orange combinée
    lower_jaune_orange = np.array([5, 80, 80])
    upper_jaune_orange = np.array([35, 255, 255])
    mask = cv2.inRange(hsv, lower_jaune_orange, upper_jaune_orange)
    pixels_jaune_orange = np.sum(mask > 0)
    pourcentage_jaune_orange = (pixels_jaune_orange / total_pixels) * 100
    
    print(f"   📊 Pourcentage jaune/orange réel: {pourcentage_jaune_orange:.2f}%")
    
    for seuil in seuils_test:
        if pourcentage_jaune_orange > seuil:
            print(f"   ✅ Détecté avec seuil {seuil}%")
        else:
            print(f"   ❌ Non détecté avec seuil {seuil}%")
    
    # Recommandation de seuil
    if pourcentage_jaune_orange > 0.5:
        seuil_recommande = max(0.5, pourcentage_jaune_orange * 0.8)
        print(f"   💡 Seuil recommandé: {seuil_recommande:.1f}%")
    else:
        print(f"   ⚠️ Très peu de jaune/orange détecté - Vérifiez la calibration")

def analyser_distribution_couleurs(image, region_name):
    """Analyse la distribution des couleurs dans l'image"""
    print(f"📈 DISTRIBUTION COULEURS - {region_name}")
    
    # Convertir en HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h_channel = hsv[:, :, 0]
    
    # Analyser la distribution des teintes
    teintes_uniques, counts = np.unique(h_channel, return_counts=True)
    total_pixels = np.sum(counts)
    
    # Identifier les teintes dominantes
    indices_tries = np.argsort(counts)[::-1]  # Tri décroissant
    top_5_teintes = indices_tries[:5]
    
    print(f"   🎨 Top 5 des teintes dominantes:")
    for i, idx in enumerate(top_5_teintes):
        teinte = teintes_uniques[idx]
        count = counts[idx]
        pourcentage = (count / total_pixels) * 100
        
        # Identifier la couleur approximative
        if 5 <= teinte <= 35:
            couleur_nom = "Jaune/Orange"
        elif 35 <= teinte <= 85:
            couleur_nom = "Vert"
        elif 85 <= teinte <= 140:
            couleur_nom = "Bleu"
        elif 140 <= teinte <= 170:
            couleur_nom = "Violet"
        elif teinte >= 170 or teinte <= 5:
            couleur_nom = "Rouge"
        else:
            couleur_nom = "Autre"
        
        print(f"      {i+1}. Teinte {teinte} ({couleur_nom}): {pourcentage:.1f}%")
        
        if couleur_nom == "Jaune/Orange" and pourcentage > 5:
            print(f"         ✅ Jaune/Orange significatif détecté !")

def creer_analyse_hsv_visuelle(image):
    """Crée une visualisation HSV de l'image"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Créer des masques pour différentes couleurs
    mask_jaune = cv2.inRange(hsv, np.array([20, 100, 100]), np.array([30, 255, 255]))
    mask_orange = cv2.inRange(hsv, np.array([10, 100, 100]), np.array([20, 255, 255]))
    mask_jaune_orange = cv2.inRange(hsv, np.array([5, 80, 80]), np.array([35, 255, 255]))
    
    # Créer une image composite
    h, w = image.shape[:2]
    composite = np.zeros((h, w * 4, 3), dtype=np.uint8)
    
    # Image originale
    composite[:, 0:w] = image
    
    # Masque jaune (en jaune)
    composite[:, w:2*w, 1] = mask_jaune  # Canal vert
    composite[:, w:2*w, 2] = mask_jaune  # Canal rouge
    
    # Masque orange (en orange)
    composite[:, 2*w:3*w, 2] = mask_orange  # Canal rouge
    composite[:, 2*w:3*w, 1] = mask_orange // 2  # Canal vert (moins intense)
    
    # Masque combiné (en blanc)
    composite[:, 3*w:4*w, 0] = mask_jaune_orange  # Tous les canaux
    composite[:, 3*w:4*w, 1] = mask_jaune_orange
    composite[:, 3*w:4*w, 2] = mask_jaune_orange
    
    return composite

def identifier_problemes_possibles():
    """Identifie les problèmes possibles"""
    print("\n🚨 PROBLÈMES POSSIBLES IDENTIFIÉS")
    print("=" * 60)
    
    problemes = [
        {
            "titre": "1. Seuils de détection trop élevés",
            "description": "Les seuils actuels (3-5%) sont peut-être trop stricts",
            "solution": "Réduire les seuils à 1-2% pour les mises jaunes"
        },
        {
            "titre": "2. Plages HSV inadaptées",
            "description": "Les plages de couleurs ne couvrent pas toutes les nuances de jaune",
            "solution": "Élargir les plages HSV pour inclure plus de nuances"
        },
        {
            "titre": "3. Éclairage/contraste",
            "description": "L'éclairage change la perception des couleurs jaunes",
            "solution": "Adapter la détection selon les conditions d'éclairage"
        },
        {
            "titre": "4. Résolution/qualité d'image",
            "description": "La qualité de l'image affecte la détection des couleurs",
            "solution": "Améliorer le prétraitement avant détection couleurs"
        },
        {
            "titre": "5. Mises temporairement absentes",
            "description": "Il n'y a peut-être pas de mises au moment de la capture",
            "solution": "Tester sur une table avec des mises visibles"
        }
    ]
    
    for probleme in problemes:
        print(f"\n{probleme['titre']}")
        print(f"   📝 {probleme['description']}")
        print(f"   🔧 {probleme['solution']}")

def recommandations_correction():
    """Recommandations pour corriger le problème"""
    print("\n🔧 RECOMMANDATIONS DE CORRECTION")
    print("=" * 60)
    
    print("1. **Examiner les images de debug**")
    print("   - Ouvrez debug_calibration_mise_joueur*.jpg")
    print("   - Vérifiez visuellement si vous voyez du jaune/orange")
    print("   - Observez debug_calibration_mise_joueur*_hsv.jpg pour l'analyse HSV")
    
    print("\n2. **Ajuster les seuils de détection**")
    print("   - Si les images montrent du jaune mais le système ne le détecte pas")
    print("   - Réduire les seuils de 3-5% à 1-2%")
    print("   - Élargir les plages HSV")
    
    print("\n3. **Tester en temps réel**")
    print("   - Placez des mises visibles sur la table")
    print("   - Relancez le diagnostic")
    print("   - Observez les pourcentages de couleurs détectées")
    
    print("\n4. **Vérifier l'état de la table**")
    print("   - Assurez-vous qu'il y a des mises jaunes/oranges visibles")
    print("   - Vérifiez que vous êtes sur une table de poker active")
    print("   - Testez à différents moments")

def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC AVANCÉ - CALIBRATION VS DÉTECTION")
    print("=" * 70)
    print("Analyse pourquoi les régions bien calibrées ne détectent pas les mises jaunes")
    print()
    
    # Effectuer l'analyse
    success = analyser_probleme_detection_vs_calibration()
    
    # Identifier les problèmes
    identifier_problemes_possibles()
    
    # Recommandations
    recommandations_correction()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DU DIAGNOSTIC AVANCÉ")
    print("-" * 70)
    
    if success:
        print("✅ Analyse détaillée terminée")
        print("📁 Images de debug créées avec analyse HSV")
        print("🔍 Problèmes possibles identifiés")
        
        print("\n🚀 ÉTAPES SUIVANTES :")
        print("1. Examinez les images debug_calibration_*.jpg")
        print("2. Vérifiez visuellement la présence de jaune/orange")
        print("3. Observez les analyses HSV (*_hsv.jpg)")
        print("4. Ajustez les seuils si nécessaire")
        print("5. Testez sur une table avec mises visibles")
    else:
        print("❌ Erreur lors de l'analyse")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
