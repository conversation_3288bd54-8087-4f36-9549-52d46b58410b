#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur sécurisé pour l'application detector_gui avec la logique avancée.
Ce script lance l'application avec une surveillance renforcée.
"""

import sys
import os
import subprocess
import time
from datetime import datetime

def check_prerequisites():
    """Vérifie que tous les prérequis sont présents"""
    print("🔍 VÉRIFICATION DES PRÉREQUIS")
    print("=" * 40)
    
    # Vérifier les fichiers essentiels
    essential_files = [
        "detector_gui.py",
        "poker_logic_advanced.py", 
        "poker_advisor_integration.py",
        "debug_crash.py"
    ]
    
    missing_files = []
    for file in essential_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} MANQUANT")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Fichiers manquants: {missing_files}")
        return False
    
    # Vérifier les modules Python
    required_modules = [
        "PyQt5",
        "cv2", 
        "numpy",
        "mss"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ Module {module}")
        except ImportError:
            print(f"   ❌ Module {module} MANQUANT")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Modules manquants: {missing_modules}")
        return False
    
    print("\n✅ Tous les prérequis sont présents")
    return True

def test_advanced_logic():
    """Test rapide de la logique avancée"""
    print("\n🧪 TEST RAPIDE DE LA LOGIQUE AVANCÉE")
    print("=" * 40)
    
    try:
        from poker_advisor_integration import poker_integration
        
        # Test simple
        result = poker_integration.evaluate_hand_advanced(
            ["As", "Roi"], ["Cœur", "Pique"], [], []
        )
        
        print(f"   ✅ Test réussi: {result['hand_description']}, {result['equity']:.1f}%")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur test logique: {e}")
        return False

def launch_application():
    """Lance l'application avec surveillance"""
    print("\n🚀 LANCEMENT DE L'APPLICATION")
    print("=" * 40)
    
    try:
        # Créer un log de lancement
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open("launch_log.txt", "a", encoding="utf-8") as f:
            f.write(f"[{timestamp}] Lancement de detector_gui avec logique avancée\n")
        
        print("   📝 Log de lancement créé")
        print("   🎯 Lancement de detector_gui.py...")
        print("   ⚠️  Si l'application se ferme, vérifiez debug_crash.log")
        print("   🔄 Appuyez sur Ctrl+C pour arrêter proprement")
        
        # Lancer l'application
        process = subprocess.Popen([
            sys.executable, "detector_gui.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"   ✅ Application lancée (PID: {process.pid})")
        
        # Attendre un peu pour voir si l'application démarre correctement
        time.sleep(3)
        
        # Vérifier si le processus est toujours en cours
        if process.poll() is None:
            print("   ✅ Application en cours d'exécution")
            print("\n📋 INSTRUCTIONS:")
            print("   1. L'application devrait s'ouvrir dans une nouvelle fenêtre")
            print("   2. Testez la détection avec la logique avancée")
            print("   3. En cas de problème, consultez debug_crash.log")
            print("   4. Fermez cette fenêtre quand vous avez terminé")
            
            # Attendre que l'utilisateur ferme l'application
            try:
                process.wait()
                print("\n✅ Application fermée proprement")
            except KeyboardInterrupt:
                print("\n⚠️ Interruption par l'utilisateur")
                process.terminate()
                process.wait()
                print("✅ Application arrêtée")
        else:
            # L'application s'est fermée rapidement
            stdout, stderr = process.communicate()
            print(f"\n❌ L'application s'est fermée rapidement (code: {process.returncode})")
            
            if stdout:
                print(f"📤 Sortie standard:\n{stdout}")
            if stderr:
                print(f"📤 Erreurs:\n{stderr}")
            
            # Vérifier les logs de crash
            if os.path.exists("debug_crash.log"):
                print("\n📋 Dernières entrées du log de crash:")
                with open("debug_crash.log", "r", encoding="utf-8") as f:
                    lines = f.readlines()
                    for line in lines[-10:]:  # 10 dernières lignes
                        print(f"   {line.strip()}")
            
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur lors du lancement: {e}")
        return False

def show_troubleshooting():
    """Affiche les conseils de dépannage"""
    print("\n🔧 CONSEILS DE DÉPANNAGE")
    print("=" * 40)
    print("Si l'application se ferme:")
    print("   1. Vérifiez debug_crash.log pour les erreurs")
    print("   2. Lancez 'python test_version_securisee.py' pour tester la logique")
    print("   3. Essayez de désactiver temporairement la logique avancée")
    print("   4. Vérifiez que toutes les dépendances sont installées")
    print("\nFichiers de diagnostic:")
    print("   - debug_crash.log : Logs de crash et erreurs")
    print("   - launch_log.txt : Historique des lancements")
    print("   - monitor.log : Surveillance système")
    print("\nCommandes utiles:")
    print("   - python diagnostic_crash_logique.py : Diagnostic complet")
    print("   - python test_integration.py : Test d'intégration")
    print("   - powershell show_logs.ps1 : Afficher les logs")

def main():
    """Fonction principale"""
    print("🛡️ LANCEUR SÉCURISÉ DETECTOR GUI AVEC LOGIQUE AVANCÉE")
    print("=" * 70)
    
    # Étape 1: Vérifier les prérequis
    if not check_prerequisites():
        print("\n❌ PRÉREQUIS MANQUANTS - ARRÊT")
        show_troubleshooting()
        return
    
    # Étape 2: Tester la logique avancée
    if not test_advanced_logic():
        print("\n⚠️ PROBLÈME AVEC LA LOGIQUE AVANCÉE")
        print("L'application sera lancée avec l'ancienne logique")
        input("Appuyez sur Entrée pour continuer...")
    
    # Étape 3: Lancer l'application
    success = launch_application()
    
    if success:
        print("\n🎉 SESSION TERMINÉE AVEC SUCCÈS")
    else:
        print("\n❌ PROBLÈME LORS DU LANCEMENT")
        show_troubleshooting()
    
    print("\n👋 Merci d'avoir utilisé le lanceur sécurisé!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Interruption par l'utilisateur")
        print("👋 Au revoir!")
    except Exception as e:
        print(f"\n❌ Erreur critique dans le lanceur: {e}")
        import traceback
        traceback.print_exc()
