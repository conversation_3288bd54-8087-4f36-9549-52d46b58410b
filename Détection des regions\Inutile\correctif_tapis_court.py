#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correctif pour les recommandations en tapis court
Évite les all-in systématiques et propose des actions plus nuancées
"""

import sys
import os

def analyser_probleme_tapis_court():
    """Analyse le problème des recommandations all-in en tapis court"""
    print("🎯 ANALYSE DU PROBLÈME - TAPIS COURT")
    print("=" * 50)
    
    print("🚨 PROBLÈME IDENTIFIÉ :")
    print("   Le conseiller recommande systématiquement ALL-IN en tapis court")
    print("   même pour des situations où d'autres actions seraient meilleures")
    print()
    
    print("🔍 CAUSES IDENTIFIÉES :")
    print("   1. Seuils trop bas pour 'tapis très court' (< 15 BB)")
    print("   2. Logique push/fold trop agressive")
    print("   3. Pas assez de nuances entre les actions")
    print("   4. Ne prend pas en compte la position et les adversaires")
    print()
    
    print("🎯 SOLUTIONS À APPLIQUER :")
    print("   1. Ajuster les seuils de tapis court")
    print("   2. Ajouter plus de nuances dans les recommandations")
    print("   3. Prendre en compte la position")
    print("   4. Considérer le nombre d'adversaires")
    print("   5. Adapter selon la force de la main")

def corriger_logique_tapis_court():
    """Corrige la logique de recommandations pour tapis court"""
    print("\n🔧 CORRECTION DE LA LOGIQUE TAPIS COURT")
    print("=" * 50)
    
    # Lire le fichier poker_advisor_light.py
    try:
        with open("poker_advisor_light.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("✅ Fichier poker_advisor_light.py lu")
        
        # Identifier les sections problématiques
        problemes = []
        
        if "if stack_category in [\"très_court\", \"court\"]:" in content:
            problemes.append("Logique trop agressive pour tapis court")
        
        if "if average_stack < 15:" in content:
            problemes.append("Seuil trop bas pour all-in (15 BB)")
        
        if "return (\"all-in\"" in content:
            all_in_count = content.count("return (\"all-in\"")
            problemes.append(f"Trop de recommandations all-in ({all_in_count} occurrences)")
        
        print(f"🚨 {len(problemes)} problèmes identifiés :")
        for i, probleme in enumerate(problemes, 1):
            print(f"   {i}. {probleme}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def proposer_nouvelle_logique():
    """Propose une nouvelle logique plus nuancée"""
    print("\n💡 NOUVELLE LOGIQUE PROPOSÉE")
    print("=" * 50)
    
    nouvelle_logique = """
    🎯 NOUVELLE LOGIQUE TAPIS COURT :
    
    1. **Seuils révisés :**
       - Très court : < 10 BB (au lieu de 15 BB)
       - Court : 10-20 BB
       - Moyen : 20-50 BB
       - Profond : > 50 BB
    
    2. **Actions selon la taille du stack :**
       
       **< 8 BB (Très très court) :**
       - Main premium (AA, KK, QQ, AK) : ALL-IN
       - Main forte (JJ, AQ, AJs) : ALL-IN si position tardive, sinon CALL
       - Main moyenne : FOLD sauf en position tardive
       
       **8-12 BB (Très court) :**
       - Main premium : RAISE 3-4 BB ou ALL-IN si relancé
       - Main forte : RAISE 2.5-3 BB
       - Main moyenne : CALL ou FOLD selon position
       
       **12-20 BB (Court) :**
       - Main premium : RAISE 2.5-3 BB
       - Main forte : RAISE 2-2.5 BB
       - Main moyenne : CALL, RAISE 2 BB en position
       
    3. **Facteurs additionnels :**
       - Position (plus agressif en position tardive)
       - Nombre d'adversaires (plus serré avec plus d'adversaires)
       - Style des adversaires (plus agressif contre passifs)
       - Niveau de blinds (plus agressif en fin de tournoi)
    """
    
    print(nouvelle_logique)

def creer_correctif():
    """Crée le correctif pour poker_advisor_light.py"""
    print("\n🛠️ CRÉATION DU CORRECTIF")
    print("=" * 50)
    
    correctif = '''
def recommend_action_tapis_court_ameliore(self, equity, pot_odds, stack_to_pot_ratio, 
                                         my_stack, pot, bet_to_call, position="middle", 
                                         nb_adversaires=5):
    """
    Recommandations améliorées pour tapis court
    Évite les all-in systématiques
    """
    min_equity, max_equity = equity
    avg_equity = (min_equity + max_equity) / 2
    
    # Catégoriser le stack plus finement
    if my_stack < 8:
        stack_category = "très_très_court"
    elif my_stack < 12:
        stack_category = "très_court"
    elif my_stack < 20:
        stack_category = "court"
    elif my_stack < 50:
        stack_category = "moyen"
    else:
        stack_category = "profond"
    
    # Ajustement selon la position
    position_factor = 1.0
    if position in ["button", "cutoff"]:
        position_factor = 1.2  # Plus agressif en position tardive
    elif position in ["early", "utg"]:
        position_factor = 0.8  # Plus conservateur en position précoce
    
    # Ajustement selon le nombre d'adversaires
    opponent_factor = 1.0
    if nb_adversaires <= 2:
        opponent_factor = 1.3  # Plus agressif en heads-up
    elif nb_adversaires >= 6:
        opponent_factor = 0.7  # Plus conservateur en table pleine
    
    # Équité ajustée
    adjusted_equity = avg_equity * position_factor * opponent_factor
    
    # Logique spécifique par catégorie de stack
    if stack_category == "très_très_court":  # < 8 BB
        if adjusted_equity >= 70:  # Main premium
            return ("all-in", f"Main premium avec {my_stack:.1f} BB - Maximiser la valeur")
        elif adjusted_equity >= 55:  # Main forte
            if position in ["button", "cutoff"] or nb_adversaires <= 3:
                return ("all-in", f"Main forte en bonne position avec {my_stack:.1f} BB")
            else:
                if bet_to_call <= my_stack * 0.3:
                    return ("call", f"Main forte mais position défavorable - Call avec {my_stack:.1f} BB")
                else:
                    return ("fold", f"Main forte mais mise trop élevée pour {my_stack:.1f} BB")
        else:  # Main moyenne/faible
            if bet_to_call == 0 and position in ["button", "small_blind"]:
                return ("all-in", f"Vol de blinds avec {my_stack:.1f} BB")
            else:
                return ("fold", f"Main trop faible pour {my_stack:.1f} BB")
    
    elif stack_category == "très_court":  # 8-12 BB
        if adjusted_equity >= 70:  # Main premium
            if bet_to_call == 0:
                raise_amount = min(3.5, my_stack * 0.4)
                return (f"raise {raise_amount:.1f} BB", f"Main premium - Relance avec {my_stack:.1f} BB")
            elif bet_to_call <= my_stack * 0.25:
                return ("call", f"Main premium - Call avec {my_stack:.1f} BB")
            else:
                return ("all-in", f"Main premium - All-in face à grosse mise avec {my_stack:.1f} BB")
        elif adjusted_equity >= 55:  # Main forte
            if bet_to_call == 0:
                raise_amount = min(2.5, my_stack * 0.3)
                return (f"raise {raise_amount:.1f} BB", f"Main forte - Relance modérée avec {my_stack:.1f} BB")
            elif bet_to_call <= my_stack * 0.2:
                return ("call", f"Main forte - Call avec {my_stack:.1f} BB")
            else:
                return ("fold", f"Main forte mais mise trop élevée pour {my_stack:.1f} BB")
        else:  # Main moyenne
            if bet_to_call == 0 and position in ["button", "cutoff"]:
                raise_amount = min(2.0, my_stack * 0.25)
                return (f"raise {raise_amount:.1f} BB", f"Vol en position avec {my_stack:.1f} BB")
            elif bet_to_call <= my_stack * 0.15:
                return ("call", f"Main moyenne - Call avec {my_stack:.1f} BB")
            else:
                return ("fold", f"Main moyenne - Fold avec {my_stack:.1f} BB")
    
    elif stack_category == "court":  # 12-20 BB
        if adjusted_equity >= 65:  # Main premium
            if bet_to_call == 0:
                raise_amount = min(3.0, my_stack * 0.2)
                return (f"raise {raise_amount:.1f} BB", f"Main premium - Relance standard avec {my_stack:.1f} BB")
            elif bet_to_call <= my_stack * 0.2:
                return ("call", f"Main premium - Call avec {my_stack:.1f} BB")
            else:
                return ("all-in", f"Main premium - All-in face à grosse mise avec {my_stack:.1f} BB")
        elif adjusted_equity >= 50:  # Main forte
            if bet_to_call == 0:
                raise_amount = min(2.5, my_stack * 0.15)
                return (f"raise {raise_amount:.1f} BB", f"Main forte - Relance avec {my_stack:.1f} BB")
            elif bet_to_call <= my_stack * 0.15:
                return ("call", f"Main forte - Call avec {my_stack:.1f} BB")
            else:
                return ("fold", f"Main forte mais mise trop élevée pour {my_stack:.1f} BB")
        else:  # Main moyenne
            if bet_to_call == 0 and position in ["button", "cutoff", "hijack"]:
                raise_amount = min(2.0, my_stack * 0.12)
                return (f"raise {raise_amount:.1f} BB", f"Vol en position avec {my_stack:.1f} BB")
            elif bet_to_call <= my_stack * 0.1:
                return ("call", f"Main moyenne - Call avec {my_stack:.1f} BB")
            else:
                return ("fold", f"Main moyenne - Fold avec {my_stack:.1f} BB")
    
    else:  # Stack moyen ou profond
        # Utiliser la logique normale (non modifiée)
        return self.recommend_action_original(equity, pot_odds, stack_to_pot_ratio, 
                                            my_stack, pot, bet_to_call)
'''
    
    print("📝 Correctif créé :")
    print("   - Seuils de stack révisés")
    print("   - Prise en compte de la position")
    print("   - Adaptation au nombre d'adversaires")
    print("   - Actions plus nuancées")
    print("   - Moins d'all-in systématiques")
    
    return correctif

def tester_nouvelle_logique():
    """Teste la nouvelle logique avec des exemples"""
    print("\n🧪 TEST DE LA NOUVELLE LOGIQUE")
    print("=" * 50)
    
    scenarios = [
        {
            "nom": "Tapis très court - Main premium",
            "stack": 7,
            "equity": (75, 85),
            "position": "button",
            "bet_to_call": 0,
            "attendu": "all-in ou raise"
        },
        {
            "nom": "Tapis très court - Main forte",
            "stack": 7,
            "equity": (60, 70),
            "position": "early",
            "bet_to_call": 2,
            "attendu": "call ou fold"
        },
        {
            "nom": "Tapis court - Main premium",
            "stack": 15,
            "equity": (70, 80),
            "position": "middle",
            "bet_to_call": 0,
            "attendu": "raise 2.5-3 BB"
        },
        {
            "nom": "Tapis court - Main moyenne",
            "stack": 15,
            "equity": (45, 55),
            "position": "button",
            "bet_to_call": 1,
            "attendu": "call ou raise léger"
        }
    ]
    
    print("📊 SCÉNARIOS DE TEST :")
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['nom']}")
        print(f"   Stack: {scenario['stack']} BB")
        print(f"   Équité: {scenario['equity'][0]}-{scenario['equity'][1]}%")
        print(f"   Position: {scenario['position']}")
        print(f"   À suivre: {scenario['bet_to_call']} BB")
        print(f"   ✅ Attendu: {scenario['attendu']}")

def main():
    """Fonction principale"""
    print("🎯 CORRECTIF TAPIS COURT - ÉVITER ALL-IN SYSTÉMATIQUES")
    print("=" * 60)
    print("Analyse et correction du problème d'all-in en tapis court")
    print()
    
    # Analyser le problème
    analyser_probleme_tapis_court()
    
    # Corriger la logique
    corriger_logique_tapis_court()
    
    # Proposer nouvelle logique
    proposer_nouvelle_logique()
    
    # Créer le correctif
    correctif = creer_correctif()
    
    # Tester la nouvelle logique
    tester_nouvelle_logique()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DU CORRECTIF")
    print("-" * 60)
    print("✅ Problème identifié : All-in systématiques en tapis court")
    print("✅ Solution proposée : Logique plus nuancée")
    print("✅ Améliorations :")
    print("   • Seuils de stack révisés")
    print("   • Prise en compte de la position")
    print("   • Adaptation aux adversaires")
    print("   • Actions plus variées")
    print("   • Moins d'all-in automatiques")
    
    print("\n🚀 PROCHAINES ÉTAPES :")
    print("1. Appliquer le correctif à poker_advisor_light.py")
    print("2. Tester avec différents scénarios")
    print("3. Ajuster les seuils si nécessaire")
    print("4. Valider en situation réelle")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
