@echo off
echo 🎯 CONSEILLER POKER TEMPS RÉEL
echo ================================
echo Détection automatique des joueurs et conseils en temps réel
echo ================================
echo.

cd /d "C:\Users\<USER>\PokerAdvisor\Détection des regions"

echo 🔍 Vérification des fichiers...
if not exist "auto_player_detection.py" (
    echo ❌ Fichier auto_player_detection.py introuvable
    pause
    exit /b 1
)

if not exist "realtime_advisor_gui.py" (
    echo ❌ Fichier realtime_advisor_gui.py introuvable
    pause
    exit /b 1
)

if not exist "lancer_conseiller_temps_reel.py" (
    echo ❌ Fichier lancer_conseiller_temps_reel.py introuvable
    pause
    exit /b 1
)

echo ✅ Fichiers trouvés
echo.

echo 🚀 Lancement de l'interface graphique...
python "lancer_conseiller_temps_reel.py" --mode gui

echo.
echo 📝 Interface fermée
pause
