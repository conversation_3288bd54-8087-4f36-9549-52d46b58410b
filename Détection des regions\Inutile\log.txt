✅ Module poker_advisor_light importé avec succès
✅ Module mss importé avec succès
🔍 Recherche du fichier de configuration à: C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json
✅ Fichier de configuration de calibration trouvé: C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json
✅ Contenu du fichier de configuration chargé avec succès
✅ Nombre de régions dans 'roi': 37
✅ Nombre de régions dans 'all_regions': 37
✅ Coordonnées de card_1: {'left': 794, 'top': 581, 'width': 118, 'height': 89}
✅ Coordonnées de card_2: {'left': 937, 'top': 579, 'width': 132, 'height': 95}
✅ Coordonnées de card_3: {'left': 1097, 'top': 579, 'width': 129, 'height': 95}
✅ Coordonnées de card_4: {'left': 1251, 'top': 579, 'width': 132, 'height': 95}
✅ Coordonnées de card_5: {'left': 1405, 'top': 579, 'width': 134, 'height': 99}
🔧 Initialisation du détecteur avec le fichier: C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json
✅ Configuration chargée depuis C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json
✅ CUDA détecté: NVIDIA GeForce RTX 3060 Ti
✅ Mémoire CUDA disponible: 8.00 Go
✅ Version CUDA: 11.8
✅ Optimisations CUDA activées pour PyTorch
⚠️ PaddlePaddle non compilé avec CUDA, utilisation du CPU
✅ PaddleOCR initialisé avec succès (CPU optimisé)
✅ Détecteur initialisé avec la configuration: C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json

✅ Détecteur initialisé avec la configuration de calibration
✅ Le détecteur a chargé 37 régions
🔍 État de SCREEN_CAPTURE_AVAILABLE: True
✅ Boutons de capture d'écran forcés à l'état activé
✅ Configuration chargée depuis le détecteur: C:\Users\<USER>\PokerAdvisor\Calibration\config\poker_advisor_config.json
