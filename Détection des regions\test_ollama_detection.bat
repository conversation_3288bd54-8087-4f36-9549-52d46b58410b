@echo off
echo ===================================================
echo TEST OLLAMA + LLAVA - DETECTION MONTANTS & PSEUDOS
echo ===================================================
echo.

echo Verification de Python...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo Python n'est pas installe ou pas dans le PATH.
    pause
    exit /b 1
)

echo.
echo Test de connexion Ollama...
python test_ollama_simple.py
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ===================================================
    echo PROBLEME DETECTE - GUIDE D'INSTALLATION:
    echo ===================================================
    echo.
    echo 1. Installer Ollama depuis: https://ollama.ai/
    echo 2. Ouvrir un terminal et taper: ollama serve
    echo 3. Dans un autre terminal: ollama pull llava
    echo 4. Relancer ce script
    echo.
    pause
    exit /b 1
)

echo.
echo ===================================================
echo LANCEMENT DU TEST COMPLET...
echo ===================================================
echo.

python ollama_vision_detector.py

echo.
echo ===================================================
echo TEST TERMINE
echo ===================================================
echo.
echo Fichiers generes:
if exist debug_ollama_*.jpg (
    echo   - Images de debug: debug_ollama_*.jpg
) else (
    echo   - Aucune image de debug generee
)

echo.
pause
