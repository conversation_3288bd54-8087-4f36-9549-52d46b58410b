#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de correction rapide pour résoudre le problème de crash lors de la détection.
"""

import os
import json
import shutil
from datetime import datetime

def create_backup():
    """Créer une sauvegarde avant modifications"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_fix_{timestamp}"
    
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    # Sauvegarder les fichiers importants
    files_to_backup = [
        "detector_gui.py",
        "detector.py",
        "multi_ocr_detector.py"
    ]
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, backup_dir)
            print(f"✅ Sauvegardé: {file}")
    
    print(f"📁 Sauvegarde créée dans: {backup_dir}")
    return backup_dir

def check_calibration_config():
    """Vérifier et créer le fichier de calibration si nécessaire"""
    config_file = "calibration_config.json"
    
    if os.path.exists(config_file):
        print(f"✅ Fichier de calibration trouvé: {config_file}")
        return True
    
    print(f"⚠️ Fichier de calibration manquant: {config_file}")
    print("🔧 Création d'un fichier de calibration par défaut...")
    
    # Configuration par défaut
    default_config = {
        "regions": {
            "hand_card_1": {"x": 100, "y": 100, "width": 80, "height": 120, "enabled": True},
            "hand_card_2": {"x": 200, "y": 100, "width": 80, "height": 120, "enabled": True},
            "card_1": {"x": 300, "y": 200, "width": 80, "height": 120, "enabled": True},
            "card_2": {"x": 400, "y": 200, "width": 80, "height": 120, "enabled": True},
            "card_3": {"x": 500, "y": 200, "width": 80, "height": 120, "enabled": True},
            "card_4": {"x": 600, "y": 200, "width": 80, "height": 120, "enabled": False},
            "card_5": {"x": 700, "y": 200, "width": 80, "height": 120, "enabled": False},
            "player_chips": {"x": 150, "y": 400, "width": 120, "height": 40, "enabled": True},
            "player_bet": {"x": 150, "y": 350, "width": 120, "height": 40, "enabled": True},
            "pot_amount": {"x": 400, "y": 150, "width": 120, "height": 40, "enabled": True}
        },
        "detection_settings": {
            "white_threshold": 200,
            "confidence_threshold": 0.7,
            "zoom_factor": 2.2,
            "use_cuda": True,
            "ocr_method": "multi",
            "preprocessing": {
                "gaussian_blur": True,
                "morphology": True,
                "contrast_enhancement": True
            }
        },
        "display_settings": {
            "show_regions": True,
            "show_confidence": True,
            "auto_save_screenshots": True,
            "screenshot_interval": 1.0
        },
        "advisor_settings": {
            "enable_advisor": True,
            "use_advanced_logic": False,
            "update_interval": 2.0,
            "show_equity": True,
            "show_outs": True
        }
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        print(f"✅ Fichier de calibration créé: {config_file}")
        return True
    except Exception as e:
        print(f"❌ Erreur création fichier calibration: {e}")
        return False

def optimize_memory_usage():
    """Optimiser l'utilisation mémoire dans detector.py"""
    detector_file = "detector.py"
    
    if not os.path.exists(detector_file):
        print(f"⚠️ Fichier {detector_file} non trouvé")
        return False
    
    print(f"🔧 Optimisation mémoire dans {detector_file}...")
    
    # Lire le fichier
    with open(detector_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter une fonction de nettoyage mémoire si elle n'existe pas
    cleanup_function = '''
    def cleanup_memory(self):
        """Nettoie la mémoire après détection"""
        try:
            import gc
            gc.collect()
            
            # Nettoyer les variables temporaires
            if hasattr(self, '_temp_images'):
                del self._temp_images
            if hasattr(self, '_temp_results'):
                del self._temp_results
                
            # Forcer le garbage collection
            gc.collect()
        except Exception as e:
            print(f"⚠️ Erreur nettoyage mémoire: {e}")
'''
    
    # Vérifier si la fonction existe déjà
    if "def cleanup_memory" not in content:
        # Ajouter la fonction avant la dernière ligne de la classe
        lines = content.split('\n')
        
        # Trouver la fin de la classe Detector
        insert_index = -1
        for i, line in enumerate(lines):
            if line.strip().startswith("class Detector"):
                class_start = i
            elif line.strip() and not line.startswith(' ') and not line.startswith('\t') and i > class_start:
                insert_index = i
                break
        
        if insert_index > 0:
            lines.insert(insert_index, cleanup_function)
            content = '\n'.join(lines)
            
            # Sauvegarder le fichier modifié
            with open(detector_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ Fonction de nettoyage mémoire ajoutée")
        else:
            print("⚠️ Impossible de trouver l'emplacement pour ajouter la fonction")
    else:
        print("✅ Fonction de nettoyage mémoire déjà présente")
    
    return True

def reduce_ocr_memory():
    """Réduire l'utilisation mémoire des OCR"""
    multi_ocr_file = "multi_ocr_detector.py"
    
    if not os.path.exists(multi_ocr_file):
        print(f"⚠️ Fichier {multi_ocr_file} non trouvé")
        return False
    
    print(f"🔧 Optimisation mémoire OCR dans {multi_ocr_file}...")
    
    # Lire le fichier
    with open(multi_ocr_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter une fonction de nettoyage OCR
    if "def cleanup_ocr_memory" not in content:
        cleanup_ocr = '''
    def cleanup_ocr_memory(self):
        """Nettoie la mémoire des OCR après utilisation"""
        try:
            import gc
            import torch
            
            # Nettoyer la mémoire GPU si CUDA est utilisé
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            
            # Forcer le garbage collection
            gc.collect()
            
        except Exception as e:
            print(f"⚠️ Erreur nettoyage mémoire OCR: {e}")
'''
        
        # Ajouter la fonction
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "class MultiOCRDetector" in line:
                # Trouver la fin de __init__
                for j in range(i, len(lines)):
                    if lines[j].strip().startswith("def ") and "__init__" not in lines[j]:
                        lines.insert(j, cleanup_ocr)
                        break
                break
        
        content = '\n'.join(lines)
        
        # Sauvegarder
        with open(multi_ocr_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Fonction de nettoyage mémoire OCR ajoutée")
    else:
        print("✅ Fonction de nettoyage mémoire OCR déjà présente")
    
    return True

def create_safe_launcher():
    """Créer un lanceur sécurisé"""
    launcher_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur sécurisé pour detector_gui avec gestion d'erreurs renforcée.
"""

import sys
import os
import traceback
import psutil
from datetime import datetime

def monitor_memory():
    """Surveille l'utilisation mémoire"""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024  # MB

def safe_launch():
    """Lance l'application de manière sécurisée"""
    print("🛡️ LANCEUR SÉCURISÉ DETECTOR GUI")
    print("=" * 50)
    
    # Vérifications préliminaires
    print("🔍 Vérifications préliminaires...")
    
    # Vérifier le fichier de calibration
    if not os.path.exists("calibration_config.json"):
        print("❌ Fichier calibration_config.json manquant!")
        print("Veuillez exécuter fix_crash_detection.py d'abord")
        return False
    
    print("✅ Fichier de calibration trouvé")
    
    # Surveiller la mémoire
    mem_start = monitor_memory()
    print(f"💾 Mémoire initiale: {mem_start:.1f} MB")
    
    try:
        print("🚀 Lancement de detector_gui...")
        
        # Import et lancement
        from detector_gui import DetectorGUI
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        window = DetectorGUI()
        window.show()
        
        print("✅ Application lancée avec succès")
        print("📋 Conseils:")
        print("   - Testez d'abord avec quelques régions seulement")
        print("   - Surveillez l'utilisation mémoire")
        print("   - En cas de problème, fermez et relancez")
        
        # Lancer l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        traceback.print_exc()
        
        mem_end = monitor_memory()
        print(f"💾 Mémoire finale: {mem_end:.1f} MB")
        print(f"💾 Différence: {mem_end - mem_start:.1f} MB")
        
        return False

if __name__ == "__main__":
    safe_launch()
'''
    
    with open("lancer_detector_safe.py", 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ Lanceur sécurisé créé: lancer_detector_safe.py")

def main():
    """Fonction principale de correction"""
    print("🔧 CORRECTION DU PROBLÈME DE CRASH LORS DE LA DÉTECTION")
    print("=" * 70)
    
    # Étape 1: Créer une sauvegarde
    backup_dir = create_backup()
    
    # Étape 2: Vérifier/créer le fichier de calibration
    if not check_calibration_config():
        print("❌ Impossible de créer le fichier de calibration")
        return False
    
    # Étape 3: Optimiser l'utilisation mémoire
    optimize_memory_usage()
    reduce_ocr_memory()
    
    # Étape 4: Créer un lanceur sécurisé
    create_safe_launcher()
    
    print("\n🎉 CORRECTIONS APPLIQUÉES AVEC SUCCÈS!")
    print("=" * 50)
    print("✅ Fichier de calibration créé")
    print("✅ Optimisations mémoire ajoutées")
    print("✅ Lanceur sécurisé créé")
    print(f"✅ Sauvegarde dans: {backup_dir}")
    
    print("\n📋 PROCHAINES ÉTAPES:")
    print("1. Testez avec: python lancer_detector_safe.py")
    print("2. Si ça fonctionne, calibrez vos régions")
    print("3. Activez progressivement les fonctionnalités")
    print("4. Surveillez l'utilisation mémoire")
    
    print("\n⚠️ EN CAS DE PROBLÈME:")
    print("- Vérifiez debug_crash.log")
    print("- Réduisez le nombre de régions actives")
    print("- Désactivez temporairement le conseiller poker")
    print("- Restaurez depuis la sauvegarde si nécessaire")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erreur critique: {e}")
        traceback.print_exc()
