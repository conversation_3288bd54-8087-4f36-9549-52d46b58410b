#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Générateur de données de test pour le conseiller poker
"""

import time
import os

def generer_situation_fold():
    """Génère une situation où il faut folder"""
    return """Cartes en main: 2♣ 7♦
Cartes sur le board: A♠ K♥ Q♦
Probabilité de gagner: 15.2%
Action recommandée: fold
Pot: 100
Pot total: 120
Tapis effectif: 150
Mes jetons: 150
Jetons joueur1: 200
Jetons joueur2: 180
Mise joueur1: 50
Mise joueur2: 25
"""

def generer_situation_call():
    """Génère une situation où il faut caller"""
    return """Cartes en main: 8♥ 8♠
Cartes sur le board: 2♦ 5♣ K♠
Probabilité de gagner: 45.8%
Action recommandée: call
Pot: 80
Pot total: 100
Tapis effectif: 200
Mes jetons: 180
Jetons joueur1: 150
Jetons joueur2: 220
Mise joueur1: 20
Mise joueur2: 0
"""

def generer_situation_raise():
    """Génère une situation où il faut relancer"""
    return """Cartes en main: A♠ A♥
Cartes sur le board: 2♦ 7♣ 9♠
Probabilité de gagner: 82.3%
Action recommandée: value-bet 3/4 pot
Pot: 60
Pot total: 80
Tapis effectif: 300
Mes jetons: 280
Jetons joueur1: 250
Jetons joueur2: 320
Mise joueur1: 15
Mise joueur2: 30
"""

def generer_situation_allin():
    """Génère une situation où il faut faire all-in"""
    return """Cartes en main: K♥ K♦
Cartes sur le board: K♠ 2♣ 7♦ 8♥
Probabilité de gagner: 95.1%
Action recommandée: all-in
Pot: 150
Pot total: 200
Tapis effectif: 80
Mes jetons: 80
Jetons joueur1: 120
Jetons joueur2: 90
Mise joueur1: 40
Mise joueur2: 80
"""

def generer_situation_check():
    """Génère une situation où il faut checker"""
    return """Cartes en main: J♣ 10♦
Cartes sur le board: 2♠ 5♥ 8♣
Probabilité de gagner: 35.7%
Action recommandée: check
Pot: 40
Pot total: 40
Tapis effectif: 200
Mes jetons: 200
Jetons joueur1: 180
Jetons joueur2: 220
Mise joueur1: 0
Mise joueur2: 0
"""

def ecrire_fichier_test(contenu, nom_fichier="realtime_results"):
    """Écrit le contenu dans le fichier de test"""
    export_dir = r"C:\Users\<USER>\PokerAdvisor\Détection des regions\export"
    
    # Créer le dossier s'il n'existe pas
    os.makedirs(export_dir, exist_ok=True)
    
    fichier_path = os.path.join(export_dir, nom_fichier)
    
    with open(fichier_path, 'w', encoding='utf-8') as f:
        f.write(contenu)
    
    # Créer le fichier de signal pour déclencher la mise à jour
    signal_path = os.path.join(export_dir, "update_signal.txt")
    with open(signal_path, 'w', encoding='utf-8') as f:
        f.write(str(time.time()))
    
    print(f"✅ Situation écrite dans {fichier_path}")

def test_toutes_situations():
    """Teste toutes les situations une par une"""
    situations = [
        ("FOLD - Main faible", generer_situation_fold()),
        ("CALL - Main moyenne", generer_situation_call()),
        ("RAISE - Main forte", generer_situation_raise()),
        ("ALL-IN - Main monstre", generer_situation_allin()),
        ("CHECK - Main marginale", generer_situation_check())
    ]
    
    print("🎯 Test de toutes les situations de poker")
    print("=" * 50)
    
    for i, (description, contenu) in enumerate(situations, 1):
        print(f"\n{i}. {description}")
        print("-" * 30)
        
        # Écrire la situation
        ecrire_fichier_test(contenu)
        
        # Attendre l'input de l'utilisateur
        input("Appuyez sur Entrée pour passer à la situation suivante...")
    
    print("\n✅ Test terminé ! Toutes les situations ont été testées.")

if __name__ == "__main__":
    print("🃏 Générateur de données de test pour le conseiller poker")
    print("=" * 60)
    print("1. Test automatique de toutes les situations")
    print("2. Générer une situation spécifique")
    
    choix = input("\nVotre choix (1 ou 2) : ").strip()
    
    if choix == "1":
        test_toutes_situations()
    elif choix == "2":
        print("\nSituations disponibles :")
        print("1. FOLD - Main faible")
        print("2. CALL - Main moyenne") 
        print("3. RAISE - Main forte")
        print("4. ALL-IN - Main monstre")
        print("5. CHECK - Main marginale")
        
        situation = input("\nChoisissez une situation (1-5) : ").strip()
        
        situations_map = {
            "1": ("FOLD", generer_situation_fold()),
            "2": ("CALL", generer_situation_call()),
            "3": ("RAISE", generer_situation_raise()),
            "4": ("ALL-IN", generer_situation_allin()),
            "5": ("CHECK", generer_situation_check())
        }
        
        if situation in situations_map:
            nom, contenu = situations_map[situation]
            print(f"\n🎯 Génération de la situation : {nom}")
            ecrire_fichier_test(contenu)
            print("✅ Situation générée ! Vérifiez le conseiller poker.")
        else:
            print("❌ Choix invalide.")
    else:
        print("❌ Choix invalide.")
