#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💾 SAUVEGARDE DE VOTRE CAPTURE POKER
Script pour sauvegarder votre capture d'écran pour les tests
"""

import os
import shutil
from datetime import datetime

def sauvegarder_capture():
    """Sauvegarde la capture d'écran pour les tests"""
    print("💾 SAUVEGARDE CAPTURE POKER")
    print("=" * 40)
    
    # Demander le chemin de l'image
    print("📁 Où se trouve votre capture d'écran poker ?")
    print("   (Glissez-déposez le fichier ou tapez le chemin)")
    
    chemin_source = input("Chemin de l'image: ").strip().strip('"')
    
    if not os.path.exists(chemin_source):
        print(f"❌ Fichier non trouvé: {chemin_source}")
        return False
    
    # Vérifier que c'est une image
    extensions_valides = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
    if not any(chemin_source.lower().endswith(ext) for ext in extensions_valides):
        print("❌ Format d'image non supporté")
        print(f"   Formats acceptés: {', '.join(extensions_valides)}")
        return False
    
    # Copier vers le dossier courant
    nom_destination = "capture_poker.png"
    
    try:
        shutil.copy2(chemin_source, nom_destination)
        print(f"✅ Capture sauvée: {nom_destination}")
        
        # Informations sur le fichier
        taille = os.path.getsize(nom_destination)
        print(f"📊 Taille: {taille / 1024:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la copie: {e}")
        return False

def main():
    """Fonction principale"""
    print("🎯 Ce script va préparer votre capture pour les tests d'IA")
    print()
    
    if sauvegarder_capture():
        print("\n🎉 Prêt pour les tests !")
        print("💡 Vous pouvez maintenant lancer:")
        print("   python test_image_poker.py")
    else:
        print("\n❌ Échec de la sauvegarde")

if __name__ == "__main__":
    main()
