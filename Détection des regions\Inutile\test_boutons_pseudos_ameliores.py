#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des améliorations de détection des boutons et pseudos
=========================================================

Ce script teste les nouvelles fonctionnalités ajoutées au détecteur :
1. Détection améliorée des boutons dealer avec logique orange affinée
2. Détection des pseudos des joueurs avec OCR spécialisé

Auteur: Augment Agent
Date: 2024
"""

import os
import sys
import cv2
import numpy as np
import json
from datetime import datetime

# Ajouter le répertoire parent au path pour importer le détecteur
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from detector import Detector
    print("✅ Module detector importé avec succès")
except ImportError as e:
    print(f"❌ Erreur lors de l'importation du detector: {e}")
    sys.exit(1)

def test_detection_boutons():
    """Test de la détection améliorée des boutons"""
    print("\n" + "="*60)
    print("🔘 TEST DE DÉTECTION DES BOUTONS AMÉLIORÉE")
    print("="*60)
    
    # Créer une image de test pour bouton avec orange
    test_image_orange = np.zeros((30, 30, 3), dtype=np.uint8)
    # Remplir avec de l'orange (HSV: 15, 200, 200)
    test_image_orange[:, :] = [0, 100, 255]  # Orange en BGR
    
    # Créer une image de test pour bouton dealer (blanc)
    test_image_dealer = np.zeros((30, 30, 3), dtype=np.uint8)
    # Remplir avec du blanc
    test_image_dealer[:, :] = [255, 255, 255]  # Blanc en BGR
    
    # Créer une image mixte (orange + blanc)
    test_image_mixte = np.zeros((30, 30, 3), dtype=np.uint8)
    test_image_mixte[:15, :] = [0, 100, 255]  # Orange en haut
    test_image_mixte[15:, :] = [255, 255, 255]  # Blanc en bas
    
    try:
        # Initialiser le détecteur
        detector = Detector()
        
        print("\n🧪 Test 1: Image avec beaucoup d'orange (pas de bouton)")
        colors_orange = detector.detect_colors_button(test_image_orange)
        print(f"   Couleurs détectées: {colors_orange}")
        
        print("\n🧪 Test 2: Image avec du blanc (bouton dealer)")
        colors_dealer = detector.detect_colors_button(test_image_dealer)
        print(f"   Couleurs détectées: {colors_dealer}")
        
        print("\n🧪 Test 3: Image mixte orange/blanc")
        colors_mixte = detector.detect_colors_button(test_image_mixte)
        print(f"   Couleurs détectées: {colors_mixte}")
        
        # Test avec une vraie capture si disponible
        config_path = os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json')
        if os.path.exists(config_path):
            print(f"\n🧪 Test 4: Configuration réelle trouvée: {config_path}")
            detector_real = Detector(config_path=config_path)
            
            # Prendre une capture d'écran de test
            try:
                import mss
                with mss.mss() as sct:
                    # Capturer une petite zone pour test
                    monitor = {"top": 100, "left": 100, "width": 200, "height": 200}
                    screenshot = sct.grab(monitor)
                    img_array = np.array(screenshot)
                    img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
                    
                    print("   Capture d'écran réalisée pour test")
                    colors_real = detector.detect_colors_button(img_bgr)
                    print(f"   Couleurs détectées sur capture réelle: {colors_real}")
                    
            except ImportError:
                print("   ⚠️ Module mss non disponible pour capture d'écran")
            except Exception as e:
                print(f"   ⚠️ Erreur lors de la capture: {e}")
        
        print("\n✅ Tests de détection des boutons terminés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des boutons: {e}")
        return False

def test_detection_pseudos():
    """Test de la détection des pseudos"""
    print("\n" + "="*60)
    print("👤 TEST DE DÉTECTION DES PSEUDOS")
    print("="*60)
    
    try:
        # Initialiser le détecteur
        detector = Detector()
        
        # Créer des images de test avec du texte simulé
        test_pseudos = [
            "Player123",
            "PokerPro",
            "Fish_Hunter",
            "ABC123",
            "TestUser"
        ]
        
        for i, pseudo in enumerate(test_pseudos):
            print(f"\n🧪 Test {i+1}: Simulation pour pseudo '{pseudo}'")
            
            # Créer une image simple avec du texte (simulation)
            test_image = np.ones((25, 120, 3), dtype=np.uint8) * 255  # Fond blanc
            
            # Ajouter du texte noir (simulation basique)
            cv2.putText(test_image, pseudo, (5, 18), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
            
            # Tester la validation du pseudo
            is_valid = detector._is_valid_player_name(pseudo)
            print(f"   Pseudo '{pseudo}' valide: {is_valid}")
            
            # Tester le prétraitement
            processed = detector._preprocess_for_player_name(test_image)
            print(f"   Prétraitement réussi: {processed.shape}")
            
            # Note: Le test OCR complet nécessiterait des images réelles
            # ou des images générées plus sophistiquées
        
        # Test de validation avec des cas limites
        print(f"\n🧪 Tests de validation des pseudos:")
        test_cases = [
            ("ValidUser", True),
            ("AB", False),  # Trop court
            ("ThisUsernameIsTooLongForValidation", False),  # Trop long
            ("User@123", False),  # Caractère invalide
            ("123", False),  # Que des chiffres
            ("FOLD", False),  # Mot réservé
            ("Player_123", True),  # Valide avec underscore
            ("Test.User", True),  # Valide avec point
            ("", False),  # Vide
        ]
        
        for pseudo, expected in test_cases:
            result = detector._is_valid_player_name(pseudo)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{pseudo}': {result} (attendu: {expected})")
        
        print("\n✅ Tests de détection des pseudos terminés")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des pseudos: {e}")
        return False

def test_integration_complete():
    """Test d'intégration complète avec configuration réelle"""
    print("\n" + "="*60)
    print("🔧 TEST D'INTÉGRATION COMPLÈTE")
    print("="*60)
    
    try:
        # Chercher la configuration
        config_paths = [
            os.path.join('..', 'Calibration', 'config', 'poker_advisor_config.json'),
            'calibration_config.json',
            os.path.join('config', 'poker_advisor_config.json')
        ]
        
        config_path = None
        for path in config_paths:
            if os.path.exists(path):
                config_path = path
                break
        
        if not config_path:
            print("⚠️ Aucune configuration trouvée, création d'une configuration de test")
            config_path = 'test_config.json'
            
            # Créer une configuration de test
            test_config = {
                "all_regions": {
                    "bouton_joueur1": {"x": 100, "y": 100, "width": 30, "height": 30},
                    "bouton_joueur2": {"x": 200, "y": 100, "width": 30, "height": 30},
                    "pseudo_joueur1": {"x": 100, "y": 150, "width": 120, "height": 25},
                    "pseudo_joueur2": {"x": 200, "y": 150, "width": 120, "height": 25},
                    "card_1": {"x": 300, "y": 200, "width": 80, "height": 120}
                }
            }
            
            with open(config_path, 'w') as f:
                json.dump(test_config, f, indent=2)
            print(f"✅ Configuration de test créée: {config_path}")
        
        # Initialiser le détecteur avec la configuration
        print(f"\n🔧 Initialisation du détecteur avec: {config_path}")
        detector = Detector(config_path=config_path)
        
        # Créer une image de test
        test_image = np.ones((400, 600, 3), dtype=np.uint8) * 128  # Fond gris
        
        # Ajouter des zones colorées pour simuler les régions
        # Zone bouton 1 (orange - pas de bouton)
        test_image[100:130, 100:130] = [0, 100, 255]  # Orange
        
        # Zone bouton 2 (blanc - bouton dealer)
        test_image[100:130, 200:230] = [255, 255, 255]  # Blanc
        
        # Zone pseudo 1 (fond blanc avec texte simulé)
        test_image[150:175, 100:220] = [255, 255, 255]  # Fond blanc
        cv2.putText(test_image, "TestPlayer", (105, 168), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)
        
        # Zone carte (rouge avec blanc)
        test_image[200:320, 300:380] = [255, 255, 255]  # Fond blanc
        test_image[210:310, 310:370] = [0, 0, 255]  # Rouge pour cœur
        cv2.putText(test_image, "A", (330, 270), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
        
        print("\n🔍 Traitement de l'image de test...")
        results = detector.process_image_direct(test_image, fast_mode=True)
        
        print(f"\n📊 Résultats de détection:")
        for region_name, result in results.items():
            text = result.get('text', '')
            colors = result.get('colors', [])
            
            if region_name.startswith('bouton_'):
                print(f"   🔘 {region_name}: Couleurs={colors}")
            elif region_name.startswith('pseudo_'):
                print(f"   👤 {region_name}: Pseudo='{text}'")
            elif region_name.startswith('card_'):
                print(f"   🃏 {region_name}: Texte='{text}', Couleurs={colors}")
            else:
                print(f"   📋 {region_name}: Texte='{text}', Couleurs={colors}")
        
        # Nettoyer le fichier de test si créé
        if config_path == 'test_config.json':
            os.remove(config_path)
            print(f"\n🧹 Configuration de test supprimée")
        
        print("\n✅ Test d'intégration complète terminé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'intégration: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 DÉMARRAGE DES TESTS D'AMÉLIORATION")
    print("="*60)
    print("Tests des nouvelles fonctionnalités:")
    print("1. Détection améliorée des boutons dealer")
    print("2. Détection des pseudos des joueurs")
    print("3. Intégration complète")
    
    # Exécuter tous les tests
    tests_results = []
    
    tests_results.append(("Détection boutons", test_detection_boutons()))
    tests_results.append(("Détection pseudos", test_detection_pseudos()))
    tests_results.append(("Intégration complète", test_integration_complete()))
    
    # Résumé des résultats
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*60)
    
    passed = 0
    total = len(tests_results)
    
    for test_name, result in tests_results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés avec succès!")
        print("\n💡 Les améliorations sont prêtes à être utilisées:")
        print("   - Détection des boutons avec logique orange affinée")
        print("   - Détection des pseudos avec OCR spécialisé")
        print("   - Intégration dans le processus de détection principal")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez les logs ci-dessus.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
