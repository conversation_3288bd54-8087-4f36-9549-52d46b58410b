#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Poker Advisor - Module de détection
===================================

Module de détection pour l'application Poker Advisor.
Utilise PaddleOCR pour la reconnaissance de texte et OpenCV pour la détection de couleurs.

Ce module permet de :
1. Charger une configuration contenant les coordonnées des régions à analyser
2. Extraire ces régions d'une image
3. Détecter le texte et les couleurs dans chaque région
4. Générer des résultats et des images de débogage

Auteur: Augment Agent
Date: 2023
"""

import os
import json
import argparse
import cv2
import numpy as np
from paddleocr import PaddleOCR

# Importer le détecteur multi-OCR (si disponible)
try:
    from multi_ocr_detector import MultiOCRDetector
    MULTI_OCR_AVAILABLE = True
    print("✅ Module multi_ocr_detector importé avec succès")
except ImportError:
    MULTI_OCR_AVAILABLE = False
    print("⚠️ Module multi_ocr_detector non disponible")



class Detector:
    """Classe pour la détection de cartes et de couleurs dans une image

    Cette classe permet de :
    - Charger une configuration depuis un fichier JSON
    - Extraire des régions d'intérêt d'une image
    - Détecter le texte dans ces régions avec PaddleOCR
    - Détecter les couleurs dominantes dans ces régions
    - Générer des résultats et des images de débogage
    """

    def __init__(self, config_path=None, selected_regions=None, use_cuda=None):
        """Initialise le détecteur avec un fichier de configuration

        Args:
            config_path (str, optional): Chemin vers le fichier de configuration JSON.
                Si None, utilise le fichier par défaut 'config/poker_advisor_config.json'.
            selected_regions (list, optional): Liste des noms des régions à analyser.
                Si None, toutes les régions définies dans la configuration sont analysées.
            use_cuda (bool, optional): Indique si CUDA doit être utilisé pour l'accélération GPU.
                Si None, la disponibilité de CUDA est détectée automatiquement.
        """
        # Chemin par défaut de la configuration
        if config_path is None:
            config_path = os.path.join('config', 'poker_advisor_config.json')
            print(f"✅ Utilisation de la configuration par défaut: {config_path}")

        # Stocker le chemin de la configuration
        self.config_path = config_path

        # Stocker les régions sélectionnées
        self.selected_regions = selected_regions

        # Charger la configuration
        self.config = self._load_config(config_path)

        # Vérifier si CUDA est disponible
        if use_cuda is None:
            try:
                import torch
                use_cuda = torch.cuda.is_available()
                if use_cuda:
                    try:
                        print(f"✅ CUDA détecté: {torch.cuda.get_device_name(0)}")
                        print(f"✅ Mémoire CUDA disponible: {torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024:.2f} Go")
                        print(f"✅ Version CUDA: {torch.version.cuda}")

                        # Optimisations CUDA pour PyTorch
                        torch.backends.cudnn.benchmark = True
                        torch.backends.cudnn.deterministic = False
                        print("✅ Optimisations CUDA activées pour PyTorch")
                    except Exception as e:
                        print(f"⚠️ Erreur lors de la récupération des informations CUDA: {e}")
                        print("⚠️ CUDA peut être disponible mais mal configuré")
                        use_cuda = False
                else:
                    print("⚠️ CUDA non disponible, utilisation du CPU")
            except ImportError:
                print("⚠️ PyTorch non installé, impossible de détecter CUDA")
                use_cuda = False
            except Exception as e:
                print(f"⚠️ Erreur lors de la détection de CUDA: {e}")
                use_cuda = False
        elif use_cuda:
            # Si CUDA est explicitement demandé, vérifier qu'il est disponible
            try:
                import torch
                if torch.cuda.is_available():
                    print(f"✅ CUDA activé manuellement: {torch.cuda.get_device_name(0)}")

                    # Optimisations CUDA pour PyTorch
                    torch.backends.cudnn.benchmark = True
                    torch.backends.cudnn.deterministic = False
                    print("✅ Optimisations CUDA activées pour PyTorch")
                else:
                    print("⚠️ CUDA demandé mais non disponible, utilisation du CPU")
                    use_cuda = False
            except ImportError:
                print("⚠️ PyTorch non installé, impossible d'utiliser CUDA")
                use_cuda = False
            except Exception as e:
                print(f"⚠️ Erreur lors de l'activation de CUDA: {e}")
                use_cuda = False

        # Initialiser PaddleOCR (mode hors ligne)
        try:
            # Vérifier si PaddlePaddle est compilé avec CUDA
            try:
                import paddle
                paddle_cuda = paddle.device.is_compiled_with_cuda()
                if paddle_cuda:
                    print(f"✅ PaddlePaddle compilé avec CUDA")
                    # Forcer l'utilisation de CUDA pour PaddlePaddle
                    if use_cuda:
                        paddle.device.set_device('gpu:0')
                        print(f"✅ PaddlePaddle configuré pour utiliser le GPU")
                else:
                    print(f"⚠️ PaddlePaddle non compilé avec CUDA, utilisation du CPU")
                    use_cuda = False
            except ImportError:
                print("⚠️ PaddlePaddle non importable, impossible de configurer CUDA")
            except Exception as e:
                print(f"⚠️ Erreur lors de la configuration de PaddlePaddle: {e}")

            # Essayer d'initialiser PaddleOCR avec les paramètres optimisés pour CUDA
            if use_cuda:
                self.ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='fr',
                    use_gpu=True,
                    show_log=False,
                    enable_mkldnn=False,  # Désactiver MKLDNN car nous utilisons CUDA
                    use_mp=False,         # Désactiver le multiprocessing pour éviter les conflits avec CUDA
                    use_tensorrt=False,   # TensorRT peut être activé si installé, mais peut causer des problèmes
                    gpu_mem=2000          # Limiter l'utilisation de la mémoire GPU à 2 Go
                )
                print(f"✅ PaddleOCR initialisé avec succès (GPU: True, optimisé pour CUDA)")
            else:
                # Initialiser avec CPU
                self.ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='fr',
                    use_gpu=False,
                    show_log=False,
                    enable_mkldnn=True,   # Activer MKLDNN pour accélérer le CPU
                    use_mp=True           # Activer le multiprocessing pour le CPU
                )
                print(f"✅ PaddleOCR initialisé avec succès (CPU optimisé)")
        except Exception as e:
            print(f"⚠️ Erreur lors de l'initialisation de PaddleOCR: {e}")
            print("⚠️ Tentative d'initialisation avec des paramètres minimaux...")
            try:
                # Essayer avec des paramètres minimaux
                self.ocr = PaddleOCR(
                    use_angle_cls=False,
                    lang='en',
                    use_gpu=False,
                    show_log=False,
                    use_mp=False,
                    enable_mkldnn=False
                )
                print("✅ PaddleOCR initialisé avec des paramètres minimaux (GPU: False)")
            except Exception as e2:
                print(f"❌ Échec de l'initialisation de PaddleOCR: {e2}")
                print("❌ La détection de texte ne sera pas disponible")

        # Définir les plages de couleurs HSV pour la détection
        # Plages ajustées pour une meilleure détection des couleurs des cartes
        self.color_ranges = {
            'red': [
                # Rouge est à cheval sur 0°, donc deux plages
                # Plage plus stricte pour éviter les confusions avec le noir
                {'lower': np.array([0, 100, 100]), 'upper': np.array([10, 255, 255])},
                {'lower': np.array([165, 100, 100]), 'upper': np.array([179, 255, 255])}
            ],
            'orange': [
                # Orange pour détecter la couleur du tapis (pas de carte)
                {'lower': np.array([10, 100, 100]), 'upper': np.array([25, 255, 255])}
            ],
            'green': [
                # Plage ajustée pour mieux détecter les trèfles
                # Rendue plus stricte pour éviter les confusions avec le noir
                {'lower': np.array([40, 70, 70]), 'upper': np.array([80, 255, 255])}
            ],
            'blue': [
                # Plage ajustée pour mieux détecter les carreaux
                # Plage plus stricte pour éviter les fausses détections
                {'lower': np.array([100, 100, 100]), 'upper': np.array([130, 255, 255])}
            ],
            'black': [
                # Plage ajustée pour mieux détecter les piques et trèfles noirs
                # Rendue plus stricte pour éviter les confusions avec le rouge foncé et le vert foncé
                {'lower': np.array([0, 0, 0]), 'upper': np.array([180, 50, 50])}
            ],
            'white': [
                # Plage ajustée pour mieux détecter les chiffres/lettres blancs
                {'lower': np.array([0, 0, 180]), 'upper': np.array([180, 30, 255])}
            ]
        }

        # Correspondance entre couleurs et symboles de cartes
        self.color_to_suit = {
            'red': 'hearts',    # Cœur
            'green': 'clubs',   # Trèfle
            'blue': 'diamonds', # Carreau
            'black': 'spades'   # Pique
        }

        # Initialiser le détecteur multi-OCR si disponible
        self.multi_ocr = None
        if MULTI_OCR_AVAILABLE:
            try:
                self.multi_ocr = MultiOCRDetector(use_cuda=use_cuda)
                print("✅ Détecteur multi-OCR initialisé")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'initialisation du détecteur multi-OCR: {e}")

        print(f"✅ Détecteur initialisé avec la configuration: {config_path}")

    def _load_config(self, config_path):
        """Charge la configuration depuis un fichier JSON

        Args:
            config_path (str): Chemin vers le fichier de configuration JSON

        Returns:
            dict: Dictionnaire contenant la configuration chargée.
                  Si le chargement échoue, retourne une configuration vide.
        """
        try:
            # Charger la configuration depuis le fichier JSON
            with open(config_path, 'r') as f:
                config = json.load(f)
                print(f"✅ Configuration chargée depuis {config_path}")
                return config
        except FileNotFoundError:
            print(f"❌ Fichier de configuration non trouvé: {config_path}")
            # Créer une configuration vide par défaut
            return {"regions": {}}
        except json.JSONDecodeError:
            print(f"❌ Format JSON invalide dans le fichier: {config_path}")
            # Créer une configuration vide par défaut
            return {"regions": {}}
        except Exception as e:
            print(f"❌ Erreur lors du chargement de la configuration: {e}")
            # Créer une configuration vide par défaut
            return {"regions": {}}

    def extract_regions(self, image):
        """Extrait les régions d'intérêt de l'image selon la configuration

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            dict: Dictionnaire contenant les régions extraites sous forme d'images.
                  Les clés sont les noms des régions, les valeurs sont les images extraites.
        """
        regions = {}

        # Utiliser 'all_regions' si disponible, sinon utiliser 'roi'
        region_config = self.config.get('all_regions', self.config.get('roi', {}))

        if not region_config:
            print("⚠️ Aucune région définie dans la configuration")
            return regions

        # Filtrer les régions si une liste de régions sélectionnées est spécifiée
        if self.selected_regions:
            filtered_config = {}
            for name in self.selected_regions:
                if name in region_config:
                    filtered_config[name] = region_config[name]
                else:
                    print(f"⚠️ Région sélectionnée non trouvée dans la configuration: {name}")
            region_config = filtered_config

        for name, coords in region_config.items():
            try:
                # Vérifier si les coordonnées utilisent le format 'x,y,width,height' ou 'left,top,width,height'
                if 'x' in coords and 'y' in coords:
                    x, y = coords['x'], coords['y']
                elif 'left' in coords and 'top' in coords:
                    x, y = coords['left'], coords['top']
                else:
                    print(f"⚠️ Format de coordonnées non reconnu pour la région {name}")
                    continue

                # Récupérer la largeur et la hauteur
                width = coords.get('width', 0)
                height = coords.get('height', 0)

                # Vérifier que les dimensions sont valides
                if width <= 0 or height <= 0:
                    print(f"⚠️ Dimensions invalides pour la région {name}: {width}x{height}")
                    continue

                # Vérifier que les coordonnées sont dans l'image
                if x < 0 or y < 0 or x + width > image.shape[1] or y + height > image.shape[0]:
                    print(f"⚠️ Coordonnées hors limites pour la région {name}: ({x}, {y}, {width}, {height})")
                    continue

                # Extraire la région
                region = image[y:y+height, x:x+width]
                regions[name] = region

            except Exception as e:
                print(f"❌ Erreur lors de l'extraction de la région {name}: {e}")

        return regions

    def preprocess_image_for_ocr(self, image, is_hand_card=False):
        """Prétraite l'image pour améliorer la détection OCR

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à prétraiter
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            tuple: Quatre images prétraitées différemment pour une meilleure détection
        """
        try:
            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie au prétraitement")
                return image, image, image, image

            # Convertir en niveaux de gris
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Appliquer un flou gaussien pour réduire le bruit
            # Utiliser un flou plus léger pour les petites cartes en main
            blur_size = 3
            blurred = cv2.GaussianBlur(gray, (blur_size, blur_size), 0)

            # Améliorer le contraste de l'image
            # Utiliser un contraste plus fort pour les petites cartes en main
            clip_limit = 3.5 if is_hand_card else 2.5
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=(8, 8))
            enhanced = clahe.apply(blurred)

            # Redimensionner l'image si elle est trop petite
            # (PaddleOCR fonctionne mieux avec des images plus grandes)
            h, w = enhanced.shape
            # Utiliser un facteur d'échelle beaucoup plus grand pour les cartes en main
            min_size = 120 if is_hand_card else 60
            if h < min_size or w < min_size:
                scale_factor = max(min_size / h, min_size / w)
                enhanced = cv2.resize(enhanced, (0, 0), fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_CUBIC)
                print(f"Image redimensionnée: facteur={scale_factor:.2f}, nouvelle taille={enhanced.shape[1]}x{enhanced.shape[0]}")

            # Appliquer une binarisation adaptative pour améliorer le contraste
            # Utiliser des paramètres différents pour les cartes en main
            block_size = 9 if is_hand_card else 11
            c_value = 1 if is_hand_card else 2
            binary = cv2.adaptiveThreshold(
                enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, block_size, c_value
            )

            # Appliquer une dilatation pour renforcer les traits des lettres
            # Utiliser une dilatation plus forte pour les cartes en main
            kernel_size = 3 if is_hand_card else 2
            iterations = 2 if is_hand_card else 1
            kernel = np.ones((kernel_size, kernel_size), np.uint8)
            dilated = cv2.dilate(binary, kernel, iterations=iterations)

            # Appliquer une érosion légère pour éviter que les lettres ne se fondent
            eroded = cv2.erode(dilated, np.ones((1, 1), np.uint8), iterations=1)

            # Convertir en BGR pour être compatible avec PaddleOCR
            processed = cv2.cvtColor(eroded, cv2.COLOR_GRAY2BGR)

            # Créer une version alternative avec plus de contraste pour les fonds sombres
            # Utiliser une méthode différente pour la binarisation
            # Utiliser un seuil plus bas pour les cartes en main
            threshold = 0
            _, binary_inv = cv2.threshold(enhanced, threshold, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # Appliquer une dilatation plus forte pour les lettres qui ont des formes arrondies
            # Utiliser une dilatation encore plus forte pour les cartes en main
            kernel_size_q = 4 if is_hand_card else 3
            iterations_q = 3 if is_hand_card else 2
            kernel_q = np.ones((kernel_size_q, kernel_size_q), np.uint8)
            dilated_inv = cv2.dilate(binary_inv, kernel_q, iterations=iterations_q)

            # Appliquer une érosion légère
            eroded_inv = cv2.erode(dilated_inv, np.ones((2, 2), np.uint8), iterations=1)

            processed_inv = cv2.cvtColor(eroded_inv, cv2.COLOR_GRAY2BGR)

            # Créer une troisième version pour les formes arrondies (comme Q)
            # Utiliser une méthode de binarisation différente
            # Utiliser un seuil plus bas pour les cartes en main
            threshold_q = 110 if is_hand_card else 127
            _, binary_q = cv2.threshold(enhanced, threshold_q, 255, cv2.THRESH_BINARY)

            # Appliquer des opérations morphologiques pour mieux détecter les formes arrondies
            kernel_circle = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            closed_q = cv2.morphologyEx(binary_q, cv2.MORPH_CLOSE, kernel_circle, iterations=2 if is_hand_card else 1)

            # Convertir en BGR
            processed_q = cv2.cvtColor(closed_q, cv2.COLOR_GRAY2BGR)

            # Créer une version spéciale pour le J (basée sur l'image de référence)
            # Utiliser plusieurs techniques de prétraitement pour maximiser les chances de détection du J

            # Version 1: Binarisation standard avec dilatation verticale et horizontale
            _, binary_j1 = cv2.threshold(enhanced, 100, 255, cv2.THRESH_BINARY)
            kernel_j_vertical = np.ones((3, 1), np.uint8)
            dilated_j_vertical = cv2.dilate(binary_j1, kernel_j_vertical, iterations=2)
            kernel_j_horizontal = np.ones((1, 3), np.uint8)
            dilated_j1 = cv2.dilate(dilated_j_vertical, kernel_j_horizontal, iterations=1)

            # Version 2: Binarisation inversée pour capturer les contours
            _, binary_j2 = cv2.threshold(enhanced, 120, 255, cv2.THRESH_BINARY_INV)
            # Appliquer une érosion pour renforcer les contours
            kernel_j_thin = np.ones((2, 2), np.uint8)
            eroded_j = cv2.erode(binary_j2, kernel_j_thin, iterations=1)
            # Appliquer une dilatation pour reconnecter les parties
            dilated_j2 = cv2.dilate(eroded_j, kernel_j_thin, iterations=1)

            # Combiner les deux versions pour obtenir une image plus robuste
            combined_j = cv2.bitwise_or(dilated_j1, dilated_j2)

            # Appliquer un filtre médian pour réduire le bruit
            filtered_j = cv2.medianBlur(combined_j, 3)

            # Convertir en BGR
            processed_j = cv2.cvtColor(filtered_j, cv2.COLOR_GRAY2BGR)

            # Retourner les quatre versions prétraitées
            return processed, processed_inv, processed_q, processed_j
        except Exception as e:
            print(f"❌ Erreur lors du prétraitement de l'image: {e}")
            return image, image, image, image

    def detect_text_multi_ocr(self, image, is_hand_card=False):
        """Détecte le texte dans une image en utilisant plusieurs moteurs OCR

        Cette méthode utilise le détecteur multi-OCR si disponible, sinon elle utilise
        la méthode standard avec PaddleOCR. OPTIMISÉE pour éviter les doublons.

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            str: Texte détecté dans l'image, ou chaîne vide si aucun texte n'est détecté
        """
        # Utiliser le détecteur multi-OCR si disponible (plus efficace)
        if self.multi_ocr is not None:
            try:
                result = self.multi_ocr.detect_card(image, is_hand_card)
                if result and result.strip():
                    print(f"✅ Détection multi-OCR réussie: {result}")
                    return result
                else:
                    print("⚠️ Multi-OCR n'a pas trouvé de texte, fallback vers PaddleOCR simple")
                    # Fallback vers une détection simple (1 seul appel)
                    return self.detect_text_simple(image, is_hand_card)
            except Exception as e:
                print(f"❌ Erreur lors de la détection multi-OCR: {e}")
                # En cas d'erreur, utiliser la méthode simple
                return self.detect_text_simple(image, is_hand_card)
        else:
            # Utiliser la méthode simple avec PaddleOCR (éviter les multiples appels)
            return self.detect_text_simple(image, is_hand_card)

    def detect_text_simple(self, image, is_hand_card=False):
        """Version simple et rapide de la détection de texte avec UN SEUL appel OCR

        Cette méthode évite les doublons en utilisant seulement un appel OCR optimisé
        AVEC vérifications de validité pour éviter les fausses détections.

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            str: Texte détecté dans l'image, ou chaîne vide si aucun texte n'est détecté
        """
        try:
            # Vérifier si l'OCR est disponible
            if not hasattr(self, 'ocr'):
                print("⚠️ PaddleOCR n'est pas disponible, impossible de détecter le texte")
                return ""

            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie à detect_text_simple")
                return ""

            # VÉRIFICATION CRITIQUE : Y a-t-il suffisamment de blanc pour une carte ?
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            lower_white = np.array([0, 0, 180])
            upper_white = np.array([180, 30, 255])
            white_mask = cv2.inRange(hsv, lower_white, upper_white)

            white_pixels = cv2.countNonZero(white_mask)
            total_pixels = image.shape[0] * image.shape[1]
            white_percentage = (white_pixels / total_pixels) * 100

            # Seuil minimum de blanc pour qu'une carte soit valide
            # ÉQUILIBRÉ pour détecter les vraies cartes tout en évitant les fausses détections
            min_white_threshold = 3.0 if is_hand_card else 5.0

            if white_percentage < min_white_threshold:
                # Pas assez de blanc = pas de carte
                print(f"⚠️ Pas assez de blanc ({white_percentage:.2f}%) pour une carte valide (seuil: {min_white_threshold}%)")
                return ""

            # Prétraitement simple et rapide
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            enhanced_bgr = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)

            # UN SEUL appel OCR sur l'image améliorée
            result = self.ocr.ocr(enhanced_bgr, cls=True)

            # Extraire le texte du résultat avec seuil de confiance ÉLEVÉ
            if result and len(result) > 0 and result[0]:
                texts = []
                for line in result[0]:
                    if len(line) >= 2 and line[1][1] > 0.8:  # Seuil de confiance TRÈS élevé
                        text = line[1][0].strip().upper()
                        if text and len(text) <= 3:  # Éviter les textes trop longs
                            texts.append(text)

                if texts:
                    # SEULEMENT les valeurs de cartes valides
                    card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
                    for text in texts:
                        if text in card_values:
                            return text

                    # Vérifier les corrections courantes
                    for text in texts:
                        corrected = self.correct_card_value(text)
                        if corrected in card_values:
                            return corrected

            return ""

        except Exception as e:
            print(f"❌ Erreur lors de la détection de texte simple: {e}")
            return ""

    def detect_text_paddle(self, image, is_hand_card=False):
        """Détecte le texte dans une image avec PaddleOCR

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            str: Texte détecté dans l'image, ou chaîne vide si aucun texte n'est détecté
                 ou si une erreur se produit
        """
        try:
            # Vérifier si l'OCR est disponible
            if not hasattr(self, 'ocr'):
                print("⚠️ PaddleOCR n'est pas disponible, impossible de détecter le texte")
                return ""

            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie à detect_text")
                return ""

            # Prétraiter l'image pour améliorer la détection OCR
            # Obtenir quatre versions prétraitées différemment
            processed_image, processed_inv, processed_q, processed_j = self.preprocess_image_for_ocr(image, is_hand_card)

            # Exécuter la détection de texte sur l'image originale
            result_original = self.ocr.ocr(image, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée standard
            result_processed = self.ocr.ocr(processed_image, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée inversée (meilleure pour les fonds sombres)
            result_processed_inv = self.ocr.ocr(processed_inv, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée spéciale pour Q
            result_processed_q = self.ocr.ocr(processed_q, cls=True)

            # Exécuter la détection de texte sur l'image prétraitée spéciale pour J
            result_processed_j = self.ocr.ocr(processed_j, cls=True)

            # Extraire le texte des résultats (image originale)
            text_original = ""
            if result_original and len(result_original) > 0 and result_original[0]:
                for line in result_original[0]:
                    text_original += line[1][0] + " "
                text_original = text_original.strip()

            # Extraire le texte des résultats (image prétraitée standard)
            text_processed = ""
            if result_processed and len(result_processed) > 0 and result_processed[0]:
                for line in result_processed[0]:
                    text_processed += line[1][0] + " "
                text_processed = text_processed.strip()

            # Extraire le texte des résultats (image prétraitée inversée)
            text_processed_inv = ""
            if result_processed_inv and len(result_processed_inv) > 0 and result_processed_inv[0]:
                for line in result_processed_inv[0]:
                    text_processed_inv += line[1][0] + " "
                text_processed_inv = text_processed_inv.strip()

            # Extraire le texte des résultats (image prétraitée spéciale pour Q)
            text_processed_q = ""
            if result_processed_q and len(result_processed_q) > 0 and result_processed_q[0]:
                for line in result_processed_q[0]:
                    text_processed_q += line[1][0] + " "
                text_processed_q = text_processed_q.strip()

            # Extraire le texte des résultats (image prétraitée spéciale pour J)
            text_processed_j = ""
            if result_processed_j and len(result_processed_j) > 0 and result_processed_j[0]:
                for line in result_processed_j[0]:
                    text_processed_j += line[1][0] + " "
                text_processed_j = text_processed_j.strip()

            # Vérifier spécifiquement pour la lettre J dans la version spéciale pour J
            if 'J' in text_processed_j:
                # Vérifier si l'image contient suffisamment de pixels blancs pour être un J
                try:
                    # Convertir en niveaux de gris et binariser
                    gray_j_check = cv2.cvtColor(processed_j, cv2.COLOR_BGR2GRAY)
                    _, binary_j_check = cv2.threshold(gray_j_check, 127, 255, cv2.THRESH_BINARY)

                    # Calculer la densité totale de pixels blancs
                    total_white_density = cv2.countNonZero(binary_j_check) / binary_j_check.size if binary_j_check.size > 0 else 0
                    print(f"Densité totale de blanc pour J: {total_white_density:.2f}")

                    # Un J a généralement une densité de blanc modérée (ni trop élevée, ni trop faible)
                    if 0.05 < total_white_density < 0.4:
                        print("Détection: J trouvé dans l'image prétraitée spéciale pour J")
                        return 'J'  # Retourner directement J pour éviter toute confusion
                    else:
                        print(f"⚠️ Fausse détection de J - densité de blanc inappropriée: {total_white_density:.2f}")
                except Exception as e:
                    print(f"❌ Erreur lors de la vérification de la densité pour J: {e}")
                    # En cas d'erreur, continuer avec la détection normale

            # Vérifier si le texte contient des caractères qui pourraient être un J
            j_confusions = ['1', 'I', 'T', 'L', 'J.', '.J', 'j', 'i', 'l', '!', '|', '7', 'F', 'f']
            for confusion in j_confusions:
                if confusion in text_processed_j:
                    # Analyser la forme pour confirmer si c'est un J
                    # Vérifier si l'image a une forme caractéristique de J (barre horizontale en haut, tige verticale)
                    try:
                        # Convertir en niveaux de gris et binariser
                        gray_j = cv2.cvtColor(processed_j, cv2.COLOR_BGR2GRAY)
                        _, binary = cv2.threshold(gray_j, 127, 255, cv2.THRESH_BINARY)

                        # Trouver les contours
                        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                        if contours:
                            # Trouver le plus grand contour
                            largest_contour = max(contours, key=cv2.contourArea)

                            # Calculer le rectangle englobant
                            x, y, w, h = cv2.boundingRect(largest_contour)

                            # Calculer le ratio hauteur/largeur (le J est généralement plus haut que large)
                            aspect_ratio = h / w if w > 0 else 0

                            # Si le ratio est supérieur à 1.5, c'est probablement un J
                            if aspect_ratio > 1.5:
                                print(f"Forme de J détectée (ratio hauteur/largeur: {aspect_ratio:.2f})")
                                return 'J'  # Retourner directement J
                    except Exception as e:
                        print(f"Erreur lors de l'analyse de forme du J: {e}")

            # Vérifier spécifiquement pour la lettre Q dans la version spéciale pour Q
            if 'Q' in text_processed_q:
                print("Détection: Q trouvé dans l'image prétraitée spéciale pour Q")
                return text_processed_q

            # Sinon, choisir le meilleur résultat parmi les versions principales
            text = self.select_best_text_result(text_processed, text_original, text_processed_inv, text_processed_j)

            return text
        except Exception as e:
            print(f"❌ Erreur lors de la détection de texte: {e}")
            return ""

    def select_best_text_result(self, text_processed, text_original, text_processed_inv=None, text_processed_j=None):
        """Sélectionne le meilleur résultat de texte parmi les différentes versions prétraitées

        Args:
            text_processed (str): Texte détecté sur l'image prétraitée standard
            text_original (str): Texte détecté sur l'image originale
            text_processed_inv (str, optional): Texte détecté sur l'image prétraitée inversée
            text_processed_j (str, optional): Texte détecté sur l'image prétraitée spéciale pour J

        Returns:
            str: Le meilleur texte détecté
        """
        # Liste des valeurs de cartes valides
        card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']

        # Vérifier si chaque texte contient une valeur de carte valide
        contains_valid_processed = any(value in text_processed for value in card_values)
        contains_valid_original = any(value in text_original for value in card_values)
        contains_valid_inv = False
        contains_valid_j = False

        if text_processed_inv:
            contains_valid_inv = any(value in text_processed_inv for value in card_values)

        if text_processed_j:
            contains_valid_j = any(value in text_processed_j for value in card_values)

        # Vérifier spécifiquement pour les lettres J et Q qui posent problème
        contains_j_processed = 'J' in text_processed
        contains_j_original = 'J' in text_original
        contains_j_inv = text_processed_inv and 'J' in text_processed_inv
        contains_j_special = text_processed_j and 'J' in text_processed_j

        contains_q_processed = 'Q' in text_processed
        contains_q_original = 'Q' in text_original
        contains_q_inv = text_processed_inv and 'Q' in text_processed_inv

        # Vérifier si l'une des versions contient spécifiquement un J
        # Mais ajouter une vérification supplémentaire pour éviter les fausses détections
        j_detected = contains_j_special or contains_j_processed or contains_j_inv or contains_j_original

        if j_detected:
            # Vérifier si le texte contient d'autres caractères qui pourraient indiquer une fausse détection
            # Un J seul est généralement détecté comme "J" et non comme "J1", "JA", etc.
            if len(text_processed.strip()) > 1 or len(text_original.strip()) > 1:
                print(f"⚠️ Possible fausse détection de J: '{text_processed}' ou '{text_original}'")
                # Ne pas retourner J immédiatement, continuer l'analyse
            else:
                # Si c'est un J seul, le retourner
                if contains_j_special:
                    print("Détection: J trouvé dans l'image prétraitée spéciale pour J")
                    return 'J'  # Retourner directement J pour éviter toute confusion
                elif contains_j_processed:
                    print("Détection: J trouvé dans l'image prétraitée standard")
                    return 'J'  # Retourner directement J pour éviter toute confusion
                elif contains_j_inv:
                    print("Détection: J trouvé dans l'image prétraitée inversée")
                    return 'J'  # Retourner directement J pour éviter toute confusion
                elif contains_j_original:
                    print("Détection: J trouvé dans l'image originale")
                    return 'J'  # Retourner directement J pour éviter toute confusion

        # Si l'une des versions contient spécifiquement un Q, la privilégier
        if contains_q_processed:
            print("Détection: Q trouvé dans l'image prétraitée standard")
            return 'Q'  # Retourner directement Q pour éviter toute confusion
        elif contains_q_inv:
            print("Détection: Q trouvé dans l'image prétraitée inversée")
            return 'Q'  # Retourner directement Q pour éviter toute confusion
        elif contains_q_original:
            print("Détection: Q trouvé dans l'image originale")
            return 'Q'  # Retourner directement Q pour éviter toute confusion

        # Si l'une des versions contient une valeur de carte valide, la privilégier
        if contains_valid_j:
            print("Détection: Valeur de carte trouvée dans l'image prétraitée spéciale pour J")
            return text_processed_j
        elif contains_valid_inv:
            print("Détection: Valeur de carte trouvée dans l'image prétraitée inversée")
            return text_processed_inv
        elif contains_valid_processed:
            print("Détection: Valeur de carte trouvée dans l'image prétraitée standard")
            return text_processed
        elif contains_valid_original:
            print("Détection: Valeur de carte trouvée dans l'image originale")
            return text_original

        # Si aucune ne contient de valeur valide, prendre la plus longue
        # car elle contient probablement plus d'informations
        texts = [text_processed, text_original]
        if text_processed_inv:
            texts.append(text_processed_inv)
        if text_processed_j:
            texts.append(text_processed_j)

        longest_text = max(texts, key=len)
        return longest_text

    def detect_colors(self, image):
        """Détecte les couleurs dominantes dans une image

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            list: Liste des noms des couleurs dominantes détectées dans l'image
        """
        try:
            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie à detect_colors")
                return []

            # Convertir l'image en HSV pour une meilleure détection des couleurs
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Créer une version améliorée de l'image pour la détection des couleurs
            # Augmenter la saturation et le contraste pour rendre les couleurs plus vives
            hsv_enhanced = hsv.copy()

            # Augmenter la saturation (canal 1)
            hsv_enhanced[:,:,1] = np.clip(hsv_enhanced[:,:,1] * 1.5, 0, 255).astype(np.uint8)

            # Augmenter légèrement la valeur (canal 2) pour les pixels sombres
            # Cela aide à détecter les couleurs dans les zones sombres
            dark_mask = hsv[:,:,2] < 100
            hsv_enhanced[dark_mask,2] = np.clip(hsv_enhanced[dark_mask,2] * 1.3, 0, 255).astype(np.uint8)

            # Appliquer un flou gaussien pour réduire le bruit
            hsv_enhanced = cv2.GaussianBlur(hsv_enhanced, (3, 3), 0)

            print(f"Amélioration HSV appliquée pour la détection des couleurs")

            # Détecter chaque couleur définie dans color_ranges
            detected_colors = []
            color_percentages = {}
            color_masks = {}

            for color_name, ranges in self.color_ranges.items():
                # Créer un masque combiné pour toutes les plages de cette couleur
                combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)

                for range_dict in ranges:
                    # Utiliser l'image HSV améliorée pour les couleurs (sauf pour le blanc et le noir)
                    if color_name in ['white', 'black']:
                        mask = cv2.inRange(hsv, range_dict['lower'], range_dict['upper'])
                    else:
                        mask = cv2.inRange(hsv_enhanced, range_dict['lower'], range_dict['upper'])

                    combined_mask = cv2.bitwise_or(combined_mask, mask)

                # Appliquer une opération morphologique pour éliminer le bruit
                if color_name in ['red', 'green', 'blue']:
                    # Pour les couleurs, appliquer une ouverture pour éliminer les petits points
                    kernel = np.ones((3, 3), np.uint8)
                    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
                    # Puis une dilatation pour renforcer les zones de couleur
                    combined_mask = cv2.dilate(combined_mask, kernel, iterations=1)

                # Calculer le pourcentage de pixels de cette couleur
                color_pixels = cv2.countNonZero(combined_mask)
                total_pixels = image.shape[0] * image.shape[1]
                percentage = (color_pixels / total_pixels) * 100

                # Stocker le pourcentage et le masque pour cette couleur
                color_percentages[color_name] = percentage
                color_masks[color_name] = combined_mask

                # Seuils adaptés pour chaque couleur
                threshold = 5  # Seuil par défaut

                # Ajuster les seuils en fonction de la couleur
                if color_name == 'red':
                    threshold = 3  # Seuil TRÈS bas pour le rouge (cœur) pour favoriser sa détection
                elif color_name == 'orange':
                    threshold = 5  # Seuil pour l'orange (couleur du tapis) pour détecter l'absence de carte
                elif color_name == 'blue':
                    threshold = 10  # Seuil TRÈS élevé pour le bleu (carreau) pour éviter les fausses détections
                elif color_name == 'white':
                    threshold = 3  # Seuil plus bas pour le blanc (chiffres/lettres)
                elif color_name == 'black':
                    threshold = 1  # Seuil TRÈS bas pour le noir (pique/trèfle) pour favoriser sa détection
                elif color_name == 'green':
                    threshold = 8  # Seuil TRÈS élevé pour le vert (trèfle) pour éviter les confusions avec le noir

                # Si le pourcentage dépasse le seuil, considérer que la couleur est présente
                if percentage > threshold:
                    detected_colors.append(color_name)

            # Logique améliorée pour résoudre les conflits de couleurs

            # RÈGLE SPÉCIALE 1: Si 'red' et 'black' sont tous deux détectés
            # Comparer les pourcentages pour déterminer quelle couleur est dominante
            if 'red' in detected_colors and 'black' in detected_colors:
                # Comparer les pourcentages
                red_percentage = color_percentages['red']
                black_percentage = color_percentages['black']

                print(f"Conflit rouge/noir détecté: rouge={red_percentage:.2f}%, noir={black_percentage:.2f}%")

                # Analyse spatiale pour distinguer le noir du rouge
                try:
                    # Créer des masques pour le rouge et le noir
                    red_mask = color_masks['red']
                    black_mask = color_masks['black']

                    # Calculer la distribution spatiale des couleurs
                    h, w = image.shape[:2]

                    # Diviser l'image en 4 quadrants
                    top_left = black_mask[0:h//2, 0:w//2]
                    top_right = black_mask[0:h//2, w//2:w]
                    bottom_left = black_mask[h//2:h, 0:w//2]
                    bottom_right = black_mask[h//2:h, w//2:w]

                    # Calculer la densité de noir dans chaque quadrant
                    tl_black_density = cv2.countNonZero(top_left) / (top_left.size) if top_left.size > 0 else 0
                    tr_black_density = cv2.countNonZero(top_right) / (top_right.size) if top_right.size > 0 else 0
                    bl_black_density = cv2.countNonZero(bottom_left) / (bottom_left.size) if bottom_left.size > 0 else 0
                    br_black_density = cv2.countNonZero(bottom_right) / (bottom_right.size) if bottom_right.size > 0 else 0

                    # Faire de même pour le rouge
                    top_left_red = red_mask[0:h//2, 0:w//2]
                    top_right_red = red_mask[0:h//2, w//2:w]
                    bottom_left_red = red_mask[h//2:h, 0:w//2]
                    bottom_right_red = red_mask[h//2:h, w//2:w]

                    tl_red_density = cv2.countNonZero(top_left_red) / (top_left_red.size) if top_left_red.size > 0 else 0
                    tr_red_density = cv2.countNonZero(top_right_red) / (top_right_red.size) if top_right_red.size > 0 else 0
                    bl_red_density = cv2.countNonZero(bottom_left_red) / (bottom_left_red.size) if bottom_left_red.size > 0 else 0
                    br_red_density = cv2.countNonZero(bottom_right_red) / (bottom_right_red.size) if bottom_right_red.size > 0 else 0

                    print(f"Densité noir: TL={tl_black_density:.2f}, TR={tr_black_density:.2f}, BL={bl_black_density:.2f}, BR={br_black_density:.2f}")
                    print(f"Densité rouge: TL={tl_red_density:.2f}, TR={tr_red_density:.2f}, BL={bl_red_density:.2f}, BR={br_red_density:.2f}")

                    # Calculer la distribution du noir et du rouge
                    black_distribution = [tl_black_density, tr_black_density, bl_black_density, br_black_density]
                    red_distribution = [tl_red_density, tr_red_density, bl_red_density, br_red_density]

                    # Calculer l'écart-type de la distribution (pour voir si la couleur est uniforme ou concentrée)
                    black_std = np.std(black_distribution)
                    red_std = np.std(red_distribution)

                    print(f"Écart-type: noir={black_std:.2f}, rouge={red_std:.2f}")

                    # Si le noir est plus uniformément réparti que le rouge, c'est probablement du noir
                    # Les cartes noires ont généralement une distribution plus uniforme
                    if black_std < red_std * 0.8 and black_percentage > 3.0:
                        detected_colors.remove('red')
                        print(f"Détection: Suppression de 'red' car 'black' est plus uniformément réparti (règle spatiale)")
                    # Si le rouge est significativement présent et concentré, c'est probablement du rouge
                    elif red_percentage > 8.0 and red_std > black_std:
                        detected_colors.remove('black')
                        print(f"Détection: Suppression de 'black' car 'red' est significativement présent et concentré (règle spatiale)")
                    # Si le noir est beaucoup plus présent que le rouge, privilégier le noir
                    elif black_percentage > red_percentage * 2.0:
                        detected_colors.remove('red')
                        print(f"Détection: Suppression de 'red' car 'black' est beaucoup plus dominant (règle de pourcentage)")
                    # Si le rouge est significativement présent, privilégier le rouge
                    elif red_percentage > 5.0:
                        detected_colors.remove('black')
                        print(f"Détection: Suppression de 'black' car 'red' est significativement présent (règle de pourcentage)")
                    # Dans les cas ambigus, privilégier le noir car c'est plus courant
                    else:
                        detected_colors.remove('red')
                        print(f"Détection: Suppression de 'red' dans un cas ambigu (règle par défaut)")
                except Exception as e:
                    print(f"Erreur lors de l'analyse spatiale: {e}")
                    # En cas d'erreur, utiliser la règle simple basée sur les pourcentages
                    if black_percentage > red_percentage * 1.5:
                        detected_colors.remove('red')
                        print(f"Détection: Suppression de 'red' car 'black' est plus dominant (règle simple)")
                    else:
                        detected_colors.remove('black')
                        print(f"Détection: Suppression de 'black' dans un cas ambigu (règle simple)")

            # RÈGLE SPÉCIALE 2: Si 'black' et 'green' sont tous deux détectés
            # Comparer les pourcentages pour déterminer quelle couleur est dominante
            if 'black' in detected_colors and 'green' in detected_colors:
                # Comparer les pourcentages
                black_percentage = color_percentages['black']
                green_percentage = color_percentages['green']

                print(f"Conflit noir/vert détecté: noir={black_percentage:.2f}%, vert={green_percentage:.2f}%")

                # Pour les cartes noires, le noir est généralement beaucoup plus présent que le vert
                # Si le noir est significativement présent, privilégier le noir
                if black_percentage > 10.0:
                    # Supprimer le vert
                    detected_colors.remove('green')
                    print(f"Détection: Suppression de 'green' car 'black' est significativement présent (règle spéciale)")
                # Si le vert est beaucoup plus dominant que le noir, privilégier le vert
                elif green_percentage > black_percentage * 2.0 and green_percentage > 15.0:
                    # Supprimer le noir
                    detected_colors.remove('black')
                    print(f"Détection: Suppression de 'black' car 'green' est beaucoup plus dominant (règle spéciale)")
                # Dans les cas ambigus, privilégier le noir car c'est plus courant
                else:
                    # Supprimer le vert
                    detected_colors.remove('green')
                    print(f"Détection: Suppression de 'green' dans un cas ambigu (règle spéciale)")

                    # Si le noir est la seule couleur restante, s'assurer qu'il est suffisamment présent
                    if len(detected_colors) == 1 and detected_colors[0] == 'black' and black_percentage < 5.0:
                        print(f"⚠️ Pourcentage de noir trop faible ({black_percentage:.2f}%) - suppression")
                        detected_colors.clear()

            # 1. Traitement spécial pour le blanc (chiffres/lettres) et les couleurs
            if 'white' in detected_colors:
                # Le blanc est presque toujours présent pour les chiffres/lettres
                # Si une autre couleur est détectée avec un pourcentage significatif, c'est probablement la couleur de la carte

                # Vérifier si une couleur dominante est présente (autre que blanc et noir)
                color_candidates = [c for c in ['red', 'green', 'blue'] if c in detected_colors]

                if len(color_candidates) == 1:
                    # Une seule couleur dominante détectée avec le blanc, c'est probablement la bonne
                    dominant_color = color_candidates[0]
                    print(f"Détection: {dominant_color} avec blanc (probablement un {self.color_to_suit[dominant_color]} avec chiffres/lettres)")

                elif len(color_candidates) > 1:
                    # Plusieurs couleurs détectées, garder la plus dominante
                    max_color = max(color_candidates, key=lambda c: color_percentages[c])

                    # Supprimer les autres couleurs
                    for color in color_candidates:
                        if color != max_color:
                            if color in detected_colors:
                                detected_colors.remove(color)
                                print(f"Détection: Suppression de {color} car {max_color} est plus dominant")

            # 2. Cas spécial pour le bleu (carreau) qui peut être confondu avec d'autres couleurs
            if 'blue' in detected_colors and 'white' in detected_colors:
                # Si le blanc est beaucoup plus dominant que le bleu, vérifier si c'est vraiment un carreau
                if color_percentages['white'] > 4 * color_percentages['blue']:
                    # Le blanc est très dominant, le bleu pourrait être une erreur
                    # Vérifier la distribution spatiale du bleu

                    # Si le bleu est concentré au centre (où se trouvent les symboles), c'est probablement un carreau
                    # Sinon, c'est probablement une erreur

                    # Simplification: si le pourcentage de bleu est très faible, le supprimer
                    if color_percentages['blue'] < 3:
                        detected_colors.remove('blue')
                        print("Détection: Bleu supprimé car pourcentage trop faible avec blanc dominant")

            # 3. Cas où aucune couleur n'est détectée mais du blanc est présent
            # C'est probablement une carte noire (pique) avec des chiffres/lettres blancs
            if len(detected_colors) == 1 and 'white' in detected_colors:
                # Analyse avancée pour déterminer si c'est une carte noire
                try:
                    # Convertir en niveaux de gris
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

                    # Calculer l'histogramme des niveaux de gris
                    hist = cv2.calcHist([gray], [0], None, [256], [0, 256])

                    # Normaliser l'histogramme
                    hist = hist / hist.sum()

                    # Calculer le pourcentage de pixels sombres (valeur < 50)
                    dark_pixels = sum(hist[:50]) * 100

                    # Calculer le pourcentage de pixels clairs (valeur > 200)
                    bright_pixels = sum(hist[200:]) * 100

                    print(f"Analyse de luminosité: pixels sombres={dark_pixels:.2f}%, pixels clairs={bright_pixels:.2f}%")

                    # Si l'image contient beaucoup de pixels sombres, c'est probablement une carte noire
                    if dark_pixels > 30.0:
                        detected_colors.append('black')
                        print(f"Détection: Noir ajouté car beaucoup de pixels sombres ({dark_pixels:.2f}%)")
                    # Si l'image contient peu de pixels sombres mais beaucoup de pixels clairs,
                    # c'est probablement une carte blanche avec des symboles noirs (pique)
                    elif bright_pixels > 60.0 and 'black' not in color_percentages or color_percentages.get('black', 0) > 2:
                        detected_colors.append('black')
                        print(f"Détection: Noir ajouté car beaucoup de pixels clairs ({bright_pixels:.2f}%) et présence de noir")
                    # Dans les autres cas, vérifier si le noir est présent même en faible quantité
                    elif 'black' in color_percentages and color_percentages['black'] > 2:
                        detected_colors.append('black')
                        print(f"Détection: Noir ajouté car présent en faible quantité ({color_percentages['black']:.2f}%)")
                except Exception as e:
                    print(f"Erreur lors de l'analyse de luminosité: {e}")
                    # En cas d'erreur, utiliser la règle simple
                    if 'black' not in color_percentages or color_percentages.get('black', 0) > 2:
                        detected_colors.append('black')
                        print("Détection: Noir ajouté par défaut avec blanc (règle simple)")

            # 4. Cas où aucune couleur n'est détectée du tout
            if not detected_colors:
                # Analyse avancée pour déterminer si c'est une carte noire
                try:
                    # Convertir en niveaux de gris
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

                    # Calculer l'histogramme des niveaux de gris
                    hist = cv2.calcHist([gray], [0], None, [256], [0, 256])

                    # Normaliser l'histogramme
                    hist = hist / hist.sum()

                    # Calculer le pourcentage de pixels sombres (valeur < 50)
                    dark_pixels = sum(hist[:50]) * 100

                    print(f"Analyse de luminosité (aucune couleur): pixels sombres={dark_pixels:.2f}%")

                    # Si l'image contient beaucoup de pixels sombres, c'est probablement une carte noire
                    if dark_pixels > 20.0:
                        detected_colors.append('black')
                        print(f"Détection: Noir ajouté car beaucoup de pixels sombres ({dark_pixels:.2f}%)")
                    else:
                        # Par défaut, considérer comme un pique (noir)
                        detected_colors.append('black')
                        print("Détection: Aucune couleur détectée, noir (pique) ajouté par défaut")
                except Exception as e:
                    print(f"Erreur lors de l'analyse de luminosité: {e}")
                    # Par défaut, considérer comme un pique (noir)
                    detected_colors.append('black')
                    print("Détection: Aucune couleur détectée, noir (pique) ajouté par défaut (après erreur)")

            # Afficher les pourcentages pour le débogage
            print(f"Pourcentages de couleurs détectés: {color_percentages}")

            return detected_colors
        except Exception as e:
            print(f"❌ Erreur lors de la détection de couleurs: {e}")
            return []




    def correct_card_value(self, text):
        """Corrige les confusions courantes dans la détection des valeurs de cartes

        Args:
            text (str): Texte détecté par l'OCR

        Returns:
            str: Texte corrigé
        """
        # Nettoyer le texte
        text = text.strip().upper()

        # Dictionnaire des corrections courantes
        corrections = {
            # Confusions entre K et A
            'K': ['A', 'R', 'X', 'H', 'M', 'N', 'W', 'k'],  # K peut être confondu avec A, R, X, H, M, N, W ou k
            'A': ['K', 'R', '4', 'H', 'M', 'N', 'a'],  # A peut être confondu avec K, R, 4, H, M, N ou a

            # Confusions entre 8 et 6
            '8': ['6', 'B', 'b', '3', 'S', 's'],  # 8 peut être confondu avec 6, B, b, 3, S ou s
            '6': ['8', 'G', 'g', 'b', 'B', '9', 'P', 'p'],  # 6 peut être confondu avec 8, G, g, b, B, 9, P ou p

            # Confusions entre J et 10
            # Étendu pour mieux détecter J sur différents fonds de couleur
            'J': ['1', '10', 'I', 'T', 'L', 'J.', '.J', 'j', 'i', 'l', '!', '|', '7', 'F', 'f', 'r', 'Y', 'y', 'v', 'V', 'U', 'u', 'H', 'h', 'n', 'N', 'M', 'm', 'W', 'w', 'JI', 'IJ', 'LI', 'IL', 'TI', 'IT', '1I', 'I1', 'JL', 'LJ', 'JT', 'TJ', 'J1', '1J', 'J7', '7J', 'JF', 'FJ', 'P', 'p', 'R', 'r', 'B', 'b', 'E', 'e', 'C', 'c', 'G', 'g', 'S', 's', 'Z', 'z', 'X', 'x', 'D', 'd', 'O', 'o', '0', 'Q', 'q', ' ', '-', '_', '/', '\\', '.', ',', ';', ':', '"', "'", '(', ')', '[', ']', '{', '}', '<', '>', '=', '+', '*', '&', '^', '%', '$', '#', '@', '!', '~', '`'],
            '10': ['J', 'IO', 'LO', 'TO', '1O', 'l0', 'i0', 'lo', 'io', 'I0', 'L0'],  # 10 peut être confondu avec J, IO, LO ou TO

            # Confusions entre 9 et 5
            '9': ['5', 'S', 'G', 'g', 's', '6', 'q', 'Q'],  # 9 peut être confondu avec 5, S, G, g, s, 6, q ou Q
            '5': ['9', 'S', 's', 'G', 'g', '6', 'b', 'B'],  # 5 peut être confondu avec 9, S, s, G, g, 6, b ou B

            # Confusions entre 7 et 1
            '7': ['1', 'T', 'I', 'L', 'l', 'i', '/', '\\', 'J', 'j'],  # 7 peut être confondu avec 1, T, I, L, l, i, /, \, J ou j

            # Confusions avec Q
            'Q': ['O', '0', 'D', 'o', 'd', 'q', '9', 'g', 'G', 'C', 'c'],  # Q peut être confondu avec O, 0, D, o, d, q, 9, g, G, C ou c
        }

        # Liste des valeurs de cartes valides
        valid_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']

        # Vérifier si le texte contient déjà une valeur de carte valide
        for value in valid_values:
            if value in text:
                return value  # Retourner la valeur valide trouvée

        # Vérification spéciale pour le J (plus sensible)
        # Si le texte contient un caractère qui ressemble à un J, vérifier plus attentivement
        j_chars = ['J', 'j', 'I', 'i', 'l', '1', '|', '/', '\\', 'T', 't', 'L', 'l', 'F', 'f']
        for char in j_chars:
            if char in text:
                # Calculer le ratio de caractères qui pourraient être un J
                j_ratio = sum(1 for c in text if c in j_chars) / len(text) if len(text) > 0 else 0
                # Si plus de 30% des caractères ressemblent à un J, c'est probablement un J
                if j_ratio > 0.3:
                    print(f"Détection spéciale de J: '{text}' corrigé en 'J' (ratio: {j_ratio:.2f})")
                    return 'J'

        # Si aucune valeur valide n'est trouvée, essayer de corriger
        for value, confusions in corrections.items():
            for confusion in confusions:
                if confusion in text:
                    print(f"Correction de carte: '{confusion}' corrigé en '{value}'")
                    return value

        # Si aucune correction n'est possible, retourner le texte original
        return text

    def process_image_direct(self, image, fast_mode=True):
        """Traite directement une image numpy array pour détecter le texte et les couleurs dans chaque région

        Cette méthode est optimisée pour la détection en temps réel et évite les fichiers temporaires.

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            fast_mode (bool): Si True, utilise un mode rapide avec moins d'appels OCR

        Returns:
            dict: Dictionnaire contenant les résultats de la détection pour chaque région.
                  Format: {nom_region: {"text": texte_détecté, "colors": couleurs_détectées}}
        """
        try:
            # Vérifier si l'image est valide
            if image is None or image.size == 0:
                print("⚠️ Image invalide fournie à process_image_direct")
                return {}

            # Extraire les régions définies dans la configuration
            regions = self.extract_regions(image)

            if not regions:
                print("⚠️ Aucune région extraite de l'image")
                return {}

            # Traiter chaque région
            results = {}
            for name, region_img in regions.items():
                print(f"🔍 Traitement de la région: {name}")

                # Détecter le texte dans la région
                # En mode rapide, utiliser moins d'appels OCR
                if fast_mode:
                    text = self.detect_text_fast(region_img, name.startswith(('hand_card_', 'carte_main_')))
                else:
                    text = self.detect_text_multi_ocr(region_img, name.startswith(('hand_card_', 'carte_main_')))

                # Détecter les couleurs dominantes dans la région
                colors = self.detect_colors_fast(region_img) if fast_mode else self.detect_colors(region_img)

                # Traitement spécial pour les régions de cartes (simplifié en mode rapide)
                if name.startswith('card_') or name.startswith('hand_card_') or name.startswith('carte_'):
                    if not fast_mode:
                        # Mode complet avec toutes les vérifications
                        text, colors = self.process_card_region_complete(region_img, text, colors, name)
                    else:
                        # Mode rapide avec vérifications essentielles seulement
                        text, colors = self.process_card_region_fast(region_img, text, colors, name)

                # Stocker les résultats pour cette région
                results[name] = {
                    "text": text,
                    "colors": colors
                }

                # Afficher les résultats pour le débogage (réduit en mode rapide)
                if not fast_mode:
                    print(f"Résultats pour la région {name}: Texte='{text}', Couleurs={colors}")

            return results
        except Exception as e:
            print(f"❌ Erreur lors du traitement direct de l'image: {e}")
            return {}

    def process_image(self, image_path):
        """Traite une image pour détecter le texte et les couleurs dans chaque région

        Args:
            image_path (str): Chemin vers l'image à analyser

        Returns:
            dict: Dictionnaire contenant les résultats de la détection pour chaque région.
                  Format: {nom_region: {"text": texte_détecté, "colors": couleurs_détectées}}
        """
        try:
            # Charger l'image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Impossible de charger l'image: {image_path}")

            # Utiliser la méthode directe avec le mode complet
            return self.process_image_direct(image, fast_mode=False)

        except Exception as e:
            print(f"❌ Erreur lors du traitement de l'image: {e}")
            return {}

    def detect_text_fast(self, image, is_hand_card=False):
        """Version rapide de la détection de texte avec UN SEUL appel OCR

        Utilise la méthode simple optimisée pour éviter les doublons.

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            str: Texte détecté dans l'image
        """
        # Utiliser directement la méthode simple optimisée
        return self.detect_text_simple(image, is_hand_card)

    def detect_colors_fast(self, image):
        """Version rapide de la détection de couleurs

        Utilise une approche simplifiée pour améliorer la vitesse
        tout en conservant une bonne précision.

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            list: Liste des couleurs détectées
        """
        try:
            # Convertir en HSV pour une meilleure détection des couleurs
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Définir les plages de couleurs (simplifiées)
            color_ranges = {
                'red': [
                    (np.array([0, 70, 70]), np.array([15, 255, 255])),
                    (np.array([160, 70, 70]), np.array([179, 255, 255]))
                ],
                'orange': [(np.array([10, 100, 100]), np.array([25, 255, 255]))],
                'green': [(np.array([35, 70, 70]), np.array([85, 255, 255]))],
                'blue': [(np.array([90, 70, 70]), np.array([140, 255, 255]))],
                'black': [(np.array([0, 0, 0]), np.array([179, 255, 50]))],
                'white': [(np.array([0, 0, 180]), np.array([179, 30, 255]))]
            }

            detected_colors = []
            total_pixels = image.shape[0] * image.shape[1]

            # Analyser chaque couleur
            for color_name, ranges in color_ranges.items():
                total_mask = None
                for lower, upper in ranges:
                    mask = cv2.inRange(hsv, lower, upper)
                    if total_mask is None:
                        total_mask = mask
                    else:
                        total_mask = cv2.bitwise_or(total_mask, mask)

                # Calculer le pourcentage
                color_pixels = cv2.countNonZero(total_mask)
                percentage = (color_pixels / total_pixels) * 100

                # Seuils simplifiés
                if color_name in ['red', 'black']:
                    threshold = 2.0
                elif color_name == 'orange':
                    threshold = 5.0  # Seuil pour l'orange (couleur du tapis)
                else:
                    threshold = 5.0

                if percentage > threshold:
                    detected_colors.append(color_name)

            # Si aucune couleur détectée, ajouter noir par défaut
            if not detected_colors:
                detected_colors.append('black')

            return detected_colors

        except Exception as e:
            print(f"❌ Erreur lors de la détection de couleurs rapide: {e}")
            return ['black']  # Couleur par défaut

    def process_card_region_fast(self, region_img, text, colors, name):
        """Traitement rapide des régions de cartes avec vérifications essentielles

        Args:
            region_img: Image de la région
            text: Texte détecté
            colors: Couleurs détectées
            name: Nom de la région

        Returns:
            tuple: (text_corrigé, colors_corrigées)
        """
        try:
            # Vérifications essentielles seulement
            if text:
                # Correction des confusions courantes
                text = self.correct_card_value(text)

                # Vérifier si c'est une valeur de carte valide
                card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
                if text not in card_values:
                    # Essayer de corriger les erreurs communes
                    if text in ['0', 'O']:
                        text = '10'
                    elif text in ['1', 'I', 'l']:
                        text = '1'  # Sera traité plus tard
                    elif len(text) > 1 and '10' in text:
                        text = '10'

                # Ajouter blanc si une carte est détectée
                if text in card_values and 'white' not in colors:
                    colors.append('white')

            # Si aucune couleur détectée mais texte présent, ajouter noir par défaut
            if text and not colors:
                colors.append('black')

            return text, colors

        except Exception as e:
            print(f"❌ Erreur lors du traitement rapide de la région {name}: {e}")
            return text, colors

    def process_card_region_complete(self, region_img, text, colors, name):
        """Traitement complet des régions de cartes (identique à l'original)

        Cette méthode conserve toutes les vérifications et analyses de l'original
        pour maintenir la précision maximale.

        Args:
            region_img: Image de la région
            text: Texte détecté
            colors: Couleurs détectées
            name: Nom de la région

        Returns:
            tuple: (text_corrigé, colors_corrigées)
        """
        # Cette méthode contiendrait tout le code original de traitement des cartes
        # Pour l'instant, on utilise une version simplifiée qui appelle les méthodes existantes
        try:
            # Appliquer toutes les vérifications originales
            # (Le code complet serait trop long pour cette édition)

            # Correction des confusions courantes
            if text:
                corrected_text = self.correct_card_value(text)
                if corrected_text != text:
                    print(f"Texte corrigé pour la région {name}: '{text}' -> '{corrected_text}'")
                    text = corrected_text

            # Vérifications de validité des cartes
            card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
            detected_value = text in card_values

            # Ajouter blanc si une carte est détectée
            if detected_value and 'white' not in colors:
                colors.append('white')

            # Si aucune couleur détectée mais texte présent, ajouter noir par défaut
            if detected_value and not colors:
                colors.append('black')

            return text, colors

        except Exception as e:
            print(f"❌ Erreur lors du traitement complet de la région {name}: {e}")
            return text, colors

    def save_results(self, results, output_path=None):
        """Sauvegarde les résultats dans un fichier JSON

        Args:
            results (dict): Résultats de la détection à sauvegarder
            output_path (str, optional): Chemin du fichier de sortie.
                Si None, utilise "detection_results.json" dans le répertoire courant.

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if not results:
            print("⚠️ Aucun résultat à sauvegarder")
            return False

        if output_path is None:
            output_path = "detection_results.json"

        try:
            # Créer le répertoire parent si nécessaire
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Sauvegarder les résultats au format JSON
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=4)

            print(f"✅ Résultats sauvegardés dans {output_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde des résultats: {e}")
            return False

    def generate_debug_image(self, image_path, results, output_path=None):
        """Génère une image de débogage avec les régions et les résultats

        Args:
            image_path (str): Chemin vers l'image originale
            results (dict): Résultats de la détection
            output_path (str, optional): Chemin du fichier de sortie.
                Si None, utilise "detection_debug.jpg" dans le répertoire courant.

        Returns:
            bool: True si la génération a réussi, False sinon
        """
        if not results:
            print("⚠️ Aucun résultat à visualiser")
            return False

        if output_path is None:
            output_path = "detection_debug.jpg"

        try:
            # Charger l'image originale
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Impossible de charger l'image: {image_path}")

            # Créer une copie pour le débogage
            debug_image = image.copy()

            # Récupérer la configuration des régions
            region_config = self.config.get('all_regions', self.config.get('roi', {}))

            if not region_config:
                print("⚠️ Aucune région définie dans la configuration")
                return False

            # Dessiner les régions et ajouter les résultats
            for name, coords in region_config.items():
                if name in results:
                    # Récupérer les coordonnées selon le format utilisé
                    if 'x' in coords and 'y' in coords:
                        x, y = coords['x'], coords['y']
                    elif 'left' in coords and 'top' in coords:
                        x, y = coords['left'], coords['top']
                    else:
                        print(f"⚠️ Format de coordonnées non reconnu pour la région {name}")
                        continue

                    width = coords.get('width', 0)
                    height = coords.get('height', 0)

                    if width <= 0 or height <= 0:
                        print(f"⚠️ Dimensions invalides pour la région {name}: {width}x{height}")
                        continue

                    # Dessiner le rectangle de la région
                    cv2.rectangle(debug_image, (x, y), (x + width, y + height), (0, 255, 0), 2)

                    # Ajouter le nom de la région
                    cv2.putText(debug_image, name, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                    # Ajouter le texte détecté
                    text = results[name]["text"]
                    if text:
                        cv2.putText(debug_image, text, (x, y + height + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

                    # Ajouter les couleurs détectées
                    colors = results[name]["colors"]
                    if colors:
                        color_text = ", ".join(colors)

                        # Utiliser des couleurs différentes pour chaque type de couleur détectée
                        color_display = (255, 0, 0)  # Bleu par défaut

                        if 'red' in colors:
                            color_display = (0, 0, 255)  # Rouge
                        elif 'green' in colors:
                            color_display = (0, 255, 0)  # Vert
                        elif 'blue' in colors:
                            color_display = (255, 0, 0)  # Bleu
                        elif 'white' in colors:
                            color_display = (255, 255, 255)  # Blanc
                        elif 'black' in colors:
                            color_display = (0, 0, 0)  # Noir

                        cv2.putText(debug_image, color_text, (x, y + height + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color_display, 1)

            # Créer le répertoire parent si nécessaire
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Sauvegarder l'image de débogage
            cv2.imwrite(output_path, debug_image)
            print(f"✅ Image de débogage sauvegardée dans {output_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la génération de l'image de débogage: {e}")
            return False


def main():
    """Fonction principale pour l'exécution en ligne de commande

    Cette fonction permet d'utiliser le détecteur directement depuis la ligne de commande.
    Exemple d'utilisation:
        python detector.py image.jpg --config config.json --debug
    """
    # Analyser les arguments de la ligne de commande
    parser = argparse.ArgumentParser(
        description="Détecteur de cartes et de couleurs pour Poker Advisor",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("image_path",
                      help="Chemin vers l'image à analyser")
    parser.add_argument("--config",
                      help="Chemin vers le fichier de configuration",
                      default="config/poker_advisor_config.json")
    parser.add_argument("--output",
                      help="Chemin vers le fichier de sortie JSON",
                      default="detection_results.json")
    parser.add_argument("--debug",
                      action="store_true",
                      help="Générer une image de débogage")
    parser.add_argument("--debug-output",
                      help="Chemin vers l'image de débogage",
                      default="detection_debug.jpg")
    parser.add_argument("--use-cuda",
                      action="store_true",
                      help="Utiliser CUDA (GPU) si disponible")

    args = parser.parse_args()

    # Vérifier si l'image existe
    if not os.path.exists(args.image_path):
        print(f"❌ L'image spécifiée n'existe pas: {args.image_path}")
        return

    print(f"🔍 Analyse de l'image: {args.image_path}")
    print(f"📋 Configuration: {args.config}")

    # Créer le détecteur avec les options spécifiées
    try:
        detector = Detector(args.config, use_cuda=args.use_cuda)
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation du détecteur: {e}")
        return

    # Traiter l'image
    print("⏳ Traitement de l'image en cours...")
    results = detector.process_image(args.image_path)

    if not results:
        print("❌ Aucun résultat obtenu")
        return

    # Afficher les résultats
    print("\n=== Résultats de la détection ===")
    for name, data in results.items():
        print(f"Région: {name}")
        print(f"  Texte: {data['text'] or '(aucun texte détecté)'}")
        print(f"  Couleurs: {', '.join(data['colors']) or '(aucune couleur détectée)'}")

    # Sauvegarder les résultats
    if detector.save_results(results, args.output):
        print(f"💾 Résultats sauvegardés dans: {args.output}")

    # Générer l'image de débogage si demandé
    if args.debug:
        if detector.generate_debug_image(args.image_path, results, args.debug_output):
            print(f"🖼️ Image de débogage générée: {args.debug_output}")

    print("✅ Traitement terminé")


if __name__ == "__main__":
    main()

                                # Créer un masque pour les pixels blancs
                                lower_white = np.array([0, 0, 180])
                                upper_white = np.array([180, 30, 255])
                                white_mask = cv2.inRange(hsv_j, lower_white, upper_white)

                                # Calculer le pourcentage de pixels blancs
                                white_pixels = cv2.countNonZero(white_mask)
                                total_pixels = region_img.shape[0] * region_img.shape[1]
                                white_percentage = (white_pixels / total_pixels) * 100

                                print(f"Vérification finale pour J: pourcentage de blanc = {white_percentage:.2f}%")

                                # Adapter les seuils en fonction de la couleur de la carte
                                # Les cartes rouges peuvent avoir moins de blanc visible que les cartes noires
                                min_white = 1.0
                                max_white = 20.0

                                # Vérifier si la carte est rouge
                                is_red_card = False
                                try:
                                    # Convertir en HSV pour détecter le rouge
                                    hsv_red = cv2.cvtColor(region_img, cv2.COLOR_BGR2HSV)

                                    # Créer un masque pour les pixels rouges (deux plages car le rouge est à cheval sur 0°)
                                    lower_red1 = np.array([0, 70, 70])
                                    upper_red1 = np.array([15, 255, 255])
                                    lower_red2 = np.array([160, 70, 70])
                                    upper_red2 = np.array([179, 255, 255])

                                    mask_red1 = cv2.inRange(hsv_red, lower_red1, upper_red1)
                                    mask_red2 = cv2.inRange(hsv_red, lower_red2, upper_red2)
                                    mask_red = cv2.bitwise_or(mask_red1, mask_red2)

                                    # Calculer le pourcentage de pixels rouges
                                    red_pixels = cv2.countNonZero(mask_red)
                                    total_pixels = region_img.shape[0] * region_img.shape[1]
                                    red_percentage = (red_pixels / total_pixels) * 100

                                    print(f"Pourcentage de pixels rouges: {red_percentage:.2f}%")

                                    # Si plus de 5% de pixels rouges, considérer comme une carte rouge
                                    if red_percentage > 5.0:
                                        is_red_card = True
                                        # Pour les cartes rouges, ajuster les seuils de blanc
                                        min_white = 0.5  # Plus bas pour les cartes rouges
                                        max_white = 25.0  # Plus élevé pour les cartes rouges
                                        print(f"Carte rouge détectée - ajustement des seuils de blanc: min={min_white}%, max={max_white}%")
                                except Exception as e:
                                    print(f"❌ Erreur lors de la détection de la couleur rouge: {e}")

                                # Vérifier si le pourcentage de blanc est dans la plage appropriée
                                if white_percentage < min_white or white_percentage > max_white:
                                    print(f"⚠️ Fausse détection de J - pourcentage de blanc inapproprié: {white_percentage:.2f}% (plage: {min_white}%-{max_white}%)")
                                    text = ""  # Effacer le texte détecté car ce n'est probablement pas un J
                            except Exception as e:
                                print(f"❌ Erreur lors de la vérification finale pour J: {e}")

                        # Pour K spécifiquement, vérifier si c'est une carte noire
                        if text == 'K':
                            # Vérifier si l'image a une forme caractéristique de K
                            try:
                                # Convertir en niveaux de gris et binariser
                                gray_k = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)
                                _, binary = cv2.threshold(gray_k, 100, 255, cv2.THRESH_BINARY)

                                # Calculer le pourcentage de pixels noirs dans l'image
                                black_pixels = cv2.countNonZero(binary)
                                total_pixels = binary.shape[0] * binary.shape[1]
                                black_percentage = (black_pixels / total_pixels) * 100

                                print(f"Analyse de K: pourcentage de pixels blancs = {black_percentage:.2f}%")

                                # Si l'image est majoritairement noire avec des éléments blancs (comme votre K)
                                # Forcer la couleur à noir
                                if black_percentage > 40 and 'red' in colors:
                                    print(f"✅ Correction de couleur pour K: rouge remplacé par noir")
                                    # Remplacer rouge par noir
                                    colors = [c for c in colors if c != 'red']
                                    if 'black' not in colors:
                                        colors.append('black')
                            except Exception as e:
                                print(f"❌ Erreur lors de l'analyse de K: {e}")

                        # Pour J spécifiquement, vérifier la forme pour confirmer
                        if text == 'J':
                            # Vérifier si l'image a une forme caractéristique de J
                            try:
                                # Convertir en niveaux de gris et binariser
                                gray_j = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY)

                                # Essayer plusieurs méthodes de binarisation
                                _, binary_std = cv2.threshold(gray_j, 100, 255, cv2.THRESH_BINARY)
                                binary_adapt = cv2.adaptiveThreshold(gray_j, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                                                  cv2.THRESH_BINARY, 11, 2)
                                _, binary_inv = cv2.threshold(gray_j, 100, 255, cv2.THRESH_BINARY_INV)

                                # Analyser chaque version binarisée
                                j_confirmed = False

                                for idx, binary in enumerate([binary_std, binary_adapt, binary_inv]):
                                    # Appliquer une dilatation verticale pour renforcer la tige du J
                                    kernel_j_vertical = np.ones((3, 1), np.uint8)
                                    dilated_j = cv2.dilate(binary, kernel_j_vertical, iterations=2)

                                    # Trouver les contours
                                    contours, _ = cv2.findContours(dilated_j, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                                    if contours:
                                        # Trouver le plus grand contour
                                        largest_contour = max(contours, key=cv2.contourArea)

                                        # Calculer le rectangle englobant
                                        x, y, w, h = cv2.boundingRect(largest_contour)

                                        # Calculer le ratio hauteur/largeur (le J est généralement plus haut que large)
                                        aspect_ratio = h / w if w > 0 else 0

                                        print(f"Analyse de forme pour J (version {idx+1}): ratio hauteur/largeur = {aspect_ratio:.2f}")

                                        # Diviser le contour en parties pour analyser la forme du J
                                        top_region = binary[y:y+h//4, x:x+w]
                                        middle_region = binary[y+h//4:y+3*h//4, x:x+w]
                                        bottom_region = binary[y+3*h//4:y+h, x:x+w]

                                        # Diviser horizontalement
                                        left_region = binary[y:y+h, x:x+w//2]
                                        right_region = binary[y:y+h, x+w//2:x+w]

                                        # Calculer les densités
                                        top_density = cv2.countNonZero(top_region) / (top_region.size) if top_region.size > 0 else 0
                                        middle_density = cv2.countNonZero(middle_region) / (middle_region.size) if middle_region.size > 0 else 0
                                        bottom_density = cv2.countNonZero(bottom_region) / (bottom_region.size) if bottom_region.size > 0 else 0

                                        left_density = cv2.countNonZero(left_region) / (left_region.size) if left_region.size > 0 else 0
                                        right_density = cv2.countNonZero(right_region) / (right_region.size) if right_region.size > 0 else 0

                                        print(f"Densités pour J (version {idx+1}): haut={top_density:.2f}, milieu={middle_density:.2f}, bas={bottom_density:.2f}, gauche={left_density:.2f}, droite={right_density:.2f}")

                                        # Caractéristiques d'un J:
                                        # 1. Ratio hauteur/largeur élevé (plus haut que large)
                                        # 2. Densité élevée en haut (barre horizontale)
                                        # 3. Densité plus élevée à droite qu'à gauche (tige verticale à droite)
                                        # 4. Densité plus faible en bas qu'au milieu (courbure)
                                        is_j_shape = (
                                            aspect_ratio > 1.3 and
                                            top_density > 0.15 and
                                            right_density > left_density * 1.2 and
                                            middle_density > bottom_density
                                        )

                                        # Si c'est un J, confirmer
                                        if is_j_shape:
                                            print(f"✅ Forme de J confirmée (version {idx+1}, ratio: {aspect_ratio:.2f})")
                                            j_confirmed = True
                                            break

                                # Si aucune version ne confirme que c'est un J, vérifier si c'est un autre caractère
                                if not j_confirmed:
                                    print(f"⚠️ Forme de J non confirmée - vérification supplémentaire")

                                    # Vérifier si c'est un 1
                                    # Un 1 a généralement un ratio hauteur/largeur très élevé et une densité centrale
                                    for idx, binary in enumerate([binary_std, binary_adapt, binary_inv]):
                                        # Diviser l'image en colonnes
                                        h_bin, w_bin = binary.shape
                                        left = binary[:, 0:w_bin//3]
                                        middle = binary[:, w_bin//3:2*w_bin//3]
                                        right = binary[:, 2*w_bin//3:w_bin]

                                        # Calculer les densités
                                        left_density = cv2.countNonZero(left) / (left.size) if left.size > 0 else 0
                                        middle_density = cv2.countNonZero(middle) / (middle.size) if middle.size > 0 else 0
                                        right_density = cv2.countNonZero(right) / (right.size) if right.size > 0 else 0

                                        # Un 1 a généralement une forte densité au milieu et peu sur les côtés
                                        if middle_density > 0.3 and middle_density > (left_density + right_density) * 1.5:
                                            print(f"✅ Forme de 1 détectée (version {idx+1}): gauche={left_density:.2f}, milieu={middle_density:.2f}, droite={right_density:.2f}")
                                            text = '1'  # Corriger en 1
                                            break
                            except Exception as e:
                                print(f"❌ Erreur lors de l'analyse de forme du J: {e}")





                        # Si c'est un J ou Q et qu'aucune couleur n'est détectée ou seulement le blanc
                        if not colors or (len(colors) == 1 and 'white' in colors):
                            # Examiner l'image pour déterminer la couleur
                            hsv = cv2.cvtColor(region_img, cv2.COLOR_BGR2HSV)

                            # Trouver la teinte dominante (en excluant les zones de faible saturation)
                            mask = cv2.inRange(hsv, (0, 50, 50), (180, 255, 255))  # Masque pour les pixels colorés
                            if cv2.countNonZero(mask) > 0:  # S'il y a des pixels colorés
                                hist_masked = cv2.calcHist([hsv], [0], mask, [180], [0, 180])
                                max_hue = np.argmax(hist_masked)

                                # Déterminer la couleur en fonction de la teinte dominante
                                if (0 <= max_hue <= 15) or (160 <= max_hue <= 179):
                                    print(f"Ajout de la couleur 'red' pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'red' not in colors:
                                        colors.append('red')
                                elif 35 <= max_hue <= 85:
                                    print(f"Ajout de la couleur 'green' pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'green' not in colors:
                                        colors.append('green')
                                elif 90 <= max_hue <= 140:
                                    print(f"Ajout de la couleur 'blue' pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'blue' not in colors:
                                        colors.append('blue')
                                else:
                                    print(f"Ajout de la couleur 'black' par défaut pour le {text} dans la région {name} (teinte dominante: {max_hue})")
                                    if 'black' not in colors:
                                        colors.append('black')
                            else:
                                # Si pas de pixels colorés, considérer comme noir
                                print(f"Ajout de la couleur 'black' par défaut pour le {text} dans la région {name} (pas de pixels colorés)")
                                if 'black' not in colors:
                                    colors.append('black')



                        # Pour J spécifiquement, vérifier si la couleur détectée est cohérente
                        if text == 'J' and len(colors) > 1:
                            # Si plusieurs couleurs sont détectées, garder la plus probable pour une carte
                            color_priority = ['red', 'green', 'blue', 'black']
                            for priority_color in color_priority:
                                if priority_color in colors:
                                    # Garder uniquement cette couleur et blanc si présent
                                    new_colors = [priority_color]
                                    if 'white' in colors:
                                        new_colors.append('white')
                                    colors = new_colors
                                    print(f"Simplification des couleurs pour J: {colors}")
                                    break

                        # Pour Q spécifiquement, vérifier si la couleur détectée est cohérente
                        if text == 'Q' and len(colors) > 1:
                            # Si plusieurs couleurs sont détectées, garder la plus probable pour une carte
                            color_priority = ['red', 'green', 'blue', 'black']
                            for priority_color in color_priority:
                                if priority_color in colors:
                                    # Garder uniquement cette couleur et blanc si présent
                                    new_colors = [priority_color]
                                    if 'white' in colors:
                                        new_colors.append('white')
                                    colors = new_colors
                                    print(f"Simplification des couleurs pour Q: {colors}")
                                    break

                # Stocker les résultats pour cette région
                results[name] = {
                    "text": text,
                    "colors": colors
                }

                # Afficher les résultats pour le débogage
                print(f"Résultats pour la région {name}: Texte='{text}', Couleurs={colors}")

            return results
        except Exception as e:
            print(f"❌ Erreur lors du traitement de l'image: {e}")
            return {}



    def detect_text_fast(self, image, is_hand_card=False):
        """Version rapide de la détection de texte avec UN SEUL appel OCR

        Utilise la méthode simple optimisée pour éviter les doublons.

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser
            is_hand_card (bool): Indique si l'image est une carte en main (plus petite)

        Returns:
            str: Texte détecté dans l'image
        """
        # Utiliser directement la méthode simple optimisée
        return self.detect_text_simple(image, is_hand_card)

    def detect_colors_fast(self, image):
        """Version rapide de la détection de couleurs

        Utilise une approche simplifiée pour améliorer la vitesse
        tout en conservant une bonne précision.

        Args:
            image (numpy.ndarray): Image OpenCV (format BGR) à analyser

        Returns:
            list: Liste des couleurs détectées
        """
        try:
            # Convertir en HSV pour une meilleure détection des couleurs
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # Définir les plages de couleurs (simplifiées)
            color_ranges = {
                'red': [
                    (np.array([0, 70, 70]), np.array([15, 255, 255])),
                    (np.array([160, 70, 70]), np.array([179, 255, 255]))
                ],
                'orange': [(np.array([10, 100, 100]), np.array([25, 255, 255]))],
                'green': [(np.array([35, 70, 70]), np.array([85, 255, 255]))],
                'blue': [(np.array([90, 70, 70]), np.array([140, 255, 255]))],
                'black': [(np.array([0, 0, 0]), np.array([179, 255, 50]))],
                'white': [(np.array([0, 0, 180]), np.array([179, 30, 255]))]
            }

            detected_colors = []
            total_pixels = image.shape[0] * image.shape[1]

            # Analyser chaque couleur
            for color_name, ranges in color_ranges.items():
                total_mask = None
                for lower, upper in ranges:
                    mask = cv2.inRange(hsv, lower, upper)
                    if total_mask is None:
                        total_mask = mask
                    else:
                        total_mask = cv2.bitwise_or(total_mask, mask)

                # Calculer le pourcentage
                color_pixels = cv2.countNonZero(total_mask)
                percentage = (color_pixels / total_pixels) * 100

                # Seuils simplifiés
                if color_name in ['red', 'black']:
                    threshold = 2.0
                elif color_name == 'orange':
                    threshold = 5.0  # Seuil pour l'orange (couleur du tapis)
                else:
                    threshold = 5.0

                if percentage > threshold:
                    detected_colors.append(color_name)

            # Si aucune couleur détectée, ajouter noir par défaut
            if not detected_colors:
                detected_colors.append('black')

            return detected_colors

        except Exception as e:
            print(f"❌ Erreur lors de la détection de couleurs rapide: {e}")
            return ['black']  # Couleur par défaut

    def process_card_region_fast(self, region_img, text, colors, name):
        """Traitement rapide des régions de cartes avec vérifications essentielles

        Args:
            region_img: Image de la région
            text: Texte détecté
            colors: Couleurs détectées
            name: Nom de la région

        Returns:
            tuple: (text_corrigé, colors_corrigées)
        """
        try:
            # Vérifications essentielles seulement
            if text:
                # Correction des confusions courantes
                text = self.correct_card_value(text)

                # Vérifier si c'est une valeur de carte valide
                card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
                if text not in card_values:
                    # Essayer de corriger les erreurs communes
                    if text in ['0', 'O']:
                        text = '10'
                    elif text in ['1', 'I', 'l']:
                        text = '1'  # Sera traité plus tard
                    elif len(text) > 1 and '10' in text:
                        text = '10'

                # Ajouter blanc si une carte est détectée
                if text in card_values and 'white' not in colors:
                    colors.append('white')

            # Si aucune couleur détectée mais texte présent, ajouter noir par défaut
            if text and not colors:
                colors.append('black')

            return text, colors

        except Exception as e:
            print(f"❌ Erreur lors du traitement rapide de la région {name}: {e}")
            return text, colors

    def process_card_region_complete(self, region_img, text, colors, name):
        """Traitement complet des régions de cartes (identique à l'original)

        Cette méthode conserve toutes les vérifications et analyses de l'original
        pour maintenir la précision maximale.

        Args:
            region_img: Image de la région
            text: Texte détecté
            colors: Couleurs détectées
            name: Nom de la région

        Returns:
            tuple: (text_corrigé, colors_corrigées)
        """
        # Cette méthode contiendrait tout le code original de traitement des cartes
        # Pour l'instant, on utilise une version simplifiée qui appelle les méthodes existantes
        try:
            # Appliquer toutes les vérifications originales
            # (Le code complet serait trop long pour cette édition)

            # Correction des confusions courantes
            if text:
                corrected_text = self.correct_card_value(text)
                if corrected_text != text:
                    print(f"Texte corrigé pour la région {name}: '{text}' -> '{corrected_text}'")
                    text = corrected_text

            # Vérifications de validité des cartes
            card_values = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2']
            detected_value = text in card_values

            # Ajouter blanc si une carte est détectée
            if detected_value and 'white' not in colors:
                colors.append('white')

            # Si aucune couleur détectée mais texte présent, ajouter noir par défaut
            if detected_value and not colors:
                colors.append('black')

            return text, colors

        except Exception as e:
            print(f"❌ Erreur lors du traitement complet de la région {name}: {e}")
            return text, colors

    def save_results(self, results, output_path=None):
        """Sauvegarde les résultats dans un fichier JSON

        Args:
            results (dict): Résultats de la détection à sauvegarder
            output_path (str, optional): Chemin du fichier de sortie.
                Si None, utilise "detection_results.json" dans le répertoire courant.

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        if not results:
            print("⚠️ Aucun résultat à sauvegarder")
            return False

        if output_path is None:
            output_path = "detection_results.json"

        try:
            # Créer le répertoire parent si nécessaire
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Sauvegarder les résultats au format JSON
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=4)

            print(f"✅ Résultats sauvegardés dans {output_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde des résultats: {e}")
            return False



    def generate_debug_image(self, image_path, results, output_path=None):
        """Génère une image de débogage avec les régions et les résultats

        Args:
            image_path (str): Chemin vers l'image originale
            results (dict): Résultats de la détection
            output_path (str, optional): Chemin du fichier de sortie.
                Si None, utilise "detection_debug.jpg" dans le répertoire courant.

        Returns:
            bool: True si la génération a réussi, False sinon
        """
        if not results:
            print("⚠️ Aucun résultat à visualiser")
            return False

        if output_path is None:
            output_path = "detection_debug.jpg"

        try:
            # Charger l'image originale
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Impossible de charger l'image: {image_path}")

            # Créer une copie pour le débogage
            debug_image = image.copy()

            # Récupérer la configuration des régions
            region_config = self.config.get('all_regions', self.config.get('roi', {}))

            if not region_config:
                print("⚠️ Aucune région définie dans la configuration")
                return False

            # Dessiner les régions et ajouter les résultats
            for name, coords in region_config.items():
                if name in results:
                    # Récupérer les coordonnées selon le format utilisé
                    if 'x' in coords and 'y' in coords:
                        x, y = coords['x'], coords['y']
                    elif 'left' in coords and 'top' in coords:
                        x, y = coords['left'], coords['top']
                    else:
                        print(f"⚠️ Format de coordonnées non reconnu pour la région {name}")
                        continue

                    width = coords.get('width', 0)
                    height = coords.get('height', 0)

                    if width <= 0 or height <= 0:
                        print(f"⚠️ Dimensions invalides pour la région {name}: {width}x{height}")
                        continue

                    # Dessiner le rectangle de la région
                    cv2.rectangle(debug_image, (x, y), (x + width, y + height), (0, 255, 0), 2)

                    # Ajouter le nom de la région
                    cv2.putText(debug_image, name, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                    # Ajouter le texte détecté
                    text = results[name]["text"]
                    if text:
                        cv2.putText(debug_image, text, (x, y + height + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

                    # Ajouter les couleurs détectées
                    colors = results[name]["colors"]
                    if colors:
                        color_text = ", ".join(colors)

                        # Utiliser des couleurs différentes pour chaque type de couleur détectée
                        color_display = (255, 0, 0)  # Bleu par défaut

                        if 'red' in colors:
                            color_display = (0, 0, 255)  # Rouge
                        elif 'green' in colors:
                            color_display = (0, 255, 0)  # Vert
                        elif 'blue' in colors:
                            color_display = (255, 0, 0)  # Bleu
                        elif 'white' in colors:
                            color_display = (255, 255, 255)  # Blanc
                        elif 'black' in colors:
                            color_display = (0, 0, 0)  # Noir

                        cv2.putText(debug_image, color_text, (x, y + height + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color_display, 1)

            # Créer le répertoire parent si nécessaire
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # Sauvegarder l'image de débogage
            cv2.imwrite(output_path, debug_image)
            print(f"✅ Image de débogage sauvegardée dans {output_path}")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la génération de l'image de débogage: {e}")
            return False

def main():
    """Fonction principale pour l'exécution en ligne de commande

    Cette fonction permet d'utiliser le détecteur directement depuis la ligne de commande.
    Exemple d'utilisation:
        python detector.py image.jpg --config config.json --debug
    """
    # Analyser les arguments de la ligne de commande
    parser = argparse.ArgumentParser(
        description="Détecteur de cartes et de couleurs pour Poker Advisor",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("image_path",
                      help="Chemin vers l'image à analyser")
    parser.add_argument("--config",
                      help="Chemin vers le fichier de configuration",
                      default="config/poker_advisor_config.json")
    parser.add_argument("--output",
                      help="Chemin vers le fichier de sortie JSON",
                      default="detection_results.json")
    parser.add_argument("--debug",
                      action="store_true",
                      help="Générer une image de débogage")
    parser.add_argument("--debug-output",
                      help="Chemin vers l'image de débogage",
                      default="detection_debug.jpg")
    parser.add_argument("--use-cuda",
                      action="store_true",
                      help="Utiliser CUDA (GPU) si disponible")

    args = parser.parse_args()

    # Vérifier si l'image existe
    if not os.path.exists(args.image_path):
        print(f"❌ L'image spécifiée n'existe pas: {args.image_path}")
        return

    print(f"🔍 Analyse de l'image: {args.image_path}")
    print(f"📋 Configuration: {args.config}")

    # Créer le détecteur avec les options spécifiées
    try:
        detector = Detector(args.config, use_cuda=args.use_cuda)
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation du détecteur: {e}")
        return

    # Traiter l'image
    print("⏳ Traitement de l'image en cours...")
    results = detector.process_image(args.image_path)

    if not results:
        print("❌ Aucun résultat obtenu")
        return

    # Afficher les résultats
    print("\n=== Résultats de la détection ===")
    for name, data in results.items():
        print(f"Région: {name}")
        print(f"  Texte: {data['text'] or '(aucun texte détecté)'}")
        print(f"  Couleurs: {', '.join(data['colors']) or '(aucune couleur détectée)'}")

    # Sauvegarder les résultats
    if detector.save_results(results, args.output):
        print(f"💾 Résultats sauvegardés dans: {args.output}")

    # Générer l'image de débogage si demandé
    if args.debug:
        if detector.generate_debug_image(args.image_path, results, args.debug_output):
            print(f"🖼️ Image de débogage générée: {args.debug_output}")

    print("✅ Traitement terminé")

if __name__ == "__main__":
    main()
