#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration pour forcer CUDA sur TOUS les composants OCR.
"""

import os
import sys

def force_cuda_environment():
    """Force l'utilisation de CUDA pour tous les composants"""
    print("🔥 CONFIGURATION CUDA FORCÉE")
    print("=" * 50)
    
    # Variables d'environnement pour forcer CUDA
    cuda_env = {
        'CUDA_VISIBLE_DEVICES': '0',  # Utiliser le GPU 0
        'CUDA_DEVICE_ORDER': 'PCI_BUS_ID',
        'USE_CUDA': '1',
        'FORCE_CUDA': '1',
        'PADDLE_USE_GPU': '1',  # Forcer PaddleOCR sur GPU
        'EASYOCR_GPU': '1',     # Forcer EasyOCR sur GPU
        'TORCH_USE_CUDA_DSA': '1',
        'PYTORCH_CUDA_ALLOC_CONF': 'max_split_size_mb:512',  # Limiter les allocations
    }
    
    for key, value in cuda_env.items():
        os.environ[key] = value
        print(f"✅ {key} = {value}")
    
    print("\n🎯 VÉRIFICATION CUDA")
    print("=" * 30)
    
    # Vérifier PyTorch CUDA
    try:
        import torch
        print(f"✅ PyTorch CUDA disponible: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ GPU détecté: {torch.cuda.get_device_name(0)}")
            print(f"✅ Mémoire GPU: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    except Exception as e:
        print(f"⚠️ Erreur PyTorch: {e}")
    
    # Vérifier PaddlePaddle CUDA
    try:
        import paddle
        print(f"✅ PaddlePaddle CUDA: {paddle.device.is_compiled_with_cuda()}")
        if paddle.device.is_compiled_with_cuda():
            print(f"✅ PaddlePaddle GPU count: {paddle.device.cuda.device_count()}")
    except Exception as e:
        print(f"⚠️ PaddlePaddle non disponible ou pas de CUDA: {e}")
    
    print("\n🚀 Configuration CUDA appliquée !")
    return True

def create_cuda_detector_config():
    """Crée une configuration spéciale pour le détecteur avec CUDA forcé"""
    config = {
        "force_cuda": True,
        "cuda_device": 0,
        "memory_optimization": True,
        "cleanup_after_detection": True,
        "ocr_engines": {
            "paddleocr": {
                "use_gpu": True,
                "gpu_mem": 2000,  # 2GB pour PaddleOCR
                "enable_mkldnn": False,
                "use_tensorrt": False
            },
            "easyocr": {
                "gpu": True,
                "cudnn_benchmark": True
            }
        },
        "pytorch": {
            "cuda_memory_fraction": 0.7,  # Utiliser 70% de la VRAM
            "empty_cache_after_detection": True
        }
    }
    
    return config

def optimize_cuda_memory():
    """Optimise la mémoire CUDA pour éviter les fuites"""
    try:
        import torch
        if torch.cuda.is_available():
            # Vider le cache CUDA
            torch.cuda.empty_cache()
            
            # Configurer la gestion mémoire
            torch.cuda.set_per_process_memory_fraction(0.8)  # Utiliser max 80% de la VRAM
            
            # Activer la synchronisation pour éviter les fuites
            torch.cuda.synchronize()
            
            print("✅ Mémoire CUDA optimisée")
            return True
    except Exception as e:
        print(f"⚠️ Erreur optimisation CUDA: {e}")
        return False

def create_cuda_launcher():
    """Crée un lanceur spécialisé CUDA"""
    launcher_content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Lanceur CUDA optimisé pour l'application de détection.
"""

import os
import sys

# Forcer CUDA avant tout import
from force_cuda_config import force_cuda_environment, optimize_cuda_memory

def main():
    """Lance l'application avec CUDA forcé"""
    print("🔥 LANCEMENT AVEC CUDA FORCÉ")
    print("=" * 60)
    
    # Configurer CUDA
    force_cuda_environment()
    optimize_cuda_memory()
    
    print("\\n🚀 Lancement de l'application...")
    
    # Changer vers le répertoire des régions
    os.chdir("Détection des regions")
    
    # Lancer l'application
    import subprocess
    process = subprocess.Popen([sys.executable, "detector_gui.py"])
    
    try:
        process.wait()
        print("\\n✅ Application fermée proprement")
    except KeyboardInterrupt:
        print("\\n⚠️ Interruption par l'utilisateur")
        process.terminate()
        process.wait()
    
    print("\\n🧹 Nettoyage final CUDA...")
    optimize_cuda_memory()
    
    input("\\nAppuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
'''
    
    with open("lancer_cuda_force.py", "w", encoding="utf-8") as f:
        f.write(launcher_content)
    
    print("✅ Lanceur CUDA créé: lancer_cuda_force.py")

if __name__ == "__main__":
    force_cuda_environment()
    create_cuda_launcher()
    print("\n🎯 Configuration CUDA terminée !")
    print("Utilisez 'python lancer_cuda_force.py' pour lancer l'application")
