#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la nouvelle logique de détection du bouton dealer basée sur l'orange
"""

def test_nouvelle_logique_bouton():
    """Test de la nouvelle logique: pas d'orange = bouton présent"""
    print("🔍 TEST NOUVELLE LOGIQUE DÉTECTION BOUTON DEALER")
    print("=" * 60)
    print("RÈGLE: Si orange détecté = PAS de bouton (tapis visible)")
    print("       Si PAS d'orange = Bouton présent")
    print("=" * 60)
    
    # Simuler la méthode de détection
    def _is_dealer_button_detected_new(text, colors):
        """Nouvelle logique basée sur l'absence d'orange"""
        if colors and isinstance(colors, list):
            # Vérifier s'il y a de l'orange (tapis)
            has_orange = any(color.lower() in ['orange'] for color in colors)
            
            if has_orange:
                print(f"❌ Pas de bouton: orange détecté (tapis visible) dans {colors}")
                return False
            else:
                # Pas d'orange = bouton présent
                print(f"🔘 Bouton dealer détecté: PAS d'orange dans {colors}")
                return True

        # Si aucune couleur détectée, considérer comme pas de bouton
        print(f"❌ Pas de bouton: aucune couleur détectée")
        return False
    
    # Tests de cas
    test_cases = [
        {
            "nom": "Région avec bouton dealer",
            "colors": ["white", "black"],
            "attendu": True,
            "description": "Bouton présent (pas d'orange)"
        },
        {
            "nom": "Région sans bouton (tapis visible)",
            "colors": ["orange"],
            "attendu": False,
            "description": "Tapis orange visible = pas de bouton"
        },
        {
            "nom": "Région avec bouton + couleurs diverses",
            "colors": ["white", "black", "red"],
            "attendu": True,
            "description": "Bouton avec couleurs diverses (pas d'orange)"
        },
        {
            "nom": "Région tapis avec orange + autres couleurs",
            "colors": ["orange", "white", "black"],
            "attendu": False,
            "description": "Orange présent = pas de bouton même avec autres couleurs"
        },
        {
            "nom": "Région vide",
            "colors": [],
            "attendu": False,
            "description": "Aucune couleur = pas de bouton"
        },
        {
            "nom": "Région avec seulement blanc",
            "colors": ["white"],
            "attendu": True,
            "description": "Blanc seul = bouton (pas d'orange)"
        },
        {
            "nom": "Région avec seulement noir",
            "colors": ["black"],
            "attendu": True,
            "description": "Noir seul = bouton (pas d'orange)"
        },
        {
            "nom": "Région avec rouge et bleu",
            "colors": ["red", "blue"],
            "attendu": True,
            "description": "Autres couleurs = bouton (pas d'orange)"
        }
    ]
    
    print("🎯 TESTS DE CAS:")
    print("-" * 60)
    
    tests_reussis = 0
    tests_totaux = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['nom']}")
        print(f"   Couleurs: {test_case['colors']}")
        print(f"   Attendu: {'Bouton' if test_case['attendu'] else 'Pas de bouton'}")
        print(f"   Description: {test_case['description']}")
        
        # Exécuter le test
        resultat = _is_dealer_button_detected_new("", test_case['colors'])
        
        # Vérifier le résultat
        if resultat == test_case['attendu']:
            print(f"   ✅ SUCCÈS")
            tests_reussis += 1
        else:
            print(f"   ❌ ÉCHEC - Obtenu: {'Bouton' if resultat else 'Pas de bouton'}")
    
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS DES TESTS")
    print("=" * 60)
    print(f"Tests réussis: {tests_reussis}/{tests_totaux}")
    print(f"Taux de réussite: {(tests_reussis/tests_totaux)*100:.1f}%")
    
    if tests_reussis == tests_totaux:
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ La nouvelle logique fonctionne parfaitement")
    else:
        print("⚠️ Certains tests ont échoué")
        print("💡 Vérifiez la logique de détection")
    
    return tests_reussis == tests_totaux

def test_comparaison_anciennes_nouvelles_logiques():
    """Compare l'ancienne et la nouvelle logique"""
    print("\n🔄 COMPARAISON ANCIENNES VS NOUVELLES LOGIQUES")
    print("=" * 60)
    
    def _is_dealer_button_detected_old(text, colors):
        """Ancienne logique: noir ET blanc"""
        if colors and isinstance(colors, list):
            has_black = any(color.lower() in ['black', 'noir'] for color in colors)
            has_white = any(color.lower() in ['white', 'blanc'] for color in colors)
            return has_black and has_white
        return False
    
    def _is_dealer_button_detected_new(text, colors):
        """Nouvelle logique: pas d'orange"""
        if colors and isinstance(colors, list):
            has_orange = any(color.lower() in ['orange'] for color in colors)
            return not has_orange
        return False
    
    # Cas de test comparatifs
    cas_comparatifs = [
        {
            "colors": ["white", "black"],
            "description": "Bouton classique (blanc + noir)"
        },
        {
            "colors": ["orange"],
            "description": "Tapis orange visible"
        },
        {
            "colors": ["white"],
            "description": "Seulement blanc"
        },
        {
            "colors": ["black"],
            "description": "Seulement noir"
        },
        {
            "colors": ["red", "white"],
            "description": "Rouge + blanc"
        },
        {
            "colors": ["orange", "white", "black"],
            "description": "Orange + blanc + noir"
        }
    ]
    
    print("📊 COMPARAISON:")
    print("-" * 60)
    print(f"{'Couleurs':<25} {'Ancienne':<10} {'Nouvelle':<10} {'Différence'}")
    print("-" * 60)
    
    differences = 0
    
    for cas in cas_comparatifs:
        colors = cas['colors']
        old_result = _is_dealer_button_detected_old("", colors)
        new_result = _is_dealer_button_detected_new("", colors)
        
        old_str = "Bouton" if old_result else "Pas"
        new_str = "Bouton" if new_result else "Pas"
        diff_str = "✅ Même" if old_result == new_result else "🔄 Diff"
        
        if old_result != new_result:
            differences += 1
        
        colors_str = str(colors)[:20] + "..." if len(str(colors)) > 20 else str(colors)
        print(f"{colors_str:<25} {old_str:<10} {new_str:<10} {diff_str}")
    
    print("-" * 60)
    print(f"Différences trouvées: {differences}/{len(cas_comparatifs)}")
    
    print("\n💡 AVANTAGES DE LA NOUVELLE LOGIQUE:")
    print("✅ Plus simple: juste vérifier l'absence d'orange")
    print("✅ Plus fiable: orange = tapis visible = pas de bouton")
    print("✅ Moins de faux positifs: évite les confusions noir/blanc")
    print("✅ Plus robuste: fonctionne même avec couleurs partielles")

def main():
    """Fonction principale"""
    print("🚀 TEST COMPLET NOUVELLE LOGIQUE BOUTON DEALER")
    print("=" * 60)
    
    # Test 1: Nouvelle logique
    success1 = test_nouvelle_logique_bouton()
    
    # Test 2: Comparaison
    test_comparaison_anciennes_nouvelles_logiques()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DE L'IMPLÉMENTATION")
    print("=" * 60)
    
    if success1:
        print("✅ Nouvelle logique implémentée avec succès")
        print("🔧 Fichiers modifiés:")
        print("   - poker_advisor_light.py")
        print("   - poker_advisor_integration.py")
        print("\n🎯 NOUVELLE RÈGLE:")
        print("   Orange détecté = PAS de bouton (tapis visible)")
        print("   PAS d'orange = Bouton présent")
        print("\n💡 Testez maintenant avec votre application !")
        print("   Regardez les logs pour voir:")
        print("   🔘 Bouton dealer détecté: PAS d'orange dans [...]")
        print("   ❌ Pas de bouton: orange détecté (tapis visible) dans [...]")
    else:
        print("❌ Problème avec la nouvelle logique")
        print("💡 Vérifiez l'implémentation")

if __name__ == "__main__":
    main()
