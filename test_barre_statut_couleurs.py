#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de la barre de statut avec couleurs des cartes
==================================================

Ce script teste la nouvelle fonctionnalité d'affichage des cartes
avec leurs couleurs dans la barre de statut du détecteur.

Auteur: Augment Agent
Date: 2024
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QStatusBar, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt

class TestBarreStatutCouleurs(QMainWindow):
    """Fenêtre de test pour la barre de statut avec couleurs"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Barre de Statut - Couleurs des Cartes")
        self.setGeometry(100, 100, 900, 400)
        
        # Style sombre comme dans le détecteur
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
            QStatusBar {
                background-color: #1e1e1e;
                color: white;
                border-top: 1px solid #555;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                padding: 5px;
            }
            QLabel {
                color: white;
                font-family: 'Arial', sans-serif;
                font-size: 14px;
                padding: 10px;
            }
            QPushButton {
                background-color: #0066CC;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #0077DD;
            }
            QPushButton:pressed {
                background-color: #0055AA;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titre
        title_label = QLabel("🎨 Test de l'affichage des cartes avec couleurs dans la barre de statut")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Instructions
        instructions = QLabel("""
Cliquez sur les boutons ci-dessous pour tester différents scénarios d'affichage
des cartes dans la barre de statut. Les couleurs devraient être visibles :

• Cœur (♥) : Rouge
• Pique (♠) : Gris clair  
• Trèfle (♣) : Vert
• Carreau (♦) : Bleu

Regardez la barre de statut en bas de la fenêtre pour voir les résultats.
        """)
        layout.addWidget(instructions)
        
        # Boutons de test
        self.btn_test1 = QPushButton("Test 1: Main complète + Board complet")
        self.btn_test1.clicked.connect(self.test_main_board_complet)
        layout.addWidget(self.btn_test1)
        
        self.btn_test2 = QPushButton("Test 2: Main seulement")
        self.btn_test2.clicked.connect(self.test_main_seulement)
        layout.addWidget(self.btn_test2)
        
        self.btn_test3 = QPushButton("Test 3: Board seulement")
        self.btn_test3.clicked.connect(self.test_board_seulement)
        layout.addWidget(self.btn_test3)
        
        self.btn_test4 = QPushButton("Test 4: Aucune carte détectée")
        self.btn_test4.clicked.connect(self.test_aucune_carte)
        layout.addWidget(self.btn_test4)
        
        self.btn_test5 = QPushButton("Test 5: Toutes les couleurs")
        self.btn_test5.clicked.connect(self.test_toutes_couleurs)
        layout.addWidget(self.btn_test5)
        
        # Barre de statut
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Prêt pour les tests - Cliquez sur un bouton ci-dessus")
    
    def format_card_html(self, card):
        """Formate une carte avec sa couleur HTML"""
        if len(card) >= 2:
            suit = card[-1]
            if suit == "♥":  # Cœur - ROUGE
                return f'<span style="color: #FF4444;">{card}</span>'
            elif suit == "♠":  # Pique - GRIS CLAIR
                return f'<span style="color: #CCCCCC;">{card}</span>'
            elif suit == "♣":  # Trèfle - VERT
                return f'<span style="color: #44FF44;">{card}</span>'
            elif suit == "♦":  # Carreau - BLEU
                return f'<span style="color: #4444FF;">{card}</span>'
        return f'<span style="color: #FFFFFF;">{card}</span>'
    
    def update_status_with_cards(self, hand_cards, board_cards):
        """Met à jour la barre de statut avec les cartes formatées"""
        # Formater les cartes avec couleurs
        board_html = " ".join([self.format_card_html(card) for card in board_cards]) if board_cards else "Non détectées"
        hand_html = " ".join([self.format_card_html(card) for card in hand_cards]) if hand_cards else "Non détectées"
        
        # Créer le texte de statut final
        status_html = f"Board : {board_html} | Main : {hand_html}"
        
        # Mettre à jour la barre de statut
        self.status_bar.showMessage(status_html)
        
        print(f"🎨 Test: Board={len(board_cards) if board_cards else 0}, Main={len(hand_cards) if hand_cards else 0}")
        print(f"📝 HTML généré: {status_html}")
    
    def test_main_board_complet(self):
        """Test avec main complète et board complet"""
        hand_cards = ["A♥", "K♠"]
        board_cards = ["Q♣", "J♦", "10♥", "9♠", "8♣"]
        self.update_status_with_cards(hand_cards, board_cards)
    
    def test_main_seulement(self):
        """Test avec main seulement"""
        hand_cards = ["A♦", "K♣"]
        board_cards = []
        self.update_status_with_cards(hand_cards, board_cards)
    
    def test_board_seulement(self):
        """Test avec board seulement"""
        hand_cards = []
        board_cards = ["Q♥", "J♠", "10♦"]
        self.update_status_with_cards(hand_cards, board_cards)
    
    def test_aucune_carte(self):
        """Test sans aucune carte"""
        hand_cards = []
        board_cards = []
        self.update_status_with_cards(hand_cards, board_cards)
    
    def test_toutes_couleurs(self):
        """Test avec toutes les couleurs"""
        hand_cards = ["A♥", "K♠"]
        board_cards = ["Q♣", "J♦", "10♥"]
        self.update_status_with_cards(hand_cards, board_cards)

def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # Créer et afficher la fenêtre de test
    window = TestBarreStatutCouleurs()
    window.show()
    
    print("🎨 FENÊTRE DE TEST OUVERTE")
    print("=" * 50)
    print("Cliquez sur les boutons pour tester l'affichage des couleurs")
    print("dans la barre de statut en bas de la fenêtre.")
    print()
    print("Couleurs attendues :")
    print("• Cœur (♥) : Rouge")
    print("• Pique (♠) : Gris clair")
    print("• Trèfle (♣) : Vert")
    print("• Carreau (♦) : Bleu")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
