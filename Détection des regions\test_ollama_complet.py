#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test complet d'Ollama + LLaVA avec CUDA
======================================

Script pour tester toutes les régions sauf les cartes :
- Montants (pot_total, pot, jetons, mises) avec gestion décimales
- Pseudos (avec chiffres acceptés)
- Boutons dealer (blanc avec D)
- Statut all-in (rouge)

Usage:
    python test_ollama_complet.py
"""

import os
import sys
import cv2
import json
import time

# Ajouter le chemin pour importer le module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ollama_vision_detector_complet import OllamaVisionDetectorComplet
except ImportError as e:
    print(f"❌ Erreur import: {e}")
    print("💡 Assurez-vous que ollama_vision_detector_complet.py est dans le même dossier")
    sys.exit(1)

def test_toutes_regions():
    """Test complet de toutes les régions importantes"""
    print("=" * 80)
    print("🚀 TEST COMPLET OLLAMA + LLAVA AVEC CUDA")
    print("🎯 TOUTES RÉGIONS SAUF CARTES")
    print("=" * 80)
    
    try:
        # Initialiser le détecteur avec CUDA
        print("🔥 Initialisation avec CUDA...")
        detector = OllamaVisionDetectorComplet(use_cuda=True)
        
        # Chercher une capture d'écran récente
        screenshots_dir = "screenshots"
        if not os.path.exists(screenshots_dir):
            screenshots_dir = "../screenshots"
        
        if os.path.exists(screenshots_dir):
            screenshots = [f for f in os.listdir(screenshots_dir) if f.endswith('.jpg')]
            if screenshots:
                # Prendre la plus récente
                latest_screenshot = max(screenshots, key=lambda x: os.path.getctime(os.path.join(screenshots_dir, x)))
                screenshot_path = os.path.join(screenshots_dir, latest_screenshot)
                
                print(f"📸 Test avec: {screenshot_path}")
                
                # Charger l'image
                image = cv2.imread(screenshot_path)
                if image is None:
                    print(f"❌ Impossible de charger l'image: {screenshot_path}")
                    return
                
                print(f"📏 Taille image: {image.shape}")
                
                # Charger la configuration des régions
                config_path = os.path.join("..", "Calibration", "config", "poker_advisor_config.json")
                if not os.path.exists(config_path):
                    config_path = "calibration_config.json"
                
                if not os.path.exists(config_path):
                    print(f"❌ Fichier de configuration non trouvé: {config_path}")
                    return
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                roi_regions = config.get('roi', {})
                
                # Définir toutes les régions à tester (sauf cartes)
                regions_a_tester = {
                    # Montants
                    "pot_total": "montant",
                    "pot": "montant", 
                    "mes_jetons": "montant",
                    "mise_joueur1": "montant",
                    "mise_joueur2": "montant",
                    "mise_joueur3": "montant",
                    "mise_joueur4": "montant",
                    "mise_joueur5": "montant",
                    "mise_joueur6": "montant",
                    "jetons_joueur1": "montant",
                    "jetons_joueur2": "montant",
                    "jetons_joueur3": "montant",
                    "jetons_joueur4": "montant",
                    "jetons_joueur5": "montant",
                    "jetons_joueur6": "montant",
                    
                    # Pseudos
                    "pseudo_joueur1": "pseudo",
                    "pseudo_joueur2": "pseudo",
                    "pseudo_joueur3": "pseudo",
                    "pseudo_joueur4": "pseudo",
                    "pseudo_joueur5": "pseudo",
                    "pseudo_joueur6": "pseudo",
                    
                    # Boutons dealer
                    "bouton_joueur1": "bouton",
                    "bouton_joueur2": "bouton",
                    "bouton_joueur3": "bouton",
                    "bouton_joueur4": "bouton",
                    "bouton_joueur5": "bouton",
                    "bouton_joueur6": "bouton",
                }
                
                print(f"\n🔍 RÉGIONS À TESTER: {len(regions_a_tester)}")
                print("-" * 80)
                
                resultats = {}
                regions_testees = 0
                regions_reussies = 0
                
                # Tester chaque région
                for region_name, type_detection in regions_a_tester.items():
                    if region_name in roi_regions:
                        region_config = roi_regions[region_name]
                        
                        # Extraire les coordonnées
                        x = region_config['left']
                        y = region_config['top']
                        w = region_config['width']
                        h = region_config['height']
                        
                        # Vérifier que les coordonnées sont valides
                        img_h, img_w = image.shape[:2]
                        if x >= 0 and y >= 0 and x + w <= img_w and y + h <= img_h:
                            # Extraire la région
                            region_img = image[y:y+h, x:x+w]
                            
                            print(f"\n🔍 TEST: {region_name} ({type_detection}) - {x},{y} {w}x{h}")
                            
                            # Appliquer la détection selon le type
                            start_time = time.time()
                            
                            if type_detection == "montant":
                                result = detector.detect_amounts_enhanced(region_img, region_name)
                            elif type_detection == "pseudo":
                                result = detector.detect_player_names_enhanced(region_img, region_name)
                            elif type_detection == "bouton":
                                result = detector.detect_button_dealer(region_img, region_name)
                            else:
                                continue
                            
                            detection_time = time.time() - start_time
                            
                            # Afficher le résultat
                            if type_detection == "montant":
                                montant = result.get("montant")
                                confiance = result.get("confiance", 0)
                                couleur = result.get("couleur_detectee", "N/A")
                                print(f"   💰 Montant: {montant} (confiance: {confiance:.2f}, couleur: {couleur})")
                                
                            elif type_detection == "pseudo":
                                pseudo = result.get("pseudo")
                                confiance = result.get("confiance", 0)
                                chiffres = result.get("contient_chiffres", False)
                                print(f"   👤 Pseudo: {pseudo} (confiance: {confiance:.2f}, chiffres: {chiffres})")
                                
                            elif type_detection == "bouton":
                                bouton = result.get("bouton_detecte", False)
                                lettre_d = result.get("lettre_d_visible", False)
                                confiance = result.get("confiance", 0)
                                print(f"   🔘 Bouton: {bouton}, D visible: {lettre_d} (confiance: {confiance:.2f})")
                            
                            print(f"   ⏱️ Temps: {detection_time:.2f}s")
                            
                            # Sauvegarder la région pour debug
                            debug_path = f"debug_complet_{region_name}.jpg"
                            cv2.imwrite(debug_path, region_img)
                            
                            resultats[region_name] = result
                            regions_testees += 1
                            
                            if result.get("confiance", 0) > 0.5:
                                regions_reussies += 1
                        
                        else:
                            print(f"⚠️ Région {region_name} hors limites: {x},{y} {w}x{h}")
                    
                    else:
                        print(f"⚠️ Région {region_name} non trouvée dans la config")
                
                # Statistiques finales
                print("\n" + "=" * 80)
                print("📊 RÉSULTATS FINAUX")
                print("=" * 80)
                
                stats = detector.get_stats()
                taux_reussite = (regions_reussies / regions_testees * 100) if regions_testees > 0 else 0
                
                print(f"🔍 Régions testées: {regions_testees}")
                print(f"✅ Régions réussies: {regions_reussies}")
                print(f"📊 Taux de réussite: {taux_reussite:.1f}%")
                print(f"⏱️ Temps moyen: {stats['average_time']:.2f}s")
                print(f"🤖 Modèle utilisé: {stats['model_used']}")
                print(f"🔥 CUDA activé: {detector.use_cuda}")
                
                print(f"\n💾 Images de debug sauvées: debug_complet_*.jpg")
                
                # Résumé par type
                print(f"\n📋 RÉSUMÉ PAR TYPE:")
                types_count = {"montant": 0, "pseudo": 0, "bouton": 0}
                types_success = {"montant": 0, "pseudo": 0, "bouton": 0}
                
                for region_name, type_detection in regions_a_tester.items():
                    if region_name in resultats:
                        types_count[type_detection] += 1
                        if resultats[region_name].get("confiance", 0) > 0.5:
                            types_success[type_detection] += 1
                
                for type_name in types_count:
                    count = types_count[type_name]
                    success = types_success[type_name]
                    rate = (success / count * 100) if count > 0 else 0
                    print(f"   {type_name.upper()}: {success}/{count} ({rate:.1f}%)")
                
            else:
                print("❌ Aucune capture d'écran trouvée")
        else:
            print(f"❌ Dossier screenshots non trouvé: {screenshots_dir}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Fonction principale"""
    test_toutes_regions()
    input("\nAppuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
