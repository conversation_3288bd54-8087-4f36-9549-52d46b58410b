#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST RÉEL AVEC VOTRE IMAGE POKER
Analyse comparative des modèles de vision locaux sur votre capture d'écran
"""

import subprocess
import time
import json
import base64
import os
from typing import Dict, List
import requests

class TestImagePoker:
    def __init__(self):
        self.modeles_vision = [
            "llava:13b",  # Priorité au plus précis
            "llava:7b",
            "bakllava:latest",
            "moondream:latest"
        ]
        self.image_path = "capture_poker.png"  # Votre capture d'écran
        self.resultats = {}
        
    def verifier_ollama(self) -> bool:
        """Vérifie si Ollama est installé et fonctionnel"""
        try:
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
            
    def encoder_image_base64(self, image_path: str) -> str:
        """Encode l'image en base64 pour l'API"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except FileNotFoundError:
            print(f"❌ Image non trouvée: {image_path}")
            return None

    def creer_prompt_poker(self) -> str:
        """Crée le prompt d'analyse poker optimisé pour LLaVA"""
        return """🔍 LECTURE PRÉCISE OCR - TABLE DE POKER

⚠️ IMPORTANT : LIS EXACTEMENT CE QUI EST ÉCRIT, NE PAS INVENTER !

📖 **ÉTAPE 1 - LECTURE TEXTE OBLIGATOIRE** :
- Lis TOUS les noms de joueurs visibles (texte blanc/jaune)
- Lis TOUS les montants avec "BB" (chiffres jaunes/orange)
- Lis le montant du pot central (près des jetons)
- Lis TOUTES les cartes visibles (lettres + symboles sur les cartes)

🃏 **ÉTAPE 2 - CARTES COMMUNES (centre table)** :
- Compte les cartes au centre (1 à 5 cartes)
- Pour chaque carte : LIS la valeur (A,K,Q,J,10,9,8,7,6,5,4,3,2)
- Pour chaque carte : IDENTIFIE la couleur (♠♥♦♣ ou pique/cœur/carreau/trèfle)
- Format attendu : "6♠ 6♣ 8♥ 7♠ Q♥"

💰 **ÉTAPE 3 - MONTANTS EXACTS** :
- Pot central : "X,X BB" (lis le chiffre exact)
- Chaque joueur : "nom: X,X BB" (lis chaque montant)

👥 **ÉTAPE 4 - JOUEURS** :
- Position et nom exact de chaque joueur
- Statut visible (SUIT, ALL-IN, etc.)

🎯 **ÉTAPE 5 - ANALYSE POKER** :
- Phase de jeu (preflop/flop/turn/river)
- Type de board (paire, tirage, etc.)
- Mains fortes possibles

RÉPONDS EN FRANÇAIS - STRUCTURE AVEC DES EMOJIS"""

    def tester_modele_vision(self, modele: str) -> Dict:
        """Teste un modèle de vision avec l'image poker"""
        print(f"🧪 Test de {modele} avec l'image poker...")
        
        if not os.path.exists(self.image_path):
            return {
                "modele": modele,
                "succes": False,
                "erreur": f"Image {self.image_path} non trouvée",
                "duree": 0
            }
        
        try:
            start_time = time.time()
            
            # Encoder l'image
            image_b64 = self.encoder_image_base64(self.image_path)
            if not image_b64:
                return {
                    "modele": modele,
                    "succes": False,
                    "erreur": "Erreur encodage image",
                    "duree": 0
                }
            
            # Préparer la requête pour l'API Ollama
            url = "http://localhost:11434/api/generate"
            payload = {
                "model": modele,
                "prompt": self.creer_prompt_poker(),
                "images": [image_b64],
                "stream": False
            }
            
            # Envoyer la requête
            response = requests.post(url, json=payload, timeout=120)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                reponse_text = result.get("response", "")
                
                return {
                    "modele": modele,
                    "succes": True,
                    "reponse": reponse_text,
                    "duree": duration,
                    "longueur_reponse": len(reponse_text),
                    "vitesse": len(reponse_text) / duration if duration > 0 else 0
                }
            else:
                return {
                    "modele": modele,
                    "succes": False,
                    "erreur": f"HTTP {response.status_code}: {response.text}",
                    "duree": duration
                }
                
        except requests.exceptions.Timeout:
            return {
                "modele": modele,
                "succes": False,
                "erreur": "Timeout (>120s)",
                "duree": 120
            }
        except Exception as e:
            return {
                "modele": modele,
                "succes": False,
                "erreur": str(e),
                "duree": time.time() - start_time if 'start_time' in locals() else 0
            }

    def verifier_modeles_installes(self) -> List[str]:
        """Vérifie quels modèles de vision sont installés"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lignes = result.stdout.split('\n')[1:]  # Skip header
                modeles_installes = []
                for ligne in lignes:
                    if ligne.strip():
                        nom_modele = ligne.split()[0]
                        if nom_modele != "NAME":
                            modeles_installes.append(nom_modele)
                return modeles_installes
            return []
        except:
            return []

    def installer_modeles_manquants(self, modeles_installes: List[str]):
        """Propose d'installer les modèles de vision manquants"""
        modeles_manquants = [m for m in self.modeles_vision if m not in modeles_installes]
        
        if modeles_manquants:
            print(f"\n📥 Modèles de vision manquants: {', '.join(modeles_manquants)}")
            print("⚠️  Note: Les modèles de vision sont volumineux (4-7GB chacun)")
            response = input("Voulez-vous les installer ? (o/N): ")
            
            if response.lower() in ['o', 'oui']:
                for modele in modeles_manquants:
                    print(f"📦 Installation de {modele}... (peut prendre plusieurs minutes)")
                    try:
                        subprocess.run(['ollama', 'pull', modele], check=True)
                        print(f"✅ {modele} installé")
                    except:
                        print(f"❌ Erreur installation {modele}")

    def executer_tests(self) -> List[Dict]:
        """Exécute tous les tests d'analyse d'image"""
        print("🔍 VÉRIFICATION DES MODÈLES DE VISION")
        print("-" * 50)
        
        modeles_installes = self.verifier_modeles_installes()
        print(f"✅ Modèles installés: {', '.join(modeles_installes)}")
        
        # Installer les modèles manquants si souhaité
        self.installer_modeles_manquants(modeles_installes)
        
        # Re-vérifier après installation
        modeles_installes = self.verifier_modeles_installes()
        modeles_a_tester = [m for m in self.modeles_vision if m in modeles_installes]
        
        if not modeles_a_tester:
            print("❌ Aucun modèle de vision à tester disponible")
            print("💡 Installez au moins un modèle: ollama pull llava:7b")
            return []
        
        print(f"\n🧪 TESTS D'ANALYSE D'IMAGE POKER")
        print("-" * 50)
        print(f"Image à analyser: {self.image_path}")
        print(f"Modèles à tester: {', '.join(modeles_a_tester)}")
        print()
        
        resultats = []
        for modele in modeles_a_tester:
            resultat = self.tester_modele_vision(modele)
            resultats.append(resultat)
            
            if resultat["succes"]:
                print(f"✅ {modele}: {resultat['duree']:.1f}s, {resultat['longueur_reponse']} chars")
            else:
                print(f"❌ {modele}: {resultat['erreur']}")
        
        return resultats

    def generer_rapport(self, resultats: List[Dict]):
        """Génère un rapport comparatif des analyses d'image"""
        print("\n📊 RAPPORT COMPARATIF - ANALYSE IMAGE POKER")
        print("=" * 60)
        
        # Trier par succès puis par vitesse
        resultats_succes = [r for r in resultats if r["succes"]]
        resultats_succes.sort(key=lambda x: x["duree"])
        
        if not resultats_succes:
            print("❌ Aucun test réussi")
            return
        
        print("🏆 CLASSEMENT PAR VITESSE:")
        print("-" * 30)
        for i, r in enumerate(resultats_succes, 1):
            print(f"{i}. {r['modele']}")
            print(f"   ⏱️  Temps: {r['duree']:.1f}s")
            print(f"   📝 Réponse: {r['longueur_reponse']} caractères")
            print(f"   ⚡ Vitesse: {r['vitesse']:.0f} car/s")
            print()
        
        # Meilleur modèle
        meilleur = resultats_succes[0]
        print(f"🥇 MEILLEUR MODÈLE: {meilleur['modele']}")
        print(f"   Temps de réponse: {meilleur['duree']:.1f}s")
        print(f"   Qualité estimée: {'Excellente' if meilleur['longueur_reponse'] > 800 else 'Bonne'}")
        
        # Aperçu de la meilleure réponse
        print(f"\n📋 ANALYSE COMPLÈTE ({meilleur['modele']}):")
        print("-" * 50)
        print(meilleur['reponse'])

    def sauvegarder_resultats(self, resultats: List[Dict], filename: str = "test_image_poker_resultats.json"):
        """Sauvegarde les résultats"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(resultats, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Résultats sauvés dans {filename}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")

def main():
    """Fonction principale"""
    print("🧪 TEST ANALYSE IMAGE POKER - MODÈLES LOCAUX")
    print("Analyse de votre capture d'écran poker")
    print("=" * 60)
    
    # Vérifier qu'Ollama fonctionne
    try:
        subprocess.run(['ollama', '--version'], capture_output=True, check=True)
    except:
        print("❌ Ollama n'est pas installé ou ne fonctionne pas")
        print("💡 Installez Ollama depuis ollama.ai")
        return
    
    tester = TestImagePoker()
    
    print("🎯 Ce test va analyser votre capture d'écran poker avec des modèles de vision")
    print("📊 Critères évalués: vitesse, précision détection, qualité analyse")
    print()
    
    # Vérifier que l'image existe
    if not os.path.exists(tester.image_path):
        print(f"❌ Image {tester.image_path} non trouvée")
        print("💡 Placez votre capture d'écran poker dans le dossier courant")
        print("   et nommez-la 'capture_poker.png'")
        return
    
    # Exécuter les tests
    resultats = tester.executer_tests()
    
    if resultats:
        # Générer le rapport
        tester.generer_rapport(resultats)
        
        # Sauvegarder
        tester.sauvegarder_resultats(resultats)
        
        print("\n💡 RECOMMANDATIONS:")
        print("-" * 30)
        print("✅ Utilisez le modèle le plus rapide pour l'analyse temps réel")
        print("🎯 Comparez avec votre détection actuelle")
        print("🔄 Testez avec différentes captures pour valider")
    
    print("\n🎉 Test terminé !")

if __name__ == "__main__":
    main()
