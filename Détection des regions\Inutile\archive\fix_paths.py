#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger les chemins dans les fichiers Python
"""

import os
import re

def fix_paths_in_file(file_path):
    """Corrige les chemins dans un fichier Python"""
    print(f"Correction des chemins dans {file_path}...")
    
    # Lire le contenu du fichier
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remplacer les chemins problématiques
    old_path = "os.path.join('C:', 'Users', 'Tomz', 'PokerAdvisor', 'Détection des regions', 'config', 'poker_advisor_config - Raccourci')"
    new_path = "'C:\\\\Users\\\\<USER>\\\\PokerAdvisor\\\\Détection des regions\\\\config\\\\poker_advisor_config - Raccourci'"
    
    # Compter le nombre de remplacements
    count = content.count(old_path)
    
    # Effectuer le remplacement
    content = content.replace(old_path, new_path)
    
    # Écrire le contenu modifié dans le fichier
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {count} chemins corrigés dans {file_path}")
    return count

def main():
    """Fonction principale"""
    # Fichiers à corriger
    files = [
        "detector.py",
        "detector_gui.py"
    ]
    
    # Corriger les chemins dans chaque fichier
    total_count = 0
    for file in files:
        file_path = os.path.join(os.getcwd(), file)
        if os.path.exists(file_path):
            count = fix_paths_in_file(file_path)
            total_count += count
        else:
            print(f"❌ Le fichier {file_path} n'existe pas")
    
    print(f"\nTotal: {total_count} chemins corrigés")
    
    print("\nAppuyez sur Entrée pour quitter...")
    input()

if __name__ == "__main__":
    main()
