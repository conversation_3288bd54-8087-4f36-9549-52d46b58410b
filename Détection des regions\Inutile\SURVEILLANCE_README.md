# Système de Surveillance - Poker Advisor

## Vue d'ensemble

Le système de surveillance intégré surveille en temps réel les performances et la stabilité de l'application Poker Advisor. Il détecte automatiquement les problèmes de mémoire, les fuites GPU, et les processus qui consomment trop de ressources.

## Fonctionnalités

### 🔍 Surveillance en temps réel
- **CPU** : Utilisation du processeur système
- **RAM** : Mémoire système utilisée (MB et pourcentage)
- **GPU** : Mémoire GPU allouée et mise en cache
- **Processus Poker** : Nombre et consommation mémoire des processus liés à l'application

### 🚨 Système d'alertes
- **Mémoire système élevée** : > 20 GB (ajustable)
- **CPU système élevé** : > 80%
- **Mémoire GPU élevée** : > 5 GB
- **Processus poker** : > 1 GB
- **Détection de fuites mémoire** : Croissance anormale sur 10 mesures

### 📊 Historique et statistiques
- Conservation des 100 dernières mesures
- Calcul de moyennes sur les dernières mesures
- Détection de tendances et de fuites mémoire

## Activation

### Via le script de lancement (Recommandé)
```bash
lancer_detector_cuda_advisor.bat
```
La surveillance est automatiquement activée via `ENABLE_MONITORING=1`

### Via ligne de commande
```bash
python detector_gui.py --enable-monitoring --use-cuda --use-advisor
```

### Via variable d'environnement
```bash
set ENABLE_MONITORING=1
python detector_gui.py
```

## Interface utilisateur

Quand la surveillance est activée, un panneau "Surveillance du système" apparaît dans l'interface :

### Affichage en temps réel
- 🖥️ **CPU** : Pourcentage d'utilisation
- 💾 **RAM** : Mémoire utilisée (MB et %)
- 🎮 **GPU** : Mémoire GPU utilisée (MB)
- 🃏 **Processus Poker** : Nombre et mémoire (MB)
- ⏰ **Dernière mise à jour** : Timestamp

### Contrôles
- **Arrêter/Démarrer surveillance** : Bouton pour contrôler la surveillance
- **Résumé** : Affiche les statistiques moyennes
- **Couleurs** : Vert = normal, Rouge = alertes actives

## Fichiers de log

### Emplacement
```
C:\Users\<USER>\PokerAdvisor\Détection des regions\monitor.log
```

### Format des logs
```
[2025-05-27 22:00:05] 📊 CPU: 6.4% | RAM: 10062MB (30.8%) | GPU: 0MB | Poker: 1 proc, 935MB
[2025-05-27 22:00:05] 🚨 ALERTE: Mémoire système élevée: 10061.8 MB
```

### Consultation des logs
```bash
# Dernières 10 entrées
powershell "Get-Content 'C:\Users\<USER>\PokerAdvisor\Détection des regions\monitor.log' | Select-Object -Last 10"

# Surveillance en temps réel
tail -f monitor.log  # Linux/Mac
Get-Content monitor.log -Wait  # PowerShell
```

## Configuration avancée

### Ajustement des seuils (monitor_app.py)
```python
# Seuils d'alerte
self.memory_threshold_mb = 20000  # 20 GB
self.gpu_threshold_mb = 5000      # 5 GB  
self.cpu_threshold_percent = 80   # 80%
```

### Fréquence de surveillance
- **Mesures** : Toutes les 5 secondes
- **Affichage UI** : Toutes les 2 secondes
- **Nettoyage GPU** : Tous les 10 détections

## Test et diagnostic

### Script de test
```bash
python test_surveillance.py
```

### Vérification manuelle
```python
from monitor_app import AppMonitor
monitor = AppMonitor()
monitor.start_monitoring()
# ... attendre ...
print(monitor.get_summary())
monitor.stop_monitoring()
```

## Dépannage

### Surveillance non disponible
- Vérifier que `monitor_app.py` existe
- Vérifier l'installation de `psutil` : `pip install psutil`

### Pas de processus poker détectés
- Les processus sont détectés par mots-clés : `detector`, `poker`, `advisor`, etc.
- Vérifier que l'application est lancée avec un nom de script approprié

### Alertes constantes
- Ajuster les seuils dans `monitor_app.py`
- Vérifier la mémoire système disponible

## Intégration

Le système de surveillance est automatiquement intégré dans :
- ✅ `detector_gui.py` (interface principale)
- ✅ `lancer_detector_cuda_advisor.bat` (script de lancement)
- ✅ Interface utilisateur (panneau dédié)
- ✅ Gestion de fermeture propre

## Avantages

- **Détection précoce** des problèmes de performance
- **Surveillance automatique** sans intervention
- **Logs persistants** pour analyse post-mortem
- **Interface intégrée** dans l'application principale
- **Alertes visuelles** en temps réel
