#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module d'analyse de forme pour la détection du J
================================================

Ce module fournit des fonctions avancées pour détecter la lettre J
dans les images de cartes à jouer en utilisant l'analyse de forme.

Auteur: Augment Agent
Date: 2023-2025
"""

import cv2
import numpy as np
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("j_shape_analyzer_log.txt"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("JShapeAnalyzer")

def analyze_j_shape(image):
    """Analyse si l'image contient un J en se basant sur sa forme
    
    Args:
        image (numpy.ndarray): Image OpenCV (format BGR) à analyser
        
    Returns:
        bool: True si l'image contient probablement un J, False sinon
    """
    try:
        # Convertir en niveaux de gris
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # Essayer plusieurs méthodes de binarisation pour s'adapter à différentes couleurs de cartes
        binary_results = []
        
        # Méthode 1: Binarisation standard
        _, binary1 = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        binary_results.append(("standard", binary1))
        
        # Méthode 2: Binarisation adaptative
        binary2 = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                      cv2.THRESH_BINARY, 11, 2)
        binary_results.append(("adaptative", binary2))
        
        # Méthode 3: Binarisation inversée (pour les cartes sombres)
        _, binary3 = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
        binary_results.append(("inversée", binary3))
        
        # Méthode 4: Binarisation Otsu
        _, binary4 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        binary_results.append(("otsu", binary4))
        
        # Méthode 5: Amélioration du contraste puis binarisation
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        _, binary5 = cv2.threshold(enhanced, 127, 255, cv2.THRESH_BINARY)
        binary_results.append(("contraste", binary5))
        
        # Analyser chaque version binarisée
        for method_name, binary in binary_results:
            # Vérifier si l'image contient suffisamment de pixels blancs
            # pour être considérée comme une carte avec un J
            total_white_density = cv2.countNonZero(binary) / binary.size if binary.size > 0 else 0
            
            # Un J a généralement une densité de blanc modérée (ni trop élevée, ni trop faible)
            if total_white_density < 0.01 or total_white_density > 0.5:
                logger.info("Méthode %s: densité de blanc %.2f hors limites (0.01-0.5)", 
                           method_name, total_white_density)
                continue
            
            # Trouver les contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                logger.info("Méthode %s: aucun contour trouvé", method_name)
                continue
            
            # Trouver le plus grand contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Calculer le rectangle englobant
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Vérifier que le contour est suffisamment grand
            contour_area = cv2.contourArea(largest_contour)
            image_area = binary.shape[0] * binary.shape[1]
            area_ratio = contour_area / image_area if image_area > 0 else 0
            
            if area_ratio < 0.05:
                logger.info("Méthode %s: contour trop petit (%.2f%%)", method_name, area_ratio * 100)
                continue
            
            # Calculer le ratio hauteur/largeur (le J est généralement plus haut que large)
            aspect_ratio = h / w if w > 0 else 0
            
            # Un J a généralement un ratio hauteur/largeur élevé
            if aspect_ratio < 1.2:
                logger.info("Méthode %s: ratio h/l trop faible (%.2f)", method_name, aspect_ratio)
                continue
            
            # Diviser l'image en grille plus fine pour mieux analyser la forme du J
            # Division horizontale en 5 parties
            top = binary[y:y+h//5, x:x+w]                     # 0-20% (barre horizontale du J)
            upper_middle = binary[y+h//5:2*h//5, x:x+w]       # 20-40% (début de la tige)
            middle = binary[y+2*h//5:3*h//5, x:x+w]           # 40-60% (milieu de la tige)
            lower_middle = binary[y+3*h//5:4*h//5, x:x+w]     # 60-80% (fin de la tige)
            bottom = binary[y+4*h//5:y+h, x:x+w]              # 80-100% (courbure du J)
            
            # Division verticale en 4 parties
            left = binary[y:y+h, x:x+w//4]                    # 0-25% (gauche)
            left_middle = binary[y:y+h, x+w//4:x+w//2]        # 25-50% (centre-gauche)
            right_middle = binary[y:y+h, x+w//2:3*w//4]       # 50-75% (centre-droit)
            right = binary[y:y+h, x+3*w//4:x+w]               # 75-100% (droite)
            
            # Sections spécifiques pour le "demi-U"
            top_right = binary[y:y+h//5, x+3*w//4:x+w]        # Coin supérieur droit (où commence le J)
            bottom_left = binary[y+4*h//5:y+h, x:x+w//4]      # Coin inférieur gauche (où se termine le J)
            
            # Calculer les densités pour chaque section
            # Sections horizontales
            top_density = cv2.countNonZero(top) / (top.size) if top.size > 0 else 0
            upper_middle_density = cv2.countNonZero(upper_middle) / (upper_middle.size) if upper_middle.size > 0 else 0
            middle_density = cv2.countNonZero(middle) / (middle.size) if middle.size > 0 else 0
            lower_middle_density = cv2.countNonZero(lower_middle) / (lower_middle.size) if lower_middle.size > 0 else 0
            bottom_density = cv2.countNonZero(bottom) / (bottom.size) if bottom.size > 0 else 0
            
            # Sections verticales
            left_density = cv2.countNonZero(left) / (left.size) if left.size > 0 else 0
            left_middle_density = cv2.countNonZero(left_middle) / (left_middle.size) if left_middle.size > 0 else 0
            right_middle_density = cv2.countNonZero(right_middle) / (right_middle.size) if right_middle.size > 0 else 0
            right_density = cv2.countNonZero(right) / (right.size) if right.size > 0 else 0
            
            # Coins spécifiques
            top_right_density = cv2.countNonZero(top_right) / (top_right.size) if top_right.size > 0 else 0
            bottom_left_density = cv2.countNonZero(bottom_left) / (bottom_left.size) if bottom_left.size > 0 else 0
            
            logger.info("Méthode %s: densités verticales: haut=%.2f, haut-milieu=%.2f, milieu=%.2f, bas-milieu=%.2f, bas=%.2f",
                       method_name, top_density, upper_middle_density, middle_density, lower_middle_density, bottom_density)
            logger.info("Méthode %s: densités horizontales: gauche=%.2f, gauche-milieu=%.2f, droite-milieu=%.2f, droite=%.2f",
                       method_name, left_density, left_middle_density, right_middle_density, right_density)
            logger.info("Méthode %s: coins: haut-droite=%.2f, bas-gauche=%.2f",
                       method_name, top_right_density, bottom_left_density)
            
            # Caractéristiques d'un J comme "demi-U":
            # 1. Barre horizontale en haut
            has_top_bar = top_density > 0.1
            
            # 2. Tige verticale à droite
            has_right_stem = (right_density > 0.1 and 
                             right_density > left_density * 1.1)
            
            # 3. Courbure vers la gauche en bas
            has_bottom_curve = (bottom_left_density > 0.05 and
                               bottom_density < middle_density * 0.9)
            
            # 4. Coin supérieur droit avec pixels (début du J)
            has_top_right_corner = top_right_density > 0.1
            
            # 5. Asymétrie verticale (plus de pixels en haut et au milieu qu'en bas)
            has_vertical_asymmetry = (top_density + middle_density) > bottom_density * 1.2
            
            # 6. Asymétrie horizontale (plus de pixels à droite qu'à gauche dans la partie supérieure)
            has_horizontal_asymmetry = right_density > left_density
            
            # Vérifier que ce n'est pas un Q (qui est plus symétrique et circulaire)
            not_q = not (
                abs(left_density - right_density) < 0.1 and  # Symétrie horizontale
                abs(top_density - bottom_density) < 0.1 and  # Symétrie verticale
                middle_density > top_density and             # Plus dense au milieu (cercle du Q)
                middle_density > bottom_density
            )
            
            # Vérifier que ce n'est pas un 7 (qui a une barre horizontale en haut et une diagonale)
            not_7 = not (
                top_density > 0.2 and                        # Barre horizontale en haut
                right_middle_density > left_middle_density and # Diagonale de droite à gauche
                bottom_left_density < 0.05                    # Pas de pixels en bas à gauche
            )
            
            # Vérifier que ce n'est pas un 1 (qui est vertical et centré)
            not_1 = not (
                aspect_ratio > 2.0 and                       # Très haut et étroit
                abs(left_density - right_density) < 0.1 and  # Symétrie horizontale
                middle_density > 0.3                         # Ligne verticale au milieu
            )
            
            # Combiner toutes les caractéristiques pour déterminer si c'est un J
            is_j = (
                has_top_bar and
                has_right_stem and
                has_bottom_curve and
                has_top_right_corner and
                has_vertical_asymmetry and
                has_horizontal_asymmetry and
                not_q and
                not_7 and
                not_1
            )
            
            if is_j:
                logger.info("✅ J détecté avec la méthode %s", method_name)
                return True
            else:
                # Déterminer quelles caractéristiques manquent
                missing = []
                if not has_top_bar: missing.append("barre horizontale en haut")
                if not has_right_stem: missing.append("tige verticale à droite")
                if not has_bottom_curve: missing.append("courbure en bas")
                if not has_top_right_corner: missing.append("coin supérieur droit")
                if not has_vertical_asymmetry: missing.append("asymétrie verticale")
                if not has_horizontal_asymmetry: missing.append("asymétrie horizontale")
                if not not_q: missing.append("ressemble à un Q")
                if not not_7: missing.append("ressemble à un 7")
                if not not_1: missing.append("ressemble à un 1")
                
                logger.info("Méthode %s: pas un J, caractéristiques manquantes: %s", 
                           method_name, ", ".join(missing))
        
        # Si aucune méthode n'a détecté un J, essayer une approche plus permissive
        # basée uniquement sur le ratio hauteur/largeur et la présence d'une barre horizontale en haut
        for method_name, binary in binary_results:
            # Trouver les contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                continue
            
            # Trouver le plus grand contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Calculer le rectangle englobant
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Calculer le ratio hauteur/largeur
            aspect_ratio = h / w if w > 0 else 0
            
            # Diviser l'image en régions simples
            top_region = binary[y:y+h//4, x:x+w]
            right_region = binary[y:y+h, x+w//2:x+w]
            
            # Calculer les densités
            top_density = cv2.countNonZero(top_region) / (top_region.size) if top_region.size > 0 else 0
            right_density = cv2.countNonZero(right_region) / (right_region.size) if right_region.size > 0 else 0
            
            # Approche permissive: juste vérifier le ratio et la barre horizontale
            if aspect_ratio > 1.2 and top_density > 0.1 and right_density > 0.1:
                logger.info("✅ J détecté avec approche permissive (méthode %s)", method_name)
                return True
        
        logger.info("❌ Aucune méthode n'a détecté un J")
        return False
    except Exception as e:
        logger.error("Erreur lors de l'analyse de forme du J: %s", e)
        return False
