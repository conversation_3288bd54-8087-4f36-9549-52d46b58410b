#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correcteur pour les mises partiellement détectées
Applique les corrections identifiées par l'analyse
"""

import sys
import os
import json

def corriger_problemes_mises():
    """Corrige les problèmes identifiés dans la détection des mises"""
    print("🔧 CORRECTEUR - MISES PARTIELLEMENT DÉTECTÉES")
    print("=" * 60)
    
    print("🔍 PROBLÈMES IDENTIFIÉS :")
    print("1. ✅ mise_joueur1 : Détecte couleurs (7.6% jaune) mais O<PERSON> échoue")
    print("2. ❌ mise_joueur2-7 : Aucune couleur jaune/orange détectée")
    print("3. ⚠️ Contraste faible sur plusieurs régions")
    print("4. 📏 Régions 6 et 7 trop petites")
    print()
    
    # Correction 1 : Améliorer l'OCR pour mise_joueur1
    corriger_ocr_mise_joueur1()
    
    # Correction 2 : Recommandations pour la calibration
    recommander_recalibration()
    
    # Correction 3 : Améliorer les paramètres de détection
    ameliorer_parametres_detection()
    
    return True

def corriger_ocr_mise_joueur1():
    """Corrige spécifiquement l'OCR pour mise_joueur1"""
    print("🎯 CORRECTION 1 - OCR MISE_JOUEUR1")
    print("-" * 50)
    
    print("📊 Analyse :")
    print("   ✅ Couleurs détectées : orange, yellow(7.6%)")
    print("   ✅ Zones de texte : 2 zones détectées")
    print("   ❌ OCR : Détecte seulement 'BB' (pas de chiffres)")
    print()
    
    print("🔧 Solutions à appliquer :")
    print("1. **Améliorer le prétraitement OCR :**")
    print("   - Augmenter le contraste avant OCR")
    print("   - Utiliser plusieurs méthodes de binarisation")
    print("   - Appliquer un débruitage")
    print()
    
    print("2. **Ajuster la région :**")
    print("   - Vérifier que la région capture bien les chiffres")
    print("   - Peut-être légèrement déplacer vers la gauche")
    print("   - Tester avec une région légèrement plus grande")
    print()
    
    print("3. **Paramètres OCR spécialisés :**")
    print("   - Utiliser des paramètres optimisés pour texte jaune")
    print("   - Tester avec différents modes OCR")
    print("   - Appliquer un masque couleur avant OCR")

def recommander_recalibration():
    """Recommande la recalibration des régions problématiques"""
    print("\n🎯 CORRECTION 2 - RECALIBRATION RÉGIONS")
    print("-" * 50)
    
    regions_problematiques = [
        {
            "region": "mise_joueur2",
            "probleme": "Très sombre (29.9), contraste faible (2.3), aucune couleur",
            "solution": "Repositionner sur une mise visible ou vérifier qu'il y a une mise"
        },
        {
            "region": "mise_joueur3", 
            "probleme": "Aucune zone de texte, aucune couleur",
            "solution": "Complètement repositionner - région mal placée"
        },
        {
            "region": "mise_joueur4",
            "probleme": "Contraste faible (2.2), aucune couleur",
            "solution": "Repositionner sur une mise visible"
        },
        {
            "region": "mise_joueur5",
            "probleme": "Bon contraste mais aucune couleur jaune",
            "solution": "Vérifier s'il y a une mise à cette position"
        },
        {
            "region": "mise_joueur6",
            "probleme": "Trop petite (49x49), contraste faible",
            "solution": "Agrandir la région ET repositionner"
        },
        {
            "region": "mise_joueur7",
            "probleme": "Trop petite (56x51), contraste faible", 
            "solution": "Agrandir la région ET repositionner"
        }
    ]
    
    print("📋 RÉGIONS À RECALIBRER :")
    for region_info in regions_problematiques:
        print(f"\n   🎯 {region_info['region'].upper()}")
        print(f"      ❌ Problème: {region_info['probleme']}")
        print(f"      ✅ Solution: {region_info['solution']}")

def ameliorer_parametres_detection():
    """Améliore les paramètres de détection"""
    print("\n🎯 CORRECTION 3 - PARAMÈTRES DE DÉTECTION")
    print("-" * 50)
    
    print("🔧 Améliorations à appliquer :")
    print()
    
    print("1. **Seuils de couleurs :**")
    print("   - Seuils actuels : 2% pour orange/jaune")
    print("   - ✅ Seuils corrects (mise_joueur1 détecte 7.6%)")
    print("   - Problème = Pas de couleurs dans les autres régions")
    print()
    
    print("2. **Prétraitement OCR amélioré :**")
    print("   - Augmenter le contraste pour texte jaune")
    print("   - Appliquer un masque couleur avant OCR")
    print("   - Utiliser plusieurs méthodes de binarisation")
    print()
    
    print("3. **Tailles de régions minimales :**")
    print("   - Largeur minimale : 100 pixels")
    print("   - Hauteur minimale : 30 pixels")
    print("   - Régions 6 et 7 à agrandir")

def creer_guide_recalibration():
    """Crée un guide de recalibration"""
    print("\n📋 GUIDE DE RECALIBRATION")
    print("=" * 60)
    
    print("🎯 ÉTAPES DE RECALIBRATION :")
    print()
    
    print("1. **Ouvrir l'outil de calibration :**")
    print("   cd 'C:\\Users\\<USER>\\PokerAdvisor\\Calibration'")
    print("   python calibration_simple.py")
    print()
    
    print("2. **Identifier les mises visibles :**")
    print("   - Assurez-vous qu'il y a des mises jaunes/oranges sur la table")
    print("   - Notez les positions où vous voyez des montants")
    print()
    
    print("3. **Recalibrer les régions problématiques :**")
    print("   - mise_joueur2 : Repositionner sur mise visible")
    print("   - mise_joueur3 : Complètement repositionner")
    print("   - mise_joueur4 : Repositionner sur mise visible")
    print("   - mise_joueur5 : Vérifier position")
    print("   - mise_joueur6 : Agrandir ET repositionner")
    print("   - mise_joueur7 : Agrandir ET repositionner")
    print()
    
    print("4. **Tester après recalibration :**")
    print("   python diagnostic_mises_jaunes.py")
    print("   python examiner_images_debug.py")
    print()
    
    print("5. **Critères de succès :**")
    print("   - Au moins 3-4 régions détectent des couleurs jaunes/oranges")
    print("   - OCR détecte des chiffres (pas seulement 'BB')")
    print("   - Contraste > 20 pour toutes les régions")

def creer_script_test_rapide():
    """Crée un script de test rapide"""
    print("\n🚀 CRÉATION SCRIPT DE TEST RAPIDE")
    print("-" * 50)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide des mises après recalibration
"""

import subprocess
import sys

def test_rapide_mises():
    """Test rapide des mises"""
    print("🧪 TEST RAPIDE - MISES APRÈS RECALIBRATION")
    print("=" * 50)
    
    try:
        # Test diagnostic
        print("1. Lancement diagnostic...")
        result = subprocess.run([sys.executable, "diagnostic_mises_jaunes.py"], 
                              capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            output = result.stdout
            
            # Compter les mises détectées
            mises_detectees = output.count("✅ MISE DÉTECTÉE:")
            mises_totales = 7
            
            print(f"📊 Résultat: {mises_detectees}/{mises_totales} mises détectées")
            
            if mises_detectees >= 3:
                print("✅ SUCCÈS - Amélioration significative!")
            elif mises_detectees >= 1:
                print("⚠️ PROGRÈS - Quelques mises détectées")
            else:
                print("❌ ÉCHEC - Aucune mise détectée")
            
            return mises_detectees >= 1
        else:
            print("❌ Erreur lors du test")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    test_rapide_mises()
'''
    
    try:
        with open("test_rapide_mises.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("✅ Script créé: test_rapide_mises.py")
    except Exception as e:
        print(f"❌ Erreur création script: {e}")

def main():
    """Fonction principale"""
    print("🔧 CORRECTEUR MISES PARTIELLES")
    print("=" * 70)
    print("Application des corrections pour les mises partiellement détectées")
    print()
    
    # Appliquer les corrections
    success = corriger_problemes_mises()
    
    # Créer le guide de recalibration
    creer_guide_recalibration()
    
    # Créer le script de test rapide
    creer_script_test_rapide()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES CORRECTIONS")
    print("-" * 70)
    
    if success:
        print("✅ Analyse des problèmes terminée")
        print("📋 Guide de recalibration créé")
        print("🧪 Script de test rapide créé")
        
        print("\n🚀 PROCHAINES ÉTAPES :")
        print("1. Examinez resume_debug_mises.jpg")
        print("2. Suivez le guide de recalibration")
        print("3. Recalibrez les régions problématiques")
        print("4. Testez avec: python test_rapide_mises.py")
        
        print("\n🎯 OBJECTIF :")
        print("   Passer de 0/7 à au moins 3-4/7 mises détectées")
    else:
        print("❌ Erreur lors des corrections")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
