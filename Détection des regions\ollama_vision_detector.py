#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Poker Advisor - Module de détection Ollama + LLaVA
==================================================

Module indépendant utilisant Ollama + LLaVA pour détecter uniquement les montants et pseudos
dans les images de poker. Test 100% gratuit et local.

Fonctionnalités :
1. Détection pure des montants (jetons, mises, pot)
2. Détection pure des pseudos de joueurs
3. Interface simple pour tester les performances
4. Optimisé pour RTX 3060 Ti + 32GB RAM

Auteur: Augment Agent
Date: 2025
"""

import os
import sys
import cv2
import numpy as np
import time
import json
import logging
from typing import Dict, List, Optional, Tuple
import requests
import base64
from io import BytesIO
from PIL import Image

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OllamaVisionDetector:
    """Détecteur de montants et pseudos utilisant Ollama + LLaVA"""
    
    def __init__(self, ollama_url: str = "http://localhost:11434", model: str = "llava:7b", use_cuda: bool = True):
        """
        Initialise le détecteur Ollama

        Args:
            ollama_url (str): URL du serveur Ollama local
            model (str): Modèle LLaVA à utiliser
            use_cuda (bool): Utiliser CUDA pour l'accélération GPU
        """
        self.ollama_url = ollama_url
        self.model = model
        self.use_cuda = use_cuda
        self.session = requests.Session()

        # Configuration CUDA pour Ollama
        if self.use_cuda:
            logger.info("🔥 Mode CUDA activé pour Ollama")
        
        # Vérifier la connexion Ollama
        self._check_ollama_connection()
        
        # Statistiques
        self.stats = {
            "total_detections": 0,
            "successful_detections": 0,
            "failed_detections": 0,
            "average_time": 0.0
        }
        
        logger.info(f"✅ OllamaVisionDetector initialisé avec {model}")
    
    def _check_ollama_connection(self):
        """Vérifie que Ollama est accessible"""
        try:
            response = self.session.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                available_models = [m["name"] for m in models]
                logger.info(f"🔗 Connexion Ollama OK. Modèles disponibles: {available_models}")
                
                if self.model not in available_models:
                    logger.warning(f"⚠️ Modèle {self.model} non trouvé. Modèles disponibles: {available_models}")
                    if available_models:
                        self.model = available_models[0]
                        logger.info(f"🔄 Utilisation du modèle: {self.model}")
            else:
                raise Exception(f"Erreur HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ Impossible de se connecter à Ollama: {e}")
            logger.error("💡 Assurez-vous qu'Ollama est installé et lancé avec: ollama serve")
            raise
    
    def _image_to_base64(self, image: np.ndarray) -> str:
        """Convertit une image OpenCV en base64"""
        try:
            # Convertir BGR vers RGB
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image
            
            # Convertir en PIL Image
            pil_image = Image.fromarray(image_rgb)
            
            # Convertir en base64
            buffer = BytesIO()
            pil_image.save(buffer, format="PNG")
            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return img_base64
            
        except Exception as e:
            logger.error(f"❌ Erreur conversion image vers base64: {e}")
            raise
    
    def detect_amounts_only(self, image: np.ndarray, region_name: str = "unknown") -> Dict:
        """
        Détecte UNIQUEMENT les montants dans une image
        
        Args:
            image (np.ndarray): Image OpenCV à analyser
            region_name (str): Nom de la région pour le debug
            
        Returns:
            Dict: Résultats de détection des montants
        """
        start_time = time.time()
        
        try:
            # Convertir l'image en base64
            img_base64 = self._image_to_base64(image)
            
            # Prompt spécialisé pour les montants de poker avec gestion des couleurs
            color_hint = ""
            if "pot_total" in region_name.lower():
                color_hint = "ATTENTION: Le pot total est affiché en BLANC sur fond sombre."
            elif "pot" in region_name.lower():
                color_hint = "ATTENTION: Le pot peut être affiché en JAUNE/ORANGE."
            elif "mise" in region_name.lower() or "jetons" in region_name.lower():
                color_hint = "ATTENTION: Les montants peuvent être en JAUNE/ORANGE ou BLANC."

            prompt = f"""Tu es un expert en reconnaissance de montants dans les jeux de poker en ligne.

ANALYSE CETTE IMAGE et détecte UNIQUEMENT les montants/chiffres visibles.

{color_hint}

RÈGLES STRICTES:
1. Cherche les chiffres qui représentent des montants (jetons, mises, pot)
2. Les montants peuvent être: 0.5, 1.2, 42.8, 100, 1.5K, 2.3M, etc.
3. ATTENTION AUX DÉCIMALES: "7" pourrait être "0.7" ou "0,7"
4. Si tu vois un seul chiffre comme "7", vérifie s'il y a un point ou virgule avant
5. Les montants de poker sont souvent avec décimales: 0.5, 1.2, 2.8, etc.
6. Ignore tout texte qui n'est pas un montant
7. Sois très précis sur les chiffres ET les décimales

RÉPONSE ATTENDUE (format JSON):
{{
    "montant_detecte": "montant exact trouvé ou null",
    "confiance": "score de 0 à 1",
    "format": "type de format détecté (decimal, K, M, etc.)",
    "couleur_detectee": "couleur du texte (blanc, jaune, orange, etc.)",
    "position": "description de où se trouve le montant"
}}

EXEMPLES:
Si tu vois "0.7" → {{"montant_detecte": "0.7", "confiance": 0.95, "format": "decimal", "couleur_detectee": "blanc", "position": "centre"}}
Si tu vois "7" mais c'est probablement "0.7" → {{"montant_detecte": "0.7", "confiance": 0.80, "format": "decimal_probable", "couleur_detectee": "blanc", "position": "centre"}}
Si tu vois "1.5K" → {{"montant_detecte": "1.5K", "confiance": 0.90, "format": "K", "couleur_detectee": "jaune", "position": "coin supérieur"}}
Si aucun montant → {{"montant_detecte": null, "confiance": 0.0, "format": null, "couleur_detectee": null, "position": null}}"""

            # Appel à Ollama
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [img_base64],
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Très peu de créativité pour plus de précision
                    "top_p": 0.9
                }
            }
            
            response = self.session.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")
                
                # Parser la réponse JSON
                detection_result = self._parse_amount_response(response_text, region_name)
                
                # Calculer le temps
                detection_time = time.time() - start_time
                detection_result["detection_time"] = detection_time
                detection_result["region_name"] = region_name
                
                # Mettre à jour les stats
                self._update_stats(detection_time, True)
                
                logger.info(f"💰 MONTANT DÉTECTÉ [{region_name}]: {detection_result}")
                return detection_result
                
            else:
                logger.error(f"❌ Erreur Ollama: {response.status_code} - {response.text}")
                self._update_stats(time.time() - start_time, False)
                return self._empty_amount_result(region_name)
                
        except Exception as e:
            logger.error(f"❌ Erreur détection montant: {e}")
            self._update_stats(time.time() - start_time, False)
            return self._empty_amount_result(region_name)
    
    def detect_player_names_only(self, image: np.ndarray, region_name: str = "unknown") -> Dict:
        """
        Détecte UNIQUEMENT les pseudos de joueurs dans une image
        
        Args:
            image (np.ndarray): Image OpenCV à analyser
            region_name (str): Nom de la région pour le debug
            
        Returns:
            Dict: Résultats de détection des pseudos
        """
        start_time = time.time()
        
        try:
            # Convertir l'image en base64
            img_base64 = self._image_to_base64(image)
            
            # Prompt spécialisé pour les pseudos de poker avec gestion des chiffres
            prompt = """Tu es un expert en reconnaissance de pseudos de joueurs dans les jeux de poker en ligne.

ANALYSE CETTE IMAGE et détecte UNIQUEMENT les pseudos/noms de joueurs visibles.

RÈGLES STRICTES:
1. Cherche les noms d'utilisateurs/pseudos de joueurs
2. Les pseudos peuvent contenir: lettres, chiffres, tirets, underscores, points
3. Longueur typique: 3-20 caractères
4. ACCEPTER LES PSEUDOS AVEC CHIFFRES: Player123, TomZ666, User42, etc.
5. Les pseudos peuvent commencer par une lettre OU un chiffre
6. Exemples valides: "Player1", "123User", "Tom-666", "User_42", "ABC123"
7. Ignore les montants purs (comme "0.7", "100", "1.5K")
8. Ignore les cartes (comme "A", "K", "Q", "J")
9. Ignore les boutons d'action (FOLD, CALL, RAISE)
10. Sois très précis sur le texte détecté

RÉPONSE ATTENDUE (format JSON):
{{
    "pseudo_detecte": "pseudo exact trouvé ou null",
    "confiance": "score de 0 à 1",
    "longueur": "nombre de caractères",
    "contient_chiffres": "true/false",
    "position": "description de où se trouve le pseudo"
}}

EXEMPLES:
Si tu vois "Player123" → {{"pseudo_detecte": "Player123", "confiance": 0.95, "longueur": 9, "contient_chiffres": true, "position": "centre"}}
Si tu vois "TomZ-666" → {{"pseudo_detecte": "TomZ-666", "confiance": 0.90, "longueur": 8, "contient_chiffres": true, "position": "coin gauche"}}
Si tu vois "User42" → {{"pseudo_detecte": "User42", "confiance": 0.90, "longueur": 6, "contient_chiffres": true, "position": "centre"}}
Si tu vois "ABC" → {{"pseudo_detecte": "ABC", "confiance": 0.85, "longueur": 3, "contient_chiffres": false, "position": "centre"}}
Si tu vois juste "7" ou "0.7" → {{"pseudo_detecte": null, "confiance": 0.0, "longueur": 0, "contient_chiffres": false, "position": null}}
Si aucun pseudo → {{"pseudo_detecte": null, "confiance": 0.0, "longueur": 0, "contient_chiffres": false, "position": null}}"""

            # Appel à Ollama
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [img_base64],
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Très peu de créativité pour plus de précision
                    "top_p": 0.9
                }
            }
            
            response = self.session.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get("response", "")
                
                # Parser la réponse JSON
                detection_result = self._parse_player_response(response_text, region_name)
                
                # Calculer le temps
                detection_time = time.time() - start_time
                detection_result["detection_time"] = detection_time
                detection_result["region_name"] = region_name
                
                # Mettre à jour les stats
                self._update_stats(detection_time, True)
                
                logger.info(f"👤 PSEUDO DÉTECTÉ [{region_name}]: {detection_result}")
                return detection_result
                
            else:
                logger.error(f"❌ Erreur Ollama: {response.status_code} - {response.text}")
                self._update_stats(time.time() - start_time, False)
                return self._empty_player_result(region_name)
                
        except Exception as e:
            logger.error(f"❌ Erreur détection pseudo: {e}")
            self._update_stats(time.time() - start_time, False)
            return self._empty_player_result(region_name)
    
    def _parse_amount_response(self, response_text: str, region_name: str) -> Dict:
        """Parse la réponse Ollama pour les montants"""
        try:
            # Chercher le JSON dans la réponse
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # Valider les champs requis
                if "montant_detecte" in result:
                    return {
                        "montant": result.get("montant_detecte"),
                        "confiance": float(result.get("confiance", 0.0)),
                        "format": result.get("format"),
                        "couleur_detectee": result.get("couleur_detectee"),
                        "position": result.get("position"),
                        "methode": "Ollama+LLaVA",
                        "raw_response": response_text
                    }
            
            # Si pas de JSON valide, essayer d'extraire le montant directement
            logger.warning(f"⚠️ Réponse non-JSON pour {region_name}: {response_text}")
            return self._extract_amount_fallback(response_text)
            
        except Exception as e:
            logger.error(f"❌ Erreur parsing montant: {e}")
            return self._extract_amount_fallback(response_text)
    
    def _parse_player_response(self, response_text: str, region_name: str) -> Dict:
        """Parse la réponse Ollama pour les pseudos"""
        try:
            # Chercher le JSON dans la réponse
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # Valider les champs requis
                if "pseudo_detecte" in result:
                    return {
                        "pseudo": result.get("pseudo_detecte"),
                        "confiance": float(result.get("confiance", 0.0)),
                        "longueur": int(result.get("longueur", 0)),
                        "contient_chiffres": result.get("contient_chiffres", False),
                        "position": result.get("position"),
                        "methode": "Ollama+LLaVA",
                        "raw_response": response_text
                    }
            
            # Si pas de JSON valide, essayer d'extraire le pseudo directement
            logger.warning(f"⚠️ Réponse non-JSON pour {region_name}: {response_text}")
            return self._extract_player_fallback(response_text)
            
        except Exception as e:
            logger.error(f"❌ Erreur parsing pseudo: {e}")
            return self._extract_player_fallback(response_text)

    def _extract_amount_fallback(self, text: str) -> Dict:
        """Extraction de fallback pour les montants"""
        import re

        # Chercher des patterns de montants
        patterns = [
            r'(\d+\.?\d*[KM]?)',  # 42.8, 1.5K, 2M
            r'(\d+,\d+)',         # 1,234
            r'(\d+\.\d+)',        # 42.8
            r'(\d+)',             # 100
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                return {
                    "montant": matches[0],
                    "confiance": 0.5,  # Confiance réduite pour fallback
                    "format": "fallback",
                    "position": "unknown",
                    "methode": "Ollama+LLaVA (fallback)",
                    "raw_response": text
                }

        return self._empty_amount_result("fallback")

    def _extract_player_fallback(self, text: str) -> Dict:
        """Extraction de fallback pour les pseudos"""
        import re

        # Chercher des patterns de pseudos
        patterns = [
            r'([A-Za-z][A-Za-z0-9_-]{2,19})',  # Pseudo commençant par lettre
            r'([A-Za-z0-9_-]{3,20})',          # Pseudo général
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                pseudo = matches[0]
                return {
                    "pseudo": pseudo,
                    "confiance": 0.5,  # Confiance réduite pour fallback
                    "longueur": len(pseudo),
                    "position": "unknown",
                    "methode": "Ollama+LLaVA (fallback)",
                    "raw_response": text
                }

        return self._empty_player_result("fallback")

    def _empty_amount_result(self, region_name: str) -> Dict:
        """Résultat vide pour montant"""
        return {
            "montant": None,
            "confiance": 0.0,
            "format": None,
            "position": None,
            "methode": "Ollama+LLaVA",
            "region_name": region_name,
            "detection_time": 0.0,
            "raw_response": ""
        }

    def _empty_player_result(self, region_name: str) -> Dict:
        """Résultat vide pour pseudo"""
        return {
            "pseudo": None,
            "confiance": 0.0,
            "longueur": 0,
            "position": None,
            "methode": "Ollama+LLaVA",
            "region_name": region_name,
            "detection_time": 0.0,
            "raw_response": ""
        }

    def _update_stats(self, detection_time: float, success: bool):
        """Met à jour les statistiques"""
        self.stats["total_detections"] += 1
        if success:
            self.stats["successful_detections"] += 1
        else:
            self.stats["failed_detections"] += 1

        # Moyenne mobile du temps de détection
        total = self.stats["total_detections"]
        self.stats["average_time"] = (
            (self.stats["average_time"] * (total - 1) + detection_time) / total
        )

    def get_stats(self) -> Dict:
        """Retourne les statistiques de performance"""
        success_rate = 0.0
        if self.stats["total_detections"] > 0:
            success_rate = self.stats["successful_detections"] / self.stats["total_detections"]

        return {
            **self.stats,
            "success_rate": success_rate,
            "model_used": self.model
        }

    def test_with_screenshot(self, screenshot_path: str) -> Dict:
        """
        Test complet avec une capture d'écran

        Args:
            screenshot_path (str): Chemin vers la capture d'écran

        Returns:
            Dict: Résultats de tous les tests
        """
        try:
            # Charger l'image
            image = cv2.imread(screenshot_path)
            if image is None:
                raise Exception(f"Impossible de charger l'image: {screenshot_path}")

            logger.info(f"🖼️ Test avec image: {screenshot_path}")
            logger.info(f"📏 Taille image: {image.shape}")

            # Charger la vraie configuration comme calibration_simple.py
            config_path = os.path.join("..", "Calibration", "config", "poker_advisor_config.json")
            if not os.path.exists(config_path):
                config_path = "calibration_config.json"  # Fallback

            test_regions = {}

            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    # Utiliser les régions ROI comme calibration_simple.py
                    roi_regions = config.get('roi', {})

                    # Sélectionner les régions importantes pour les montants et pseudos
                    regions_to_test = [
                        "pot_total",
                        "mes_jetons",
                        "pseudo_joueur1",
                        "pseudo_joueur2",
                        "pseudo_joueur3",
                        "mise_joueur1",
                        "mise_joueur2",
                        "mise_joueur3"
                    ]

                    for region_name in regions_to_test:
                        if region_name in roi_regions:
                            region_config = roi_regions[region_name]

                            # Format ROI: left, top, width, height
                            x = region_config['left']
                            y = region_config['top']
                            w = region_config['width']
                            h = region_config['height']

                            # Vérifier que les coordonnées sont dans l'image
                            img_h, img_w = image.shape[:2]
                            if x >= 0 and y >= 0 and x + w <= img_w and y + h <= img_h:
                                # Extraire la région de l'image
                                region_img = image[y:y+h, x:x+w]
                                test_regions[region_name] = region_img

                                logger.info(f"✅ Région {region_name} extraite: {x},{y} {w}x{h}")
                            else:
                                logger.warning(f"⚠️ Région {region_name} hors limites: {x},{y} {w}x{h} (image: {img_w}x{img_h})")
                        else:
                            logger.warning(f"⚠️ Région {region_name} non trouvée dans roi")

                    logger.info(f"📊 {len(test_regions)} régions extraites sur {len(regions_to_test)} demandées")

                except Exception as e:
                    logger.error(f"❌ Erreur lecture config: {e}")

            # Si pas de config ou erreur, utiliser des régions par défaut
            if not test_regions:
                logger.warning("⚠️ Utilisation de régions par défaut")
                h, w = image.shape[:2]
                test_regions = {
                    "pot_total": image[int(h*0.1):int(h*0.3), int(w*0.4):int(w*0.6)],
                    "mes_jetons": image[int(h*0.7):int(h*0.9), int(w*0.1):int(w*0.4)],
                    "pseudo_joueur1": image[int(h*0.2):int(h*0.4), int(w*0.1):int(w*0.3)],
                    "mise_joueur1": image[int(h*0.4):int(h*0.6), int(w*0.1):int(w*0.3)],
                }

            results = {}

            # Tester la détection de montants
            for region_name, region_img in test_regions.items():
                if "pseudo" in region_name:
                    # Test pseudo
                    result = self.detect_player_names_only(region_img, region_name)
                else:
                    # Test montant
                    result = self.detect_amounts_only(region_img, region_name)

                results[region_name] = result

                # Sauvegarder la région pour debug
                debug_path = f"debug_ollama_{region_name}.jpg"
                cv2.imwrite(debug_path, region_img)
                logger.info(f"💾 Région sauvée: {debug_path}")

            # Statistiques globales
            results["global_stats"] = self.get_stats()

            return results

        except Exception as e:
            logger.error(f"❌ Erreur test screenshot: {e}")
            return {"error": str(e)}


def main():
    """Fonction principale pour tester le détecteur Ollama"""
    print("=" * 60)
    print("🚀 TEST OLLAMA + LLAVA - DÉTECTION MONTANTS & PSEUDOS")
    print("=" * 60)

    try:
        # Initialiser le détecteur
        detector = OllamaVisionDetector()

        # Chercher une capture d'écran récente pour tester
        screenshots_dir = "screenshots"
        if os.path.exists(screenshots_dir):
            screenshots = [f for f in os.listdir(screenshots_dir) if f.endswith('.jpg')]
            if screenshots:
                # Prendre la plus récente
                latest_screenshot = max(screenshots, key=lambda x: os.path.getctime(os.path.join(screenshots_dir, x)))
                screenshot_path = os.path.join(screenshots_dir, latest_screenshot)

                print(f"📸 Test avec: {screenshot_path}")

                # Lancer le test
                results = detector.test_with_screenshot(screenshot_path)

                # Afficher les résultats
                print("\n" + "=" * 60)
                print("📊 RÉSULTATS DU TEST")
                print("=" * 60)

                for region_name, result in results.items():
                    if region_name == "global_stats":
                        continue

                    print(f"\n🔍 RÉGION: {region_name}")
                    print("-" * 40)

                    if "montant" in result:
                        montant = result.get("montant")
                        confiance = result.get("confiance", 0)
                        temps = result.get("detection_time", 0)
                        print(f"💰 Montant: {montant}")
                        print(f"🎯 Confiance: {confiance:.2f}")
                        print(f"⏱️ Temps: {temps:.2f}s")

                    elif "pseudo" in result:
                        pseudo = result.get("pseudo")
                        confiance = result.get("confiance", 0)
                        temps = result.get("detection_time", 0)
                        print(f"👤 Pseudo: {pseudo}")
                        print(f"🎯 Confiance: {confiance:.2f}")
                        print(f"⏱️ Temps: {temps:.2f}s")

                # Statistiques globales
                if "global_stats" in results:
                    stats = results["global_stats"]
                    print(f"\n📈 STATISTIQUES GLOBALES")
                    print("-" * 40)
                    print(f"✅ Détections réussies: {stats['successful_detections']}")
                    print(f"❌ Détections échouées: {stats['failed_detections']}")
                    print(f"📊 Taux de succès: {stats['success_rate']:.1%}")
                    print(f"⏱️ Temps moyen: {stats['average_time']:.2f}s")
                    print(f"🤖 Modèle utilisé: {stats['model_used']}")

            else:
                print("❌ Aucune capture d'écran trouvée dans le dossier screenshots/")
        else:
            print("❌ Dossier screenshots/ non trouvé")

    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("\n💡 INSTRUCTIONS D'INSTALLATION:")
        print("1. Installer Ollama: https://ollama.ai/")
        print("2. Lancer: ollama serve")
        print("3. Télécharger LLaVA: ollama pull llava")
        print("4. Relancer ce script")


if __name__ == "__main__":
    main()
